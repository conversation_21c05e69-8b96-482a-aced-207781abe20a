package com.espc.sec.dataexport.kafka.service;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.List;
import java.util.Properties;

/**
 * Kafka消费者服务
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class KafkaConsumerService {
    private KafkaConsumer<String, Object> consumer;
    private volatile boolean subscribed = false;

    @PostConstruct
    public void init() {
        initConsumer();
        log.info(LogKeyword.KAFKA_EXPORT + "Kafka消费者服务初始化完成");
    }

    private void initConsumer() {
        try {
            Properties props = new Properties();
            props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, Config.environment.kafka.getBootstrapServers());

            props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 1000);
            props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);
            props.put(ConsumerConfig.GROUP_ID_CONFIG, "bigdata-export-group-0721");
            props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
            props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, 1000);
            props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);
            props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");

            this.consumer = new KafkaConsumer<>(props);
            log.info(LogKeyword.KAFKA_EXPORT + "Kafka消费者初始化成功，连接到: {}", Config.environment.kafka.getBootstrapServers());
        } catch (Exception e) {
            log.error(LogKeyword.KAFKA_EXPORT + "初始化Kafka消费者失败", e);
            throw new RuntimeException("Failed to initialize Kafka consumer", e);
        }
    }

    /**
     * 批量消费消息
     *
     * @return 消息列表
     */
    public synchronized ConsumerRecords<String, Object> consumeMessages(List<String> topicList) {
        try {
            // 只在第一次或连接断开时subscribe，避免重复订阅导致offset重置
            if (!subscribed) {
                consumer.subscribe(topicList);
                subscribed = true;
                log.info(LogKeyword.KAFKA_EXPORT + "订阅Kafka主题: {}", topicList);
            }
            ConsumerRecords<String, Object> records = consumer.poll(Duration.ofMillis(10000L));
            log.info(LogKeyword.KAFKA_EXPORT + "批量消费消息完成: 获取到 {} 条消息", records.count());
            return records;
        } catch (Exception e) {
            log.error(LogKeyword.KAFKA_EXPORT + "批量消费消息异常", e);
            // 重置订阅状态，下次重新订阅
            subscribed = false;
            return null;
        }
    }

    /**
     * 批量消费消息（带条数限制，用于参数化导出）
     *
     * @param topicList  主题列表
     * @param maxRecords 最大记录数
     * @return 消息列表
     */
    public synchronized ConsumerRecords<String, Object> consumeMessagesWithLimit(List<String> topicList, int maxRecords) {
        try {
            log.info(LogKeyword.KAFKA_EXPORT + "准备消费Kafka消息（限制条数），主题列表: {}, 最大条数: {}", topicList, maxRecords);

            // 创建临时消费者，使用不同的配置
            Properties props = new Properties();
            props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, Config.environment.kafka.getBootstrapServers());
            props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, Math.min(maxRecords, 10000)); // 使用传入的条数限制
            props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);
            props.put(ConsumerConfig.GROUP_ID_CONFIG, "bigdata-export-group-params-" + System.currentTimeMillis()); // 使用唯一的组ID
            props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
            props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, 1000);
            props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);
            props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringDeserializer");

            KafkaConsumer<String, Object> tempConsumer = new KafkaConsumer<>(props);

            try {
                // 检查主题是否存在
                for (String topic : topicList) {
                    try {
                        var partitions = tempConsumer.partitionsFor(topic);
                        if (partitions == null || partitions.isEmpty()) {
                            log.warn(LogKeyword.KAFKA_EXPORT + "主题 {} 不存在或没有分区", topic);
                        } else {
                            log.info(LogKeyword.KAFKA_EXPORT + "主题 {} 存在，分区数: {}", topic, partitions.size());
                        }
                    } catch (Exception e) {
                        log.error(LogKeyword.KAFKA_EXPORT + "检查主题 {} 时发生错误: {}", topic, e.getMessage());
                    }
                }

                log.info(LogKeyword.KAFKA_EXPORT + "开始订阅Kafka主题: {}", topicList);
                tempConsumer.subscribe(topicList);
                log.info(LogKeyword.KAFKA_EXPORT + "订阅Kafka主题完成: {}", topicList);

                log.info(LogKeyword.KAFKA_EXPORT + "开始轮询消息，超时时间: 10秒，最大条数: {}", maxRecords);
                ConsumerRecords<String, Object> records = tempConsumer.poll(Duration.ofMillis(10000L));
                log.info(LogKeyword.KAFKA_EXPORT + "批量消费消息完成: 获取到 {} 条消息", records.count());

                // 如果获取到消息，打印详细信息
                if (records.count() > 0) {
                    int count = 0;
                    for (ConsumerRecord<String, Object> record : records) {
                        if (count < 5) { // 只打印前5条消息的详细信息
                            log.info(LogKeyword.KAFKA_EXPORT + "消费到消息: topic={}, partition={}, offset={}, key={}, value={}",
                                    record.topic(), record.partition(), record.offset(),
                                    record.key(), record.value());
                        }
                        count++;
                        if (count >= maxRecords) {
                            log.info(LogKeyword.KAFKA_EXPORT + "已达到最大条数限制: {}", maxRecords);
                            break;
                        }
                    }
                } else {
                    log.info(LogKeyword.KAFKA_EXPORT + "本次轮询未获取到任何消息");
                }

                return records;

            } finally {
                // 关闭临时消费者
                tempConsumer.close();
            }

        } catch (Exception e) {
            log.error(LogKeyword.KAFKA_EXPORT + "批量消费消息异常", e);
            return null;
        }
    }

}