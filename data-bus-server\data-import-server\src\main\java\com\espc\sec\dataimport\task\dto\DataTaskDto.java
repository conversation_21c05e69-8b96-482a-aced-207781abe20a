package com.espc.sec.dataimport.task.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 数据任务DTO
 */
@Data
public class DataTaskDto {

    /**
     * 任务ID
     */
    private Integer id;

    /**
     * job名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String name;

    /**
     * 任务类型 1-立即执行，2-定时执行
     */
    @NotBlank(message = "任务类型不能为空")
    private Integer type;

    /**
     * 如果type=1则为空，格式：2025-07-15 14:30:30
     */
    private String cron;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空")
    private String businessType;

    /**
     * 任务配置信息
     */
    @NotBlank(message = "任务配置不能为空")
    private Object businessConfig;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 状态 1-待执行，2-执行中，3-执行成功，4-执行失败
     */
    private Integer status;

    /**
     * 节点
     */
    private String node;

    /**
     * 预留字段
     */
    private String extraFiled;
}
