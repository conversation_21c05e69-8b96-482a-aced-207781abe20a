package com.espc.sec.dataexport.common.service.impl;

import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.dto.Tricycle;
import com.espc.sec.dataexport.common.dto.Tuple;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import com.espc.sec.dataexport.common.enums.config.ConfigModuleEnum;
import com.espc.sec.dataexport.common.enums.config.ConfigTtypeEnum;
import com.espc.sec.dataexport.common.service.CheckPointService;
import com.espc.sec.dataexport.common.util.DateUtil;
import com.espc.sec.dataexport.config.service.ConfigService;
import com.espc.sec.dataexport.config.vo.ConfigVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * es,startRocks检查点实现类
 *
 * <AUTHOR>
 * @date 2025/7/26
 */
@Service
public class CheckPointServiceImpl implements CheckPointService {
    @Autowired
    private ConfigService configService;

    @Override
    public Tuple<Integer, Date> getLastTime(ExportTypeEnum exportTypeEnum, String tableName) {
        ConfigVo config = configService.getConfig(ConfigModuleEnum.CHECK_POINT
                , ConfigTtypeEnum.getByCode(exportTypeEnum.getCode())
                , tableName);
        if (config == null) {
            return null;
        }
        return new Tuple<>(config.getId(), DateUtil.parse(config.getTValue(), Constants.YYYY_MM_DD_HH_MM_SS));
    }

    @Override
    public void insert(ExportTypeEnum exportTypeEnum, String tableName, Date date) {
          configService.save(ConfigModuleEnum.CHECK_POINT
                , ConfigTtypeEnum.getByCode(exportTypeEnum.getCode())
                , tableName
                , DateUtil.format(date, Constants.YYYY_MM_DD_HH_MM_SS));
    }


    @Override
    public void update(Integer id, Date date) {
        configService.update(id, DateUtil.format(date, Constants.YYYY_MM_DD_HH_MM_SS));
    }

    @Override
    public Tricycle<Integer, Date, Date> getTimeRangeForIndex(ExportTypeEnum exportTypeEnum, String indexName) {
        Tuple<Integer, Date> idAndLastTime = getLastTime(exportTypeEnum, indexName);
        if (idAndLastTime == null) {
            // 首次导出，从指定天数前开始
            Date startTime = DateUtil.offsetDay(new Date(), -7);
            Date endTime = DateUtil.offsetMinute(startTime, 60);

            insert(exportTypeEnum, indexName, endTime);
            Integer id = getLastTime(exportTypeEnum, indexName).getX();
            return new Tricycle<>(id, startTime, endTime);
        }

        Date startTime = idAndLastTime.getY();
        // 往前推10分钟，避免数据同步完成数据未入库情况
        if (startTime.after(DateUtil.offsetMinute(new Date(), -10))) {
            return null;
        }
        // 每次同步五分钟数据
        Date endTime = DateUtil.offsetMinute(startTime, 5);

        return new Tricycle<>(idAndLastTime.getX(), startTime, endTime);
    }
}
