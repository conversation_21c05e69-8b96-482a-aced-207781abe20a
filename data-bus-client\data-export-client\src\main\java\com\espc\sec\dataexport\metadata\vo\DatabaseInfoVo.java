package com.espc.sec.dataexport.metadata.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据库信息VO
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatabaseInfoVo {
    /**
     * 数据库名称
     */
    private String databaseName;
    
    /**
     * 表数量
     */
    private Integer tableCount;
    
    /**
     * 字符集
     */
    private String charset;
    
    /**
     * 描述
     */
    private String description;
}