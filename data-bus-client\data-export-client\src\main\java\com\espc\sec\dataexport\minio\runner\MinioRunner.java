package com.espc.sec.dataexport.minio.runner;

import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.minio.helper.MinIOServiceHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.espc.sec.dataexport.common.constant.Constants.ARN_MINIO_SQS_MINIO_KAFKA;

/**
 * 启动添加topic监听
 */
@Component
@Slf4j
public class MinioRunner implements ApplicationRunner {

    @Override
    public void run(ApplicationArguments args) {
        MinIOServiceHelper.loadMinIOConnect();
        Config.exportConfig.minio.getBuckets().forEach(bucket -> {
            try {
                String bucketName = bucket.getPrefix();
                if ("d".equals(bucket.getMode())) {
                    bucketName = bucketName + "-" + DateUtil.format(new Date(), "yyyyMMdd");
                }
                MinIOServiceHelper.configureKafkaNotification(bucketName, ARN_MINIO_SQS_MINIO_KAFKA);
            } catch (Exception e) {
                log.error(LogKeyword.MINIO_EXPORT + "minio桶监听失败", e);
            }
        });

    }
}
