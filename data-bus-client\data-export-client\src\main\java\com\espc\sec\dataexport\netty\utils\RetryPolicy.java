package com.espc.sec.dataexport.netty.utils;

import lombok.Data;

/**
 * 重试策略类，用于定义重试的最大次数、初始延迟和最大延迟。
 */
@Data
public class RetryPolicy {
    private final int maxRetries;
    private final long initialDelay;
    private final long maxDelay;

    public RetryPolicy(int maxRetries, long initialDelay, long maxDelay) {
        this.maxRetries = maxRetries;
        this.initialDelay = initialDelay;
        this.maxDelay = maxDelay;
    }

    /**
     * 判断是否应该重试。
     *
     * @param retryCount 当前重试次数
     * @return 如果重试次数小于最大重试次数，返回 true；否则返回 false
     */
    public boolean shouldRetry(int retryCount) {
        return retryCount < maxRetries;
    }

    /**
     * 获取下一次重试的延迟时间。
     *
     * @param retryCount 当前重试次数
     * @return 下一次重试的延迟时间
     */
    public long getNextDelay(int retryCount) {
        long delay = initialDelay * (long) Math.pow(2, retryCount - 1);
        return Math.min(delay, maxDelay);
    }
}
