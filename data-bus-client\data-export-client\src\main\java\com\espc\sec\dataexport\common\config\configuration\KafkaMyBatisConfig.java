package com.espc.sec.dataexport.common.config.configuration;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;


/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
@MapperScan(
        basePackages = {
                "com.espc.sec.dataexport.monitor.increment.mapper",
                "com.espc.sec.dataexport.monitor.task.mapper",
                "com.espc.sec.dataexport.task.mapper",
                "com.espc.sec.dataexport.config.mapper"
        },
        sqlSessionFactoryRef = "mybatisSqlSessionFactory"
)
public class KafkaMyBatisConfig {

    @Bean("mybatisSqlSessionFactory")
    @Primary  // 设置为主要的SqlSessionFactory
    public SqlSessionFactory mybatisSqlSessionFactory(
            @Qualifier("mysqlDataSource") DataSource dataSource
    ) throws Exception {
        MybatisSqlSessionFactoryBean sessionFactory = new MybatisSqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);

        // MyBatis 配置
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setLogImpl(org.apache.ibatis.logging.stdout.StdOutImpl.class);
        sessionFactory.setConfiguration(configuration);

        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath:mapper/*.xml"));
        
        log.info("初始化主要的MyBatis SqlSessionFactory (MySQL数据源)");
        return sessionFactory.getObject();
    }


    @Bean("mybatisSqlSessionTemplate")
    @Primary  // 设置为主要的SqlSessionTemplate
    public SqlSessionTemplate kafkaSqlSessionTemplate(
            @Qualifier("mybatisSqlSessionFactory") SqlSessionFactory sqlSessionFactory
    ) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}