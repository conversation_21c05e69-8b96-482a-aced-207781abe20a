package com.espc.sec.dataexport.common.dto.task;

import lombok.Data;

import java.util.List;

/**
 * StarRocks导出请求参数
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class StarRocksTaskProperties extends BaseTaskProperties {

    private List<DatabaseConfig> database;

    /**
     * 数据库配置
     */
    @Data
    public static class DatabaseConfig {

        private List<String> tableName;


        private String dataStartTime;
        private String dataEndTime;

    }


}