{"elasticsearch": {"fixedDelayMills": 60000, "dataNodeEnable": true, "dataNodeTableEnable": true}, "starRocks": {"fixedDelayMills": 60000, "dataNodeEnable": true, "dataNodeTableEnable": true}, "tidb": {"fixedDelayMills": 60000, "dataNodeEnable": true, "dataNodeTableEnable": true}, "kafka": {"fixedDelayMills": 60000, "dataNodeEnable": true, "dataNodeTableEnable": true}, "minio": {"fixedDelayMills": 120000, "uploadBucketPrefix": "pr-inf-", "dataNodeEnable": true, "dataNodeTableEnable": true}, "hdfs": {"fixedDelayMills": 120000, "hdfsUploadPath": "/data1", "dataNodeEnable": true}, "scanFileLimit": {"elasticsearch": 10, "starrocks": 10, "tidb": 10, "kafka": 10, "minio": 100, "hdfs": 5}}