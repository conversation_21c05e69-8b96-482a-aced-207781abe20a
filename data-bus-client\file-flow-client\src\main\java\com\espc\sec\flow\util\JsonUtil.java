package com.espc.sec.flow.util;


import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.fasterxml.jackson.databind.DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT;

/**
 * <AUTHOR>
 * @date 2017-07-26
 */
@Slf4j
public class JsonUtil {
    /**
     * json格式转换工具
     */
    public static ObjectMapper objectMapper;

    static {
        JsonFactory jsonFactory = new JsonFactory();
        jsonFactory.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        objectMapper = new ObjectMapper(jsonFactory);
        // 忽略未知属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        objectMapper.configure(ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
    }

    /**
     * 把json格式的字符串转换成map
     *
     * @param string json字符串
     * @return 返回map
     * @throws Exception 抛出异常
     */
    public static Map stringToMap(String string) throws Exception {
        return objectMapper.readValue(string, Map.class);
    }

    /**
     * 把对象转换成json格式
     *
     * @param object 对象
     * @return 返回json
     */
    public static String objectToJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            log.error("error when Object to Json String", e);
        }
        return null;
    }

    /**
     * 把json转换成对象
     *
     * @param json  json字符串
     * @param clazz 对象的类型
     * @return 返回对象
     * @throws Exception 抛出异常
     */
    public static Object jsonToObject(String json, Class clazz) throws Exception {
        return JsonUtil.objectMapper.readValue(json, clazz);
    }
}
