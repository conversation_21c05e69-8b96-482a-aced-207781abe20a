package com.espc.sec.flow.util;

import Ice.Communicator;
import Ice.ObjectPrx;
import com.common.ice.IceDirectClient;
import com.ice.common.CommonServicePrx;
import com.ice.common.CommonServicePrxHelper;
import com.ice.datapool.DatapoolServicePrx;
import com.ice.datapool.DatapoolServicePrxHelper;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-06-04
 */
@Slf4j
public class IceUtil {

    /**
     * 服务端代理列表
     */
    private static List<CommonServicePrx> commonServicePrxes = new ArrayList<>();


    /**
     * ice配置初始化
     *
     * @return
     */
    public static boolean iceInit() {
        Communicator communicator = getCommunicator();
        if (communicator == null) {
            return false;
        }
        IceDirectClient.setCommunicator(communicator);
        return true;
    }

    /**
     * 得到FileFlowService的代理
     *
     * @param ip   连接ip
     * @param port 连接端口
     * @return 返回代理
     */
    public static CommonServicePrx getFlowServerProxy(String ip, int port) {
        String endPoint = "FileFlowService:default -h " + ip + " -p " + port;
        try {
            ObjectPrx objectPrx = IceDirectClient.getClient(endPoint);
            CommonServicePrx targetPrx = CommonServicePrxHelper.checkedCast(objectPrx);
            if (targetPrx == null) {
                log.error("getFlowServerProxy failed. endPoint:" + endPoint);
                return null;
            }
            return targetPrx;
        } catch (Exception e) {
            log.error("getFlowServerProxy failed. endPoint:" + endPoint);
            return null;
        }
    }

    /**
     * 得到DatapoolService的代理
     *
     * @param ip   连接ip
     * @param port 连接端口
     * @return 返回代理
     */
    public static DatapoolServicePrx getDatapoolServiceProxy(String ip, int port) {
        String endPoint = "DatapoolService:ssl -p " + port + " -h " + ip;
        try {
            ObjectPrx objectPrx = IceDirectClient.getClient(endPoint);
            DatapoolServicePrx targetPrx = DatapoolServicePrxHelper.checkedCast(objectPrx);
            if (targetPrx == null) {
                log.error("getDatapoolServiceProxy failed. endPoint:" + endPoint);
                return null;
            }
            return targetPrx;
        } catch (Throwable e) {
            log.error("getDatapoolServiceProxy failed. endPoint:" + endPoint, e);
            return null;
        }
    }


    /**
     * 获得代理列表
     *
     * @return 返回代理列表
     */
    public static List<CommonServicePrx> getCommonServicePrxes() {
        return commonServicePrxes;
    }

    /**
     * 关闭代理连接
     *
     * @param communicator 代理连接
     */
    public static void destroyCommunicator(Communicator communicator) {
        if (null != communicator) {
            try {
                communicator.shutdown();
                communicator.destroy();
            } catch (Exception e) {
                log.error("error when destroy communicator", e);
            }
        }
    }

    /**
     * 记录代理连接的信息
     *
     * @param communicator 代理连接
     */
    public static void log(Communicator communicator) {
        log.debug(communicator.getProperties().getPropertiesForPrefix("Ice").toString());
    }

    /**
     * 得到代理连接
     *
     * @return 返回代理连接
     */
    public static synchronized Communicator getCommunicator() {
        Communicator communicator = null;
        try {
            String[] args = new String[]{"--Ice.Config=conf/FileFlowClient/conf/ice.config"};
            communicator = Ice.Util.initialize(args);
            log.debug(communicator.getProperties().getPropertiesForPrefix("Ice").toString());
        } catch (Exception e) {
            destroyCommunicator(communicator);
            log.error("error when getCommunicator", e);
        }
        return communicator;
    }
}