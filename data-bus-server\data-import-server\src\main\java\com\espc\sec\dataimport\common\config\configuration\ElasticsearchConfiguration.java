package com.espc.sec.dataimport.common.config.configuration;

import com.espc.sec.dataimport.elasticsearch.config.EsClientHolder;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkProcessor;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.common.unit.ByteSizeUnit;
import org.elasticsearch.common.unit.ByteSizeValue;
import org.elasticsearch.common.unit.TimeValue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PreDestroy;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
@Configuration
public class ElasticsearchConfiguration {
    private BulkProcessor bulkProcessor;

    @Bean
    public BulkProcessor initBulkProcessor() {
        BulkProcessor.Builder builder = BulkProcessor.builder(
                (request, bulkListener) -> EsClientHolder.normalRestClient.bulkAsync(request, RequestOptions.DEFAULT, bulkListener),
                new BulkProcessor.Listener() {
                    @Override
                    public void beforeBulk(long executionId, BulkRequest request) {
                        log.debug("准备执行批量操作，ID: {}, 文档数: {}", executionId, request.numberOfActions());
                    }

                    @Override
                    public void afterBulk(long executionId, BulkRequest request, BulkResponse response) {
                        if (response.hasFailures()) {
                            log.error("批量操作部分失败，ID: {}, 错误: {}", executionId, response.buildFailureMessage());
                        } else {
                            log.debug("批量操作成功，ID: {}, 文档数: {}, 耗时: {}ms",
                                    executionId, request.numberOfActions(), response.getTook().getMillis());
                        }
                    }

                    @Override
                    public void afterBulk(long executionId, BulkRequest request, Throwable failure) {
                        log.error("批量操作失败，ID: {}", executionId, failure);
                    }
                });

        builder.setBulkActions(1000);
        builder.setBulkSize(new ByteSizeValue(5, ByteSizeUnit.MB));
        builder.setFlushInterval(TimeValue.timeValueSeconds(30));
        builder.setConcurrentRequests(5);
        bulkProcessor = builder.build();
        return bulkProcessor;
    }

    @PreDestroy
    public void destroy() {
        try {
            if (bulkProcessor != null) {
                log.info("Closing BulkProcessor...");
                bulkProcessor.close();
                log.info("BulkProcessor closed successfully");
            }
        } catch (Exception e) {
            log.error("Error closing BulkProcessor", e);
        }
    }
}
