package com.espc.sec.dataimport.kafka.schedule;

import com.espc.sec.dataimport.common.constant.LogKeyword;
import com.espc.sec.dataimport.common.enums.ImportTypeEnum;
import com.espc.sec.dataimport.kafka.service.KafkaImporter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class KafkaScheduler {
    @Autowired
    private KafkaImporter kafkaImporter;

    @Scheduled(fixedDelayString = "#{T(com.espc.sec.dataimport.common.config.Config).importConfig.kafka.getFixedDelayMills()}")
    public void run() {
        log.info(LogKeyword.KAFKA_IMPORT + "定时任务开始");
        kafkaImporter.import0(ImportTypeEnum.KAFKA);
    }

}
