package com.espc.sec.start;

import cn.hutool.core.date.StopWatch;
import com.espc.sec.dataexport.DataExportApp;
import com.espc.sec.flow.FileFlowClientMain;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2025/6/26
 **/
@SpringBootApplication(scanBasePackages = {"com.espc.sec"})
@Slf4j
@EnableScheduling
@MapperScan("com.espc.sec.dataexport.**.mapper")
public class DataBusClient {
    public static void main(String[] args) {
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();

            // 启动数据导出模块
            DataExportApp.start();
            // 启动Spring Boot应用
            SpringApplication.run(DataBusClient.class, args);
            // 启动数据通道客户端
            FileFlowClientMain.start();

            // // 启动后进行配置诊断
            // ConfigDiagnosticUtil diagnosticUtil = context.getBean(ConfigDiagnosticUtil.class);
            // diagnosticUtil.printConfigurationSummary();
            // diagnosticUtil.diagnoseAllConfigurations();

            stopWatch.stop();
            log.info("DataBusClient has been started successfully, it takes {} milliseconds", stopWatch.getTotalTimeMillis());
        } catch (Exception e){
            log.error("DataBusClient 启动失败", e);
            Runtime.getRuntime().halt(0);
        }
    }
}
