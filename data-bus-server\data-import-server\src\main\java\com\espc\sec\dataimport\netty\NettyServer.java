package com.espc.sec.dataimport.netty;

import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.netty.handler.HeartbeatHandler;
import com.espc.sec.dataimport.netty.handler.MessageDecoder;
import com.espc.sec.dataimport.netty.handler.MessageEncoder;
import com.espc.sec.dataimport.netty.handler.NettyServerHandler;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.PooledByteBufAllocator;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.IdleStateHandler;
import io.netty.handler.traffic.ChannelTrafficShapingHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/16
 **/
@Slf4j
public class NettyServer implements Runnable {

    public static void main(String[] args) {
        new Thread(new NettyServer()).start();
    }

    @Override
    public void run() {
        int port = Config.commonConfig.getTaskServerPort();
        EventLoopGroup bossGroup = new NioEventLoopGroup();
        EventLoopGroup workerGroup = new NioEventLoopGroup();

        try {
            ServerBootstrap b = new ServerBootstrap();
            b.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    // 等待队列长度
                    .option(ChannelOption.SO_BACKLOG, 1024)
                    // 端口重用
                    .option(ChannelOption.SO_REUSEADDR, true)
                    // 关闭Nagle
                    .childOption(ChannelOption.TCP_NODELAY, true)
                    // 开启TCP心跳
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    // 池化内存
                    .childOption(ChannelOption.ALLOCATOR, PooledByteBufAllocator.DEFAULT)
                    .handler(new LoggingHandler(LogLevel.INFO))
                    .childHandler(new ChildChannelHandler());

            ChannelFuture f = b.bind(port).sync();
            log.info("NettyServer 启动成功, 端口号: {}", port);

            f.channel().closeFuture().sync();
        } catch (Exception e) {
            log.error("NettyServer 启动失败", e);
        } finally {
            bossGroup.shutdownGracefully();
            workerGroup.shutdownGracefully();
        }
    }

    private class ChildChannelHandler extends ChannelInitializer<SocketChannel> {
        @Override
        protected void initChannel(SocketChannel socketChannel) {
            ChannelPipeline pipeline = socketChannel.pipeline();
            // 空闲检测
            pipeline.addLast(new IdleStateHandler(60, 0, 0, TimeUnit.SECONDS));
            // 流量整形（限制每秒最大流量）
            pipeline.addLast(new ChannelTrafficShapingHandler(10 * 1024 * 1024, 10 * 1024 * 1024));
            // 消息编码解码
            pipeline.addLast(new MessageDecoder());
            pipeline.addLast(new MessageEncoder());
            // 业务处理
            pipeline.addLast(new HeartbeatHandler());
            pipeline.addLast(new NettyServerHandler());
        }
    }
}
