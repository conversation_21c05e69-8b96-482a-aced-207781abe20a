package com.espc.sec.dataexport.task.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.enums.*;
import com.espc.sec.dataexport.common.exception.BusinessException;
import com.espc.sec.dataexport.common.util.DateUtil;
import com.espc.sec.dataexport.common.util.JsonUtil;
import com.espc.sec.dataexport.common.util.PageHelperUtil;
import com.espc.sec.dataexport.common.vo.PageVo;
import com.espc.sec.dataexport.job.service.JobService;
import com.espc.sec.dataexport.task.dto.DataTaskDto;
import com.espc.sec.dataexport.task.dto.DataTaskReq;
import com.espc.sec.dataexport.task.entity.DataTaskPo;
import com.espc.sec.dataexport.task.mapper.DataTaskMapper;
import com.espc.sec.dataexport.task.service.DataTaskService;
import com.espc.sec.dataexport.task.vo.DataTaskVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 数据任务服务实现
 */
@Slf4j
@Service
public class DataTaskServiceImpl implements DataTaskService {

    @Autowired
    private DataTaskMapper dataTaskMapper;
    @Autowired
    private JobService jobService;
    private static final ConcurrentHashMap<Integer, String> taskIdAndTaskNameMap = new ConcurrentHashMap<>();


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createTask(DataTaskDto dto) {
        DataTaskPo taskByName = dataTaskMapper.selectByName(dto.getName());
        if (taskByName != null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "任务名称不能重复");
        }
        DataTaskPo dataTaskPo = new DataTaskPo();
        BeanUtils.copyProperties(dto, dataTaskPo);
        dataTaskPo.setBusinessConfig(JsonUtil.objectToJson(dto.getBusinessConfig()));
        dataTaskPo.setStatus(TaskStatusEnum.PENDING.getCode());
        dataTaskPo.setIsDelete(YesOrNoEnum.NO.getCode());
        dataTaskPo.setCreateTime(new Date());
        dataTaskPo.setUpdateTime(new Date());
        try {
            if (TaskTypeEnum.SCHEDULED.getCode().equals(dto.getType())) {
                dataTaskPo.setCron(DateUtil.dateTimeToCron(dto.getCron()));
            } else {
                dataTaskPo.setCron(DateUtil.dateTimeToCron(DateUtil.format(DateUtil.offsetSecond(new Date(), 10), Constants.YYYY_MM_DD_HH_MM_SS)));
            }
        } catch (ParseException e) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "cron表达式错误,格式为yyyy-MM-dd HH:mm:ss");
        }

        dataTaskMapper.insert(dataTaskPo);
        JobTypeEnum jobTypeEnum = JobTypeEnum.getByCode(dto.getBusinessType());
        //添加定时任务
        jobService.addJob(dataTaskPo.getId()
                , jobTypeEnum
                , dataTaskPo.getCron()
                , JsonUtil.fileToObject(dataTaskPo.getBusinessConfig(), jobTypeEnum.getTaskProperties())
        );
        return dataTaskPo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTask(DataTaskDto dto) {
        DataTaskPo taskByName = dataTaskMapper.selectByNameNotId(dto.getName(), dto.getId());
        if (taskByName != null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "任务名称不能重复");
        }
        DataTaskPo dataTaskPo = new DataTaskPo();
        BeanUtils.copyProperties(dto, dataTaskPo);
        try {
            if (TaskTypeEnum.SCHEDULED.getCode().equals(dto.getType())) {
                dataTaskPo.setCron(DateUtil.dateTimeToCron(dto.getCron()));
            } else {
                dataTaskPo.setCron(DateUtil.dateTimeToCron(DateUtil.format(DateUtil.offsetSecond(new Date(), 10), Constants.YYYY_MM_DD_HH_MM_SS)));
            }
        } catch (ParseException e) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "cron表达式错误,格式为yyyy-MM-dd HH:mm:ss");
        }
        dataTaskPo.setUpdateTime(new Date());
        dataTaskPo.setBusinessConfig(JsonUtil.objectToJson(dto.getBusinessConfig()));

        JobTypeEnum jobTypeEnum = JobTypeEnum.getByCode(dto.getBusinessType());
        jobService.updateJob(dataTaskPo.getId()
                , jobTypeEnum
                , dataTaskPo.getCron()
                , JsonUtil.fileToObject(dataTaskPo.getBusinessConfig(), jobTypeEnum.getTaskProperties())
        );
        return dataTaskMapper.updateById(dataTaskPo) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTask(Integer id) {
        dataTaskMapper.deleteById(id);
        // 删除定时任务
        jobService.deleteJob(id);
        return true;
    }

    @Override
    public PageVo<DataTaskVo> pageTasks(DataTaskReq req) {
        PageVo<DataTaskPo> pageList = PageHelperUtil.startPage(req, () -> dataTaskMapper.selectByCondition(req));

        List<DataTaskVo> dataTaskPos = pageList.getList()
                .stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return new PageVo<>(req.getPageNo(), req.getPageSize(), pageList.getTotal(), dataTaskPos);
    }

    @Override
    public List<DataTaskPo> listTasksByStatus(TaskStatusEnum statusEnum) {
        return dataTaskMapper.listTasksByStatus(statusEnum.getCode());
    }

    @Override
    public void updateStatus(Integer id, TaskStatusEnum statusEnum) {
        dataTaskMapper.updateStatus(id, statusEnum.getCode());
    }

    @Override
    public String getTaskNameByIdFromMemoryCache(Integer id) {
        if (taskIdAndTaskNameMap.containsKey(id)) {
            return taskIdAndTaskNameMap.get(id);
        }
        DataTaskPo taskPo = dataTaskMapper.getById(id);
        if (taskPo == null) {
            return "";
        }
        taskIdAndTaskNameMap.put(id, taskPo.getName());
        return taskPo.getName();
    }

    /**
     * 将PO转换为VO
     *
     * @param dataTaskPo PO对象
     * @return VO对象
     */
    private DataTaskVo convertToVO(DataTaskPo dataTaskPo) {
        DataTaskVo dataTaskVo = new DataTaskVo();
        BeanUtils.copyProperties(dataTaskPo, dataTaskVo);
        if (StringUtils.isNotBlank(dataTaskVo.getCron())) {
            dataTaskVo.setCron(DateUtil.cronToDateTime(dataTaskVo.getCron()));
        }
        dataTaskVo.setBusinessConfig(JsonUtil.fileToObject(dataTaskPo.getBusinessConfig(), Object.class));
        return dataTaskVo;
    }
}
