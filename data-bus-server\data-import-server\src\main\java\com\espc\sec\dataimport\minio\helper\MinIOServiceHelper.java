package com.espc.sec.dataimport.minio.helper;


import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.config.Environment;
import io.minio.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class MinIOServiceHelper {

    public static MinioClient minioClient = null;

    // 缓存桶是否存在
    private static ConcurrentHashMap<String, Boolean> existsBucket = new ConcurrentHashMap<>();

    public static boolean loadMinIOConnect() {
        try {
            Environment.MinioDTO minio = Config.environment.minio;
            minioClient = MinioClient.builder()
                    .endpoint(minio.getEndPoint())
                    .credentials(minio.getUsername(), minio.getPassword())
                    .build();
            return true;
        } catch (Exception e) {
            log.error("获取minio连接异常", e);
            return false;
        }
    }

    /**
     * 从给定输入流中传输对象并放入bucket
     */
    public static ObjectWriteResponse putObject(String bucketName, String objectName, InputStream stream, long objectSize, String contentType, Map<String, String> metadata) throws Exception {
        if (minioClient == null) {
            loadMinIOConnect();
        }

        if (minioClient == null) {
            log.error("获取minio连接失败");
            return null;
        }

        if (!existsBucket.getOrDefault(bucketName, false)) {
            synchronized (MinIOServiceHelper.class) {
                if (!existsBucket.getOrDefault(bucketName, false)) {
                    BucketExistsArgs existsArgs = BucketExistsArgs.builder().bucket(bucketName).build();
                    if (!minioClient.bucketExists(existsArgs)) {
                        MakeBucketArgs makeArgs = MakeBucketArgs.builder().bucket(bucketName).build();
                        minioClient.makeBucket(makeArgs);
                        log.info("bucket {} 不存在， 自动创建该bucket", bucketName);
                    }
                    existsBucket.put(bucketName, true);
                }
            }
        }
        if (objectName.endsWith(".txt")) {
            contentType = "text/plain";
        } else if (objectName.endsWith(".cert")) {
            contentType = "application/x-x509-ca-cert"; // 证书文件
        } else if (objectName.endsWith(".eml")) {
            contentType = "message/rfc822"; // 电子邮件
        } else {
            contentType = "application/octet-stream"; // 默认二进制流
        }
        //long objSize = -1;
        // objectSize已知，partSize设为-1意为自动设置
        long partSize = -1;
        PutObjectArgs putArgs = PutObjectArgs.builder().userMetadata(metadata).bucket(bucketName).object(objectName).stream(stream, objectSize, partSize).contentType(contentType).build();
        ObjectWriteResponse response = minioClient.putObject(putArgs);

        return response;
    }


    /**
     * 上传MultipartFile
     *
     * @param bucketName 文件存放的bucket
     * @param objectName
     */
    public static ObjectWriteResponse uploadFile(String bucketName, String objectName, File file) throws Exception {
        return uploadFile(bucketName, objectName, file, null);
    }

    /**
     * 上传MultipartFile,加入metadata
     *
     * @param bucketName 文件存放的bucket
     * @param objectName
     */
    public static ObjectWriteResponse uploadFile(String bucketName, String objectName, File file, Map<String, String> metadata) throws Exception {
        InputStream inputStream = Files.newInputStream(file.toPath());
        ObjectWriteResponse response = putObject(bucketName, objectName, inputStream, file.length(), Files.probeContentType(file.toPath()), metadata);
        inputStream.close();
        return response;
    }


}