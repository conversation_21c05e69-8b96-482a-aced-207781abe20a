package com.espc.sec.dataimport.common.enums;

import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.constant.LogKeyword;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.File;

@AllArgsConstructor
@Getter
public enum ImportTypeEnum {
    /**
     * 导出类型枚举
     */
    ES("elasticsearch", LogKeyword.ES_IMPORT),
    STAR_ROCKS("starrocks", LogKeyword.STARROCKS_IMPORT),
    TIDB("tidb", LogKeyword.TIDB_IMPORT),
    KAFKA("kafka", LogKeyword.KAFKA_IMPORT),
    MINIO("minio", LogKeyword.MINIO_IMPORT),
    HDFS("hdfs", LogKeyword.HDFS_IMPORT),
    ;

    private final String code;
    private final String logKeyWord;

    public static String getImportPath(ImportTypeEnum importTypeEnum) {
        return Config.commonConfig.getImportPath() + File.separator + importTypeEnum.getCode();
    }

    public static String getImportErrorPath(ImportTypeEnum importTypeEnum) {
        return Config.commonConfig.getImportErrorPath() + File.separator + importTypeEnum.getCode();
    }

    public static Integer getScanFileSize(ImportTypeEnum importTypeEnum) {
        return Config.importConfig.scanFileLimit.get(importTypeEnum.getCode());
    }
}
