package com.espc.sec.dataexport.common.loader;


import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.config.Environment;
import com.espc.sec.dataexport.common.config.ExportConfig;
import com.espc.sec.dataexport.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;

/**
 * 本地配置加载器
 * 负责从environment.json文件加载配置信息
 * 替代Spring Boot的配置加载机制
 */
@Slf4j
public class LocalConfigLoader {
    public static void parseConfig() {
        File env = new File("conf/DataExportClient/conf/environment.json");
        File common = new File("conf/DataExportClient/conf/config-common.json");
        File export = new File("conf/DataExportClient/conf/config-export.json");
        try {
            Config.environment = JsonUtil.fileToObject(env, Environment.class);
            Config.commonConfig = JsonUtil.fileToObject(common, com.espc.sec.dataexport.common.config.CommonConfig.class);
            Config.exportConfig = JsonUtil.fileToObject(export, ExportConfig.class);
        } catch (IOException e) {
            log.error("解析配置失败", e);
        }
    }

}
