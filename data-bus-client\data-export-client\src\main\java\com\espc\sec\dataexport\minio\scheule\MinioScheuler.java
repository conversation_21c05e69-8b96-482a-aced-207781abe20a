package com.espc.sec.dataexport.minio.scheule;

import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.minio.helper.MinIOServiceHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;

import static com.espc.sec.dataexport.common.constant.Constants.ARN_MINIO_SQS_MINIO_KAFKA;

@Component
@Slf4j
public class MinioScheuler {
    @Scheduled(cron = "2 0 0 * * ?")
    public void minioTopicListening() {
        log.info("minio添加监听");
        Config.exportConfig.minio.getBuckets().forEach(bucket -> {
            try {
                String bucketName = bucket.getPrefix();
                if ("d".equals(bucket.getMode())) {
                    bucketName = bucketName + "-" + DateUtil.format(new Date(), "yyyyMMdd");
                }
                MinIOServiceHelper.configureKafkaNotification(bucketName, ARN_MINIO_SQS_MINIO_KAFKA);
            } catch (Exception e) {
                log.info(LogKeyword.MINIO_EXPORT + "minio桶监听失败", e);
            }
        });
    }
}
