package com.espc.sec.dataimport.task.controller;


import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.dto.Result;
import com.espc.sec.dataimport.common.vo.PageVo;
import com.espc.sec.dataimport.task.dto.DataTaskDto;
import com.espc.sec.dataimport.task.dto.DataTaskReq;
import com.espc.sec.dataimport.task.service.DataTaskService;
import com.espc.sec.dataimport.task.vo.DataTaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据任务控制器
 */
@RestController
@RequestMapping("/task")
@Api(tags = "数据任务管理")
public class DataTaskController {

    @Resource
    private DataTaskService dataTaskService;

    @PostMapping
    @ApiOperation("创建任务")
    public Result<Integer> createTask(@RequestBody @Validated DataTaskDto dataTaskDto) {
        Integer taskId = dataTaskService.createTask(dataTaskDto);
        return Result.success(taskId);
    }

    @PutMapping
    @ApiOperation("更新任务")
    public Result<Boolean> updateTask(@RequestBody @Validated DataTaskDto dataTaskDto) {
        Boolean result = dataTaskService.updateTask(dataTaskDto);
        return Result.success(result);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除任务")
    public Result<Boolean> deleteTask(@PathVariable Integer id) {
        Boolean result = dataTaskService.deleteTask(id);
        return Result.success(result);
    }


    @PostMapping("/list")
    @ApiOperation("查询所有任务")
    public Result<PageVo<DataTaskVo>> listAllTasks(@RequestBody DataTaskReq dataTaskReq) {
        PageVo<DataTaskVo> taskList = dataTaskService.listAllTasks(dataTaskReq);
        return Result.success(taskList);
    }

    @GetMapping("/nodeList")
    @ApiOperation("查询节点")
    public Result<List<String>> nodeList() {
        List<String> nodeList = Config.commonConfig.getSupportedNodeNames();
        return Result.success(nodeList);
    }
}
