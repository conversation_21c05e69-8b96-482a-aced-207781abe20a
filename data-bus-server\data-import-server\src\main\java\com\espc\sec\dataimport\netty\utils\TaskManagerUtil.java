package com.espc.sec.dataimport.netty.utils;

import cn.hutool.json.JSONUtil;
import com.espc.sec.dataimport.common.enums.OperateTypeEnum;
import com.espc.sec.dataimport.common.exception.NettyConnectionException;
import com.espc.sec.dataimport.netty.entity.MessageTypeEnum;
import com.espc.sec.dataimport.netty.entity.MessageWrapper;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2025/7/18
 **/
@Slf4j
public class TaskManagerUtil {
    /**
     * @param node
     * @param operateTypeEnum
     * @param task
     */
    public static void taskManager(String node, OperateTypeEnum operateTypeEnum, Object task) {
        if (node == null || task == null) {
            log.error("任务下发参数异常: null");
            return;
        }

        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setType(MessageTypeEnum.TASK);
        messageWrapper.setOperateType(operateTypeEnum);

        byte[] body = JSONUtil.toJsonStr(task).getBytes(StandardCharsets.UTF_8);
        messageWrapper.setLength(body.length);
        messageWrapper.setBody(body);

        Channel channel = ChannelUtil.getChannel(node);
        if (channel == null) {
            log.error("客户端连接异常");
            throw new NettyConnectionException(node + "客户端连接异常");
        }

        channel.writeAndFlush(messageWrapper);
        log.info("总中心下发任务成功, 分中心: {}", node);
    }

}
