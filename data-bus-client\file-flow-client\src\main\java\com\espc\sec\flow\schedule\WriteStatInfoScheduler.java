package com.espc.sec.flow.schedule;

import com.espc.sec.flow.aspect.IStat;
import com.espc.sec.flow.bean.StatStruct;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2018-09-20
 */
@Slf4j
public class WriteStatInfoScheduler {

    private static final Logger STAT_LOGGER = LoggerFactory.getLogger("WriteStatInfoScheduler.stat");
    private static final Logger FLOWSERVERSTAT_LOGGER = LoggerFactory.getLogger("WriteStatInfoScheduler.flowServerStat");

    /**
     * 一小时的毫秒数
     */
    private static final long ONE_HOUR_MILL = 1000 * 60 * 60L;

    /**
     * 开始调度写6005文件和统计的结果
     */
    public void schedule() {
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    String lastHour = new SimpleDateFormat("yyyy-MM-dd HH")
                            .format(new Date(System.currentTimeMillis() - ONE_HOUR_MILL));

                    writeOneHour(IStat.STAT_FLOW_MAP, lastHour, STAT_LOGGER);
                    writeOneHour(IStat.FLOW_SERVER_STAT_MAP, lastHour, FLOWSERVERSTAT_LOGGER);
                    // writeOneHour(IStat.THIS_MACHINE_STAT_MAP, lastHour, Log.thisMachineStat);
                    // writeOneHour(IStat.ICE_DATA_POOL_STAT_MAP, lastHour, Log.iceDataPoolStat);
                    // writeOneHour(IStat.SFTP_DATA_POOL_STAT_MAP, lastHour, Log.sftpDataPoolStat);
                    //如果上一个小时是前一天的23点，就把前一天的统计信息写到文件中，同时删除前一天的统计内容
                    if (lastHour.endsWith("23")) {
                        String lastDay = lastHour.substring(0, "yyyy-MM-dd".length());
                        writeOneDay(IStat.STAT_FLOW_MAP, lastDay, STAT_LOGGER);
                        writeOneDay(IStat.FLOW_SERVER_STAT_MAP, lastDay, FLOWSERVERSTAT_LOGGER);
                        // writeOneDay(IStat.THIS_MACHINE_STAT_MAP, lastDay, Log.thisMachineStat);
                        // writeOneDay(IStat.ICE_DATA_POOL_STAT_MAP, lastDay, Log.iceDataPoolStat);
                        // writeOneDay(IStat.SFTP_DATA_POOL_STAT_MAP, lastDay, Log.sftpDataPoolStat);
                    }
                } catch (Exception e) {
                    log.error("schedule write stat info error.", e);
                }
            }
        }, getStartTime(), ONE_HOUR_MILL);
    }

    /**
     * 得到调度的开始时间
     *
     * @return 返回开始时间
     */
    private Date getStartTime() {
        String nextHour = new SimpleDateFormat("yyyy-MM-dd HH")
                .format(new Date(System.currentTimeMillis() + ONE_HOUR_MILL));
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(nextHour + ":00:00");
        } catch (ParseException e) {
            log.error("stat scheduler get start time error.", e);
            return new Date();
        }
    }

    /**
     * 把上一个小时的统计信息写到统计的日志文件中
     *
     * @param statMap  存放统计信息的map
     * @param lastHour 时间yyyy-MM-dd HH
     * @param statLog  统计的结果文件
     */
    private void writeOneHour(Map<String, Map<String, Map<String, StatStruct>>> statMap, String lastHour,Logger statLog) {
        Map<String, Map<String, StatStruct>> nodeMap = statMap.get(lastHour);
        if (nodeMap != null && !nodeMap.isEmpty()) {
            StringBuilder content = new StringBuilder();
            for (String node : nodeMap.keySet()) {
                content.append("    ").append(node).append(":").append("\r\n");
                Map<String, StatStruct> typeMap = nodeMap.get(node);
                if (typeMap != null && !typeMap.isEmpty()) {
                    for (String type : typeMap.keySet()) {
                        content.append("        ").append(type).append(": ").append(typeMap.get(type).toString()).append("\r\n");
                    }
                }
            }
            String logCon = "flow data info between " + lastHour + ":00:00——" + lastHour + ":59:59:\r\n" + content.toString();
            statLog.info(logCon);
        } else {
            statLog.info("no flow data between " + lastHour + ":00:00——" + lastHour + ":59:59");
        }
    }

    private void writeOneDay(Map<String, Map<String, Map<String, StatStruct>>> statMap, String lastDay,Logger statLog) {
        Map<String, StatStruct> hourCountMap = getCountMap(statMap);
        statLog.info("flow data info between " + lastDay + " 00:00:00——" + lastDay + " 23:59:59 —— " + hourCountMap);
        statLog.info("flow data detail info in " + lastDay + " is:" + statMap);

        Set<String> set = new HashSet<>();
        set.addAll(statMap.keySet());
        for (String hour : set) {
            if (hour.startsWith(lastDay)) {
                statMap.remove(hour);
            }
        }
    }


    /**
     * 得到每种类型的统计总数
     *
     * @param map 存放统计结果的map
     * @return 返回类型对应统计总数的map
     */
    private Map<String, StatStruct> getCountMap(Map map) {
        Map<String, StatStruct> countMap = new HashMap<>(map.size() * 2);
        Set<Map.Entry> set = map.entrySet();
        for (Map.Entry entry : set) {
            Object value = entry.getValue();
            if (value instanceof Map) {
                countMap.put(entry.getKey().toString(), getCount((Map) entry.getValue()));
            }
        }
        return countMap;
    }

    /**
     * 得到Map中统计的总数
     *
     * @param map 统计结果的map
     * @return 返回总数
     */
    private StatStruct getCount(Map map) {
        StatStruct struct = new StatStruct();
        for (Object value : map.values()) {
            if (value instanceof Map) {
                struct.add(getCount((Map) value));
            } else if (value instanceof StatStruct) {
                struct.add((StatStruct) value);
            }
        }
        return struct;
    }
}
