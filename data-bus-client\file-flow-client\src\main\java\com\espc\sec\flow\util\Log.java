// package com.espc.sec.flow.util;
//
// import lombok.extern.slf4j.Slf4j;
// import org.apache.log4j.LogManager;
// import org.apache.log4j.PropertyConfigurator;
//
// /**
//  * <AUTHOR>
//  * @date 2017-07-26
//  * Log日志
//  */
// @Slf4j
// public class Log {
//
//     /**
//      * log日志
//      */
//     public static org.apache.log4j.Logger low = null;
//     /**
//      * error日志
//      */
//     public static org.apache.log4j.Logger high = null;
//     /**
//      * 分流统计日志
//      */
//     public static org.apache.log4j.Logger stat = null;
//
//     /**
//      * 本机分流统计日志
//      */
//     public static org.apache.log4j.Logger thisMachineStat = null;
//     /**
//      * 汇聚分流统计日志
//      */
//     public static org.apache.log4j.Logger flowServerStat = null;
//     /**
//      * 分布式数据池分流统计日志
//      */
//     public static org.apache.log4j.Logger iceDataPoolStat = null;
//     /**
//      * 单机数据池分流统计日志
//      */
//     public static org.apache.log4j.Logger sftpDataPoolStat = null;
//
//     static {
//         try {
//             PropertyConfigurator.configure("conf/FileFlowClient/log4j.properties");
//             low = LogManager.getLogger("LOW");
//             high = LogManager.getLogger("HIGH");
//             stat = LogManager.getLogger("STAT");
//             thisMachineStat = LogManager.getLogger("THIS_MACHINE_STAT");
//             flowServerStat = LogManager.getLogger("FLOW_SERVER_STAT");
//             iceDataPoolStat = LogManager.getLogger("ICE_DATA_POOL_STAT");
//             sftpDataPoolStat = LogManager.getLogger("SFTP_DATA_POOL_STAT");
//         } catch (Exception e) {
//             log.error("log error", e);
//         }
//     }
// }
