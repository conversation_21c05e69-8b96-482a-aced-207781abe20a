package com.espc.sec.dataexport;

import com.espc.sec.dataexport.common.loader.LocalConfigLoader;
import com.espc.sec.dataexport.elasticsearch.util.EsClientHolder;
import com.espc.sec.dataexport.netty.NettyClient;
import lombok.extern.slf4j.Slf4j;

/**
 * 大数据导出应用主类
 *
 * <AUTHOR>
 * @date 2023/7/14
 **/
@Slf4j
public class DataExportApp {

    public static void start() {
        try {
            // 加载配置文件
            LocalConfigLoader.parseConfig();
            // 初始化ES连接
            EsClientHolder.init();
            // 连接netty服务端
            new Thread(new NettyClient()).start();
        } catch (Exception e) {
            log.error("Failed to start DataExportApp", e);
            System.exit(-1);
        }
    }
}
