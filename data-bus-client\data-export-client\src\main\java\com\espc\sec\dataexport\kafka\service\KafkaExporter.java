package com.espc.sec.dataexport.kafka.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import com.espc.sec.dataexport.common.service.impl.AbstractExporter;
import com.espc.sec.dataexport.common.util.DateUtil;
import com.espc.sec.dataexport.kafka.dto.KafkaExportDto;
import com.espc.sec.dataexport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataexport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataexport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataexport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataexport.task.service.DataTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */
@Service
@Slf4j
public class KafkaExporter extends AbstractExporter<KafkaExportDto> {
    @Autowired
    private KafkaConsumerService consumerService;

    @Autowired
    private MonitorTaskDataService monitorTaskDataService;

    @Autowired
    private MonitorIncrementDataService monitorIncrementDataService;

    @Autowired
    private DataTaskService dataTaskService;

    @Override
    protected List<File> doExport(KafkaExportDto dto) throws Exception {
        ConsumerRecords<String, Object> records;
        if (ExportModeEnum.INCREMENT.equals(dto.getExportModeEnum())) {
            records = consumerService.consumeMessages(dto.getTopicNames());
        } else {
            records = consumerService.consumeMessagesWithLimit(dto.getTopicNames(), dto.getSize());
        }
        if (null == records || records.isEmpty()) {
            return Lists.newArrayList();
        }
        log.info("{}消费到 {} 条消息", LogKeyword.KAFKA_EXPORT, records.count());
        List<File> tempFiles = Lists.newArrayList();
        for (String topic : dto.getTopicNames()) {
            Iterable<ConsumerRecord<String, Object>> recordTopic = records.records(topic);
            if (null == recordTopic) {
                continue;
            }

            List<String> messages = new ArrayList<>(1000);
            for (ConsumerRecord<String, Object> record : recordTopic) {
                messages.add(StrUtil.str(record.value(), StandardCharsets.UTF_8));
            }
            exportToFilesWithSizeLimit(topic, messages, tempFiles, dto.getExportModeEnum(), dto.getTaskId());
        }
        return tempFiles;
    }

    @Override
    protected void saveImportLogToMysql(KafkaExportDto exporter, File tempFile) throws Exception {
        String timeStr = DateUtil.format(new Date(), Constants.MILLS_FORMAT);
        String nodeName = Config.commonConfig.getNodeName();
        String databaseType = exporter.getExportTypeEnum().getCode();
        String fileName = tempFile.getName();
        Long size = (long) FileUtil.readLines(tempFile, StandardCharsets.UTF_8).size();

        // 从文件名解析topic
        String[] parts = fileName.split("_");
        String topic = parts.length >= 6 ? parts[5] : "unknown";
        Integer exportMode = Integer.valueOf(parts[0]);

        if (ExportModeEnum.INCREMENT.getCode().equals(exportMode)) {
            MonitorIncrementDataDto dto = new MonitorIncrementDataDto();
            dto.setNode(nodeName);
            dto.setDatabaseType(databaseType);
            dto.setTableName(topic);
            dto.setFileName(fileName);
            dto.setSize(size);
            dto.setTime(new Date());
            monitorIncrementDataService.create(dto);
        } else {

            String taskName = "kafka_export_task";
            if (exporter.getTaskId() != null) {
                taskName = dataTaskService.getTaskNameByIdFromMemoryCache(exporter.getTaskId());
            }

            MonitorTaskDataAddReq req = new MonitorTaskDataAddReq();
            req.setNode(nodeName);
            req.setTaskName(taskName);
            req.setDatabaseType(databaseType);
            req.setTableName(topic);
            req.setSize(size);
            req.setTime(timeStr);
            req.setFileName(fileName);
            monitorTaskDataService.add(req);
        }
    }

    private void exportToFilesWithSizeLimit(String topic, List<String> messages, List<File> tempFiles, ExportModeEnum exportModeEnum, Integer taskId) throws Exception {
        int fileIndex = 1;
        File currentFile = createNewFile(topic, fileIndex, exportModeEnum, taskId);
        tempFiles.add(currentFile);

        BufferedWriter writer = new BufferedWriter(new FileWriter(currentFile, false));

        try {
            for (String message : messages) {
                writer.write(message);
                writer.newLine();

                // 检查当前文件大小是否超过限制
                if (currentFile.length() >= Config.commonConfig.getMaxTempFileSizeMb() * 1024 * 1024) {
                    writer.close(); // 关闭当前文件

                    // 创建新文件
                    fileIndex++;
                    currentFile = createNewFile(topic, fileIndex, exportModeEnum, taskId);
                    tempFiles.add(currentFile);
                    writer = new BufferedWriter(new FileWriter(currentFile, false));
                }
            }
        } finally {
            writer.close();
        }
    }

    /**
     * 时间戳_节点_数据库类型_主题名_文件分片索引.log
     *
     * @param topic
     * @param index
     * @param exportModeEnum
     * @return
     */
    private File createNewFile(String topic, int index, ExportModeEnum exportModeEnum, Integer taskId) {
        String tempDir = Config.commonConfig.getOutputTempPath() + File.separator + ExportTypeEnum.KAFKA.getCode();
        String timeStr = DateUtil.format(new Date(), Constants.MILLS_FORMAT);
        String nodeName = Config.commonConfig.getNodeName();
        String databaseType = "kafka";
        boolean isTaskType = exportModeEnum.equals(ExportModeEnum.TASK);

        String absPath = String.format("%s_%s_%s_%s_%s_%s_%s.log"
                , exportModeEnum.getCode()
                , isTaskType ? (taskId != null ? taskId : 0) : 0
                , timeStr
                , nodeName
                , databaseType
                , topic
                , index);

        return new File(tempDir, absPath);
    }

}

