package com.espc.sec.dataexport.hdfs.service;

import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.exception.HdfsDownLoadException;
import com.espc.sec.dataexport.common.service.impl.AbstractExporter;
import com.espc.sec.dataexport.hdfs.dto.HdfsExportDto;
import com.espc.sec.dataexport.hdfs.helper.HdfsServiceHelper;
import com.espc.sec.dataexport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataexport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataexport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataexport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataexport.task.service.DataTaskService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class HdfsExporter extends AbstractExporter<HdfsExportDto> {

    @Autowired
    private MonitorIncrementDataService monitorIncrementDataService;

    @Autowired
    private MonitorTaskDataService monitorTaskDataService;

    @Autowired
    private DataTaskService dataTaskService;

    @Override
    protected List<File> doExport(HdfsExportDto dto) throws Exception {
        String tempFileAbsName = buildTempFileName(dto);

        boolean success = HdfsServiceHelper.downloadFile(dto.getHdfsAbsPath(), tempFileAbsName);
        if (!success) {
            throw new HdfsDownLoadException("hdfs文件下载失败");
        }

        return Lists.newArrayList(new File(tempFileAbsName));
    }

    @Override
    protected void saveImportLogToMysql(HdfsExportDto exporter, File tempFile) throws Exception {
        if (exporter.getExportModeEnum().equals(ExportModeEnum.INCREMENT)) {
            monitorIncrementDataService.create(MonitorIncrementDataDto.builder()
                    .node(Config.commonConfig.getNodeName())
                    .size(tempFile.length())
                    .databaseType(exporter.getExportTypeEnum().getCode())
                    .fileName(exporter.getHdfsAbsPath().substring(exporter.getHdfsAbsPath().lastIndexOf("/") + 1))
                    .tableName(exporter.getHdfsAbsPath().substring(0, exporter.getHdfsAbsPath().lastIndexOf("/") + 1))
                    .time(new Date())
                    .build());
        } else {
            String taskName = dataTaskService.getTaskNameByIdFromMemoryCache(exporter.getTaskId());

            MonitorTaskDataAddReq taskDataAddReq = new MonitorTaskDataAddReq();
            taskDataAddReq.setTaskName(taskName);
            taskDataAddReq.setTime(DateUtil.format(new Date(), Constants.MILLS_FORMAT));
            taskDataAddReq.setSize(tempFile.length());
            taskDataAddReq.setTableName(exporter.getHdfsAbsPath().substring(0, exporter.getHdfsAbsPath().lastIndexOf("/") + 1));
            taskDataAddReq.setNode(Config.commonConfig.getNodeName());
            taskDataAddReq.setDatabaseType(exporter.getExportTypeEnum().getCode());
            taskDataAddReq.setFileName(tempFile.getName());
            monitorTaskDataService.add(taskDataAddReq);
        }

    }

    private String buildTempFileName(HdfsExportDto dto) {
        //dto.getHdfsAbsPath
        ///data/pcap/2025/7/21/20250721085847091_sichuan_20241127175236846_79E8566826_00029.pcap
        String fileName = dto.getHdfsAbsPath().substring(dto.getHdfsAbsPath().lastIndexOf("/") + 1);

        String millsDateStr = DateUtil.format(new Date(), Constants.MILLS_FORMAT);
        boolean isTaskType = dto.getExportModeEnum().equals(ExportModeEnum.TASK);
        String nodeFileName = dto.getExportModeEnum().getCode()
                + "_"
                + (isTaskType ? dto.getTaskId() : 0)
                + "_"
                + millsDateStr
                + "_"
                + Config.commonConfig.getNodeName()
                + "_"
                + fileName;

        return dto.getExportTempPath() + File.separator + nodeFileName;
    }

}
