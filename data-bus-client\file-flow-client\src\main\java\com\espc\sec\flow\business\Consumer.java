package com.espc.sec.flow.business;

import com.espc.sec.flow.FileFlowClientMain;
import com.espc.sec.flow.bean.Constant;
import com.espc.sec.flow.bean.Job;
import com.espc.sec.flow.proxy.AbstractProxy;
import com.espc.sec.flow.util.ConfigUtil;
import com.espc.sec.flow.util.IoUtil;
import com.espc.sec.flow.util.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2019-08-26
 */
@Slf4j
public class Consumer implements Runnable {
    @Override
    public void run() {
        int i = 0;
        while (i < 10) {
            //如果启动的时候队列中还没有文件，就先等待把任务读到队列中
            if (Constant.JOB_QUEUE.size() == 0) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                }
                i++;
            } else {
                break;
            }
        }
        ThreadPoolExecutor pool = ThreadUtil.createThreadPool(ConfigUtil.config.getThreadPoolSize());
        while (true) {
            while (FileFlowClientMain.proxyMap.isEmpty()) {
                log.info("no server proxy can be connected. sleep 10s.");
                try {
                    Thread.sleep(10000);
                } catch (InterruptedException e) {
                }
            }
            Job job = takeJobFromQueue();
            if (job != null) {
                for (Map.Entry<Integer, AbstractProxy> entry : FileFlowClientMain.proxyMap.entrySet()) {
                    if (entry.getValue() != null) {
                        while (pool.getQueue().size() > ConfigUtil.config.getThreadPoolSize()) {
                            log.info("wait flow thread is over " + ConfigUtil.config.getThreadPoolSize() + ", wait 1s.");
                            try {
                                Thread.sleep(1000);
                            } catch (InterruptedException e) {
                                log.error(e.getMessage());
                            }
                        }
                        pool.submit(new Runnable() {
                            @Override
                            public void run() {
                                entry.getValue().flowOneFile(job);
                            }
                        });
                    } else {
                        log.error("proxy is null");
                    }
                }
                if (ConfigUtil.needDeleteFile()) {
                    File file = job.getFile();
                    if (file.exists()) {
                        FileUtils.deleteQuietly(file);
                    }
                }
            }
        }
    }

    /**
     * 从队列中获取一个任务
     *
     * @return 返回任务
     */
    private static Job takeJobFromQueue() {
        Job job = null;
        try {
            job = Constant.JOB_QUEUE.poll(3, TimeUnit.SECONDS);
            if (job == null) {
                log.info("JOB_QUEUE is empty. flow thread will sleep 10s.");
                Thread.sleep(10000);
            }
        } catch (Exception e) {
            log.error("take job from JOB_QUEUE failed.", e);
        }
        if (job != null) {
            try {
                if (ConfigUtil.needReadToMem()) {
                    byte[] content = IoUtil.readFileToBinary(job.getAbsolutePath());
                    job.setContent(content);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
                return null;
            }
        }
        return job;
    }
}
