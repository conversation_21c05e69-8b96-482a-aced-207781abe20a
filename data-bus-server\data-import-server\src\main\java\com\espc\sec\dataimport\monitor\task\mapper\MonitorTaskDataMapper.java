package com.espc.sec.dataimport.monitor.task.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataBaseReq;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataReq;
import com.espc.sec.dataimport.monitor.task.entity.MonitorTaskDataPo;
import com.espc.sec.dataimport.monitor.task.vo.MonitorTaskDataAggregateVo;
import com.espc.sec.dataimport.monitor.task.vo.MonitorTaskDataVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 监控任务数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Mapper
public interface MonitorTaskDataMapper extends BaseMapper<MonitorTaskDataPo> {

    /**
     * 分页查询监控任务数据
     * 
     * @param page 分页对象
     * @param req 查询条件
     * @return 分页结果
     */
    List<MonitorTaskDataVo> selectPageList(@Param("req") MonitorTaskDataReq req);

    /**
     * 列表查询监控任务数据（不分页）
     *
     * @param req 查询条件
     * @return 列表结果
     */
    List<MonitorTaskDataVo> selectList(@Param("req") MonitorTaskDataBaseReq req);

    /**
     * 聚合查询监控任务数据
     * 根据node、task_name、database_type、table_name分组汇总size
     *
     * @param req 查询条件
     * @return 聚合结果
     */
    List<MonitorTaskDataAggregateVo> selectAggregateList(@Param("req") MonitorTaskDataBaseReq req);
}