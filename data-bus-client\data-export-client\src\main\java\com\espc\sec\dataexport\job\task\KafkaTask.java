package com.espc.sec.dataexport.job.task;

import com.espc.sec.dataexport.common.dto.task.KafkaTaskProperties;
import com.espc.sec.dataexport.common.util.SpringBeanUtil;
import com.espc.sec.dataexport.kafka.service.KafkaExportServiceImpl;

public class KafkaTask extends AbstractTask<KafkaTaskProperties> {

    @Override
    protected void doExecute(KafkaTaskProperties taskProperties) throws Exception {
        KafkaExportServiceImpl exportService = SpringBeanUtil.getBean(KafkaExportServiceImpl.class);
        exportService.taskExport(taskProperties);
    }
}
