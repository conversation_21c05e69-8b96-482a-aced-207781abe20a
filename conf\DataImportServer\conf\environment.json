{"elasticsearch": {"indexClusterName": "my_es_cluster", "indexClusterServer": "192.168.40.156:9200", "indexClusterServerHttp": "192.168.40.156:9200", "indexUserName": "", "indexPassword": ""}, "starRocks": {"url": "********************************************************************************************************************************************", "username": "root", "password": ""}, "tidb": {"url": "********************************************************************************************************************************************", "username": "root", "password": ""}, "mysql": {"url": "****************************************************************************************************************************************", "username": "root", "password": "1qaz2wsx#EDC"}, "kafka": {"bootstrapServers": "192.168.40.156:9092", "username": "", "password": ""}, "minio": {"endPoint": "http://192.168.40.156:9000", "username": "admin", "password": "password"}, "hdfs": {"hdfsUri": "hdfs://nnCluster"}}