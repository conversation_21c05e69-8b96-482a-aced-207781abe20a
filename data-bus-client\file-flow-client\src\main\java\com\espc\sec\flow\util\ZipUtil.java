package com.espc.sec.flow.util;

import com.espc.sec.flow.bean.Job;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.Zip64Mode;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @date 2018-06-01
 */
@Slf4j
public class ZipUtil {

    /**
     * 压缩文件列表
     *
     * @param list 任务列表
     * @return 返回压缩后的二进制流
     * @throws Exception 抛出异常
     */
    public static byte[] zipJobList(List<Job> list) throws Exception {
        try (ByteArrayOutputStream byteOutputStream = new ByteArrayOutputStream();
             ZipArchiveOutputStream outputStream = new ZipArchiveOutputStream(byteOutputStream)) {
            outputStream.setUseZip64(Zip64Mode.AsNeeded);

            for (Job job : list) {
                zip(job.getContent(), job.getFilePath(), outputStream);
            }

            outputStream.closeArchiveEntry();
            outputStream.finish();
            byte[] bb = byteOutputStream.toByteArray();
            byteOutputStream.close();
            return bb;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 压缩一个文件
     *
     * @param content      文件内容
     * @param path         此文件在压缩文件中的相对路径
     * @param outputStream 文件输出流
     * @throws Exception 抛出异常
     */
    private static void zip(byte[] content, String path, ZipArchiveOutputStream outputStream) throws Exception {
        try {
            ZipArchiveEntry zipArchiveEntry = new ZipArchiveEntry(path);
            zipArchiveEntry.setSize(content.length);
            outputStream.putArchiveEntry(zipArchiveEntry);
            outputStream.write(content);
        } catch (Exception e) {
            log.error("add one file to zip failed.");
            throw e;
        }
    }

    /**
     * 压缩文件列表
     *
     * @param list 任务列表
     * @return 返回压缩后的二进制流
     * @throws Exception 抛出异常
     */
    public static byte[] compress(List<Job> list) throws Exception {
        try (ByteArrayOutputStream byteOutputStream = new ByteArrayOutputStream();
             ZipOutputStream outputStream = new ZipOutputStream(byteOutputStream)) {
            for (Job job : list) {
                compress(outputStream, job.getContent(), job.getFilePath());
            }
            outputStream.close();
            byte[] bb = byteOutputStream.toByteArray();
            byteOutputStream.close();
            return bb;
        } catch (Exception e) {
            throw e;
        }
    }


    /**
     * 压缩一个文件
     *
     * @param out     文件输出流
     * @param content 文件内容
     * @param path    此文件在压缩文件中的相对路径
     * @throws Exception 抛出异常
     */
    private static void compress(ZipOutputStream out, byte[] content, String path) throws Exception {
        ZipEntry entry = new ZipEntry(path);
        entry.setSize(content.length);
        out.putNextEntry(entry);
        out.write(content);
        out.flush();
    }

    /**
     * 将文件打包成zip压缩包文件
     *
     * @param list        需要压缩的文件列表
     * @param targetPath  压缩文件的存放路径
     * @param zipFileName 压缩文件名
     * @throws Exception 抛出异常
     */
    public static void zip(List<File> list, String targetPath, String zipFileName) throws Exception {
        File zipFile = new File(targetPath, zipFileName);
        try (ZipArchiveOutputStream outputStream = new ZipArchiveOutputStream(zipFile)) {
            outputStream.setUseZip64(Zip64Mode.AsNeeded);
            for (File file : list) {
                if (!file.exists()) {
                    continue;
                }
                String path = file.getAbsolutePath().substring(targetPath.length() + 1);
                zip(file, path, outputStream);
            }
            outputStream.closeArchiveEntry();
            outputStream.finish();
        } catch (Exception e) {
            log.error("zip failed.");
            throw e;
        }
    }

    /**
     * 把一个文件加入到压缩文件
     *
     * @param file         文件
     * @param path         此文件在压缩文件中的相对路径
     * @param outputStream 输出流
     * @throws Exception 抛出异常
     */
    private static void zip(File file, String path, ZipArchiveOutputStream outputStream) throws Exception {
        try (InputStream inputStream = new FileInputStream(file)) {
            ZipArchiveEntry zipArchiveEntry = new ZipArchiveEntry(file, path);
            outputStream.putArchiveEntry(zipArchiveEntry);
            long length = file.length();
            byte[] buffer = new byte[(int) length];
            inputStream.read(buffer);
            outputStream.write(buffer);
        } catch (Exception e) {
            log.error("add one file to zip failed.");
            throw e;
        }
    }

}