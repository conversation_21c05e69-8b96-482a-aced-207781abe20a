package com.espc.sec.dataimport.monitor.increment.service;

import com.espc.sec.dataimport.common.vo.PageVo;
import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataReq;
import com.espc.sec.dataimport.monitor.increment.vo.MonitorIncrementDataGroupVo;
import com.espc.sec.dataimport.monitor.increment.vo.MonitorIncrementDataVo;

public interface MonitorIncrementDataService {
    /**
     * 分页
     */
    PageVo<MonitorIncrementDataVo> page(MonitorIncrementDataReq monitorIncrementDataReq);

    /**
     * 插入
     */
    Integer create(MonitorIncrementDataDto dto);
    /**
     * 分组
     */
    PageVo<MonitorIncrementDataGroupVo> group(MonitorIncrementDataReq dto);
}
