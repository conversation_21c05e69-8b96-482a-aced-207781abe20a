package com.espc.sec.dataexport.monitor.increment.vo;

import lombok.Data;

/**
 * @Author: zh
 * @date: 2025/7/25
 */
@Data
public class MonitorIncrementDataGroupVo {

    /**
     * 节点
     */
    private String node;
    /**
     * 数据库类型 如hdfs minio es kafka starrocks
     */
    private String databaseType;
    /**
     * 表类型 对应操作的文件或者数据表名字
     */
    private String tableName;
    /**
     * 对应数量
     */
    private Integer size;
}
