package com.espc.sec.dataexport.tidb.service;

import cn.hutool.core.io.FileUtil;
import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.service.impl.AbstractExporter;
import com.espc.sec.dataexport.common.util.DateUtil;
import com.espc.sec.dataexport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataexport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataexport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataexport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataexport.task.service.DataTaskService;
import com.espc.sec.dataexport.tidb.dto.TidbExportDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * TiDB导出器
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Slf4j
@Service
public class TidbExporter extends AbstractExporter<TidbExportDto> {

    @Autowired
    private TidbHelper tidbHelper;

    @Autowired
    private MonitorTaskDataService monitorTaskDataService;

    @Autowired
    private MonitorIncrementDataService monitorIncrementDataService;

    @Autowired
    private DataTaskService dataTaskService;

    @Override
    protected List<File> doExport(TidbExportDto exporter) throws Exception {
        // 1.数据分页参数
        int offset = 0;
        int pageSize = 1000;
        // 2.文件大小，以及是否需要创建新文件
        long currentSize = 0;
        int fileIndex = 1;
        // 3.一个文件最大能装多少
        int maxFileSize = Config.commonConfig.getMaxTempFileSizeMb() * (1024 * 1024);

        List<String> tmpFileList = new ArrayList<>();

        String fileName = generateIncrementalFileNameForTableForTidb(exporter);

        String tempFile = exporter.getExportTempPath();

        // 4.临时文件
        Path filePath = createFilePath(tempFile, fileName, fileIndex);
        tmpFileList.add(filePath.toString());
        Writer writer = null;
        try {
            writer = createWriter(filePath);
            while (true) {
                // 5.具体数据行
                List<Map<String, Object>> pageData = tidbHelper.queryDataForTable(exporter.getTableName(),
                        exporter.getTimeField(),
                        exporter.getStartTime(), exporter.getEndTime(), offset, pageSize);
                if (pageData.isEmpty()) {
                    break;
                }
                // 6.写入每行数据到文件
                for (Map<String, Object> pageDatum : pageData) {
                    String insertSql = tidbHelper
                            .generateInsertSql(exporter.getTableName(), pageDatum)
                            .trim() + ";";

                    byte[] recordBytes = (insertSql + "\n").getBytes(StandardCharsets.UTF_8);
                    int recordSize = recordBytes.length;
                    // 7.如果当前数据已经超过了规定文件大小，需要重新创建一个新的文件写
                    if (currentSize + recordSize > maxFileSize) {
                        writer.close();

                        fileIndex++;
                        // 8.新的文件writer
                        filePath = createFilePath(tempFile, fileName, fileIndex);
                        writer = createWriter(filePath);
                        // 9.存入需要处理的tmp文件
                        tmpFileList.add(filePath.toString());
                    }
                    writeLine(writer, insertSql);
                    currentSize += recordSize;
                }
                offset += pageData.size();
            }
        } finally {
            if (writer != null) {
                try {
                    writer.close();
                } catch (IOException e) {
                    log.error("文件流关闭失败", e);
                }
            }
        }

        return tmpFileList.stream().map(File::new).collect(Collectors.toList());
    }

    @Override
    protected void saveImportLogToMysql(TidbExportDto exporter, File tempFile) throws Exception {
        String startTimeStr = DateUtil.format(exporter.getStartTime(), Constants.MILLS_FORMAT);
        String nodeName = Config.commonConfig.getNodeName();
        String databaseType = exporter.getExportTypeEnum().getCode();
        String tableName = exporter.getTableName();
        Long size = (long) FileUtil.readLines(tempFile, StandardCharsets.UTF_8).size();
        String fileName = tempFile.getName();
        Integer exportMode = exporter.getExportModeEnum().getCode();

        if (ExportModeEnum.INCREMENT.getCode().equals(exportMode)) {
            MonitorIncrementDataDto dto = new MonitorIncrementDataDto();
            dto.setNode(nodeName);
            dto.setDatabaseType(databaseType);
            dto.setTableName(tableName);
            dto.setFileName(fileName);
            dto.setSize(size);
            dto.setTime(exporter.getStartTime());
            monitorIncrementDataService.create(dto);
        } else {
            String taskName = "tidb_task";
            if (exporter.getTaskId() != null) {
                taskName = dataTaskService.getTaskNameByIdFromMemoryCache(exporter.getTaskId());
            }

            MonitorTaskDataAddReq req = new MonitorTaskDataAddReq();
            req.setNode(nodeName);
            req.setTaskName(taskName);
            req.setDatabaseType(databaseType);
            req.setTableName(tableName);
            req.setSize(size);
            req.setTime(startTimeStr);
            req.setFileName(fileName);
            monitorTaskDataService.add(req);
        }
    }

    /**
     * TiDB分库分表文件名格式：1_20220920164916873_shanghai_tidb_probe-center_probe-info.log
     */
    private String generateIncrementalFileNameForTableForTidb(TidbExportDto exporter) {
        String startTimeStr = DateUtil.format(exporter.getStartTime(), Constants.MILLS_FORMAT);
        boolean isTaskType = exporter.getExportModeEnum().equals(ExportModeEnum.TASK);

        String nodeName = Config.commonConfig.getNodeName();
        return String.format("%d_%d_%s_%s_%s_%s_%s"
                , exporter.getExportModeEnum().getCode()
                , isTaskType ? (exporter.getTaskId() != null ? exporter.getTaskId() : 0) : 0
                , startTimeStr
                , nodeName
                , exporter.getExportTypeEnum().getCode()
                , exporter.getDatabaseName()
                , exporter.getTableName()
        );
    }

    /**
     * 创建文件。如果超出这儿会用fileIndex区分
     */
    private Path createFilePath(String exportPath, String fileName, int fileIndex) {
        return Paths.get(exportPath,
                String.format("%s_%d.log", fileName, fileIndex));
    }

    /**
     * 创建文件写入Writer
     */
    private Writer createWriter(Path file) throws IOException {
        return Files.newBufferedWriter(file, StandardCharsets.UTF_8);
    }

    /**
     * 写入数据
     */
    private void writeLine(Writer writer, String line) throws IOException {
        writer.write(line);
        writer.write("\n");
    }
}