package com.espc.sec.dataexport.monitor.increment.dto;

import com.espc.sec.dataexport.common.enums.ErrorCodeEnum;
import com.espc.sec.dataexport.common.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 增量导入导出监控DTO
 */
@Valid
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MonitorIncrementDataDto {


    /**
     * 节点
     */
    @NotBlank(message = "节点不能为空")
    private String node;
    /**
     * 数据库类型 如hdfs minio es kafka starrocks
     */
    @NotBlank(message = "数据库类不能为空")
    private String databaseType;
    /**
     * 表类型 对应操作的文件或者数据表名字
     */
    @NotBlank(message = "表类型不能为空")
    private String tableName;
    /**
     * 文件名字。上级结构
     */
    private String fileName;
    /**
     * 导出或者导入的条数
     */
    private Long size;
    /**
     * 导入导出的时间
     */
    @NotNull(message = "时间不能为空")
    private Date time;

    public void check() {
        if (StringUtils.isBlank(node)) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "节点不能为空");
        }
        if (StringUtils.isBlank(databaseType)) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "数据库类不能为空");
        }
        if (StringUtils.isBlank(tableName)) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "表类型不能为空");
        }
        if (time == null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "时间不能为空");
        }
    }
}
