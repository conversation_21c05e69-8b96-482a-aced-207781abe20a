package com.espc.sec.dataexport.task.service.impl;

import cn.hutool.core.date.DateTime;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.common.enums.TaskStatusEnum;
import com.espc.sec.dataexport.common.util.DateUtil;
import com.espc.sec.dataexport.task.entity.DataTaskPo;
import com.espc.sec.dataexport.task.service.DataTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.espc.sec.dataexport.common.constant.Constants.YYYY_MM_DD_HH_MM_SS;

/**
 * 任务监测服务
 */
@Component
@Slf4j
public class DataTaskMonitor {

    @Autowired
    private DataTaskService taskService;

    /**
     * 待执行任务监测
     * 执行时间小于(当前时间+一个小时)，将状态由待执行变为执行失败
     */
    @Scheduled(cron = "0 */30 * * * ?")
    public void pendingTaskMonitor() {
        log.info(LogKeyword.QUARTZ_DATA_TASK + "待执行异常任务扫描开始");
        List<DataTaskPo> pendingTasks = taskService.listTasksByStatus(TaskStatusEnum.PENDING);
        updateTask(pendingTasks, -60, TaskStatusEnum.PENDING);
    }

    /**
     * 执行中任务监测
     * 长期处于执行中的任务(执行超过两个小时)，将任务由执行中变为执行失败
     */
    @Scheduled(cron = "0 */30 * * * ?")
    public void executingTaskMonitor() {
        log.info(LogKeyword.QUARTZ_DATA_TASK + "执行中异常任务扫描开始");
        List<DataTaskPo> executingTasks = taskService.listTasksByStatus(TaskStatusEnum.EXECUTING);
        updateTask(executingTasks, -120, TaskStatusEnum.EXECUTING);
    }

    private void updateTask(List<DataTaskPo> tasks, Integer minite, TaskStatusEnum statusEnum) {
        Date now = new Date();
        List<Integer> ids = tasks.stream()
                .filter(taskPo -> {
                    DateTime cronDate = DateUtil.parse(DateUtil.cronToDateTime(taskPo.getCron()), YYYY_MM_DD_HH_MM_SS);
                    return cronDate.before(DateUtil.offsetMinute(now, minite));
                })
                .map(DataTaskPo::getId)
                .collect(Collectors.toList());
        ids.forEach(id -> taskService.updateStatus(id, TaskStatusEnum.FAILED));
        // 通知服务端,告知失败

        log.info(LogKeyword.QUARTZ_DATA_TASK + statusEnum + "异常任务,更新任务为失败,ids:{}", ids);
    }


}
