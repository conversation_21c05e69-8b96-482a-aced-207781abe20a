package com.espc.sec.dataexport.elasticsearch.service;


import cn.hutool.core.date.DateTime;
import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.dto.Tricycle;
import com.espc.sec.dataexport.common.dto.Tuple;
import com.espc.sec.dataexport.common.dto.task.ElasticsearchTaskProperties;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import com.espc.sec.dataexport.common.service.CheckPointService;
import com.espc.sec.dataexport.common.service.IncrementExportService;
import com.espc.sec.dataexport.common.service.TaskExportService;
import com.espc.sec.dataexport.common.util.DateUtil;
import com.espc.sec.dataexport.common.util.MultiThreadUtil;
import com.espc.sec.dataexport.elasticsearch.dto.ElasticsearchExportDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;

/**
 * Elasticsearch导出服务
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class ElasticsearchExportServiceImpl implements IncrementExportService, TaskExportService<ElasticsearchTaskProperties> {

    @Autowired
    private CheckPointService checkPointService;

    @Autowired
    private ElasticsearchExporter elasticsearchExporter;

    @Override
    public void incrementExport() {
        List<String> indexNames = Config.exportConfig.elasticsearch.getIndexName();
        if (indexNames == null || indexNames.isEmpty()) {
            log.warn("没有配置索引名称，跳过导出");
            return;
        }

        for (String indexName : indexNames) {

            Tricycle<Integer, Date, Date> idAndTimeRange = checkPointService.getTimeRangeForIndex(ExportTypeEnum.ES,indexName);
            if (idAndTimeRange == null) {
                continue;
            }

            try {
                ElasticsearchExportDto elasticsearchExportDto = new ElasticsearchExportDto();
                elasticsearchExportDto.setExportModeEnum(ExportModeEnum.INCREMENT);
                elasticsearchExportDto.setExportTypeEnum(ExportTypeEnum.ES);
                elasticsearchExportDto.setStartTime(idAndTimeRange.getY());
                elasticsearchExportDto.setEndTime(idAndTimeRange.getZ());
                elasticsearchExportDto.setIndexName(indexName);

                elasticsearchExporter.export(elasticsearchExportDto);
                checkPointService.update(idAndTimeRange.getX(), idAndTimeRange.getZ());
            } catch (Exception e) {
                log.error("es导出错误", e);
            }
        }
    }

    /**
     * 根据任务导出数据
     */
    @Override
    public void taskExport(ElasticsearchTaskProperties request) {

        // 并行导出所有索引
        List<Callable<Void>> tasks = new ArrayList<>();
        for (String indexName : request.getIndexName()) {
            DateTime startTime = DateUtil.parse(request.getDataStartTime(), Constants.YYYY_MM_DD_HH_MM_SS);
            DateTime endTime = DateUtil.parse(request.getDataEndTime(), Constants.YYYY_MM_DD_HH_MM_SS);
            tasks.add(() -> {
                ElasticsearchExportDto elasticsearchExportDto = new ElasticsearchExportDto();
                elasticsearchExportDto.setExportModeEnum(ExportModeEnum.TASK);
                elasticsearchExportDto.setExportTypeEnum(ExportTypeEnum.ES);
                elasticsearchExportDto.setStartTime(startTime);
                elasticsearchExportDto.setEndTime(endTime);
                elasticsearchExportDto.setIndexName(indexName);
                elasticsearchExportDto.setTaskId(request.getTaskId());

                elasticsearchExporter.export(elasticsearchExportDto);
                return null;
            });

        }
        MultiThreadUtil.executeAll(tasks);
    }

}