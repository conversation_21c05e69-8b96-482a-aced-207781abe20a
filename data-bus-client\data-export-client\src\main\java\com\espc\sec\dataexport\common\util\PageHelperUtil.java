package com.espc.sec.dataexport.common.util;


import com.espc.sec.dataexport.common.dto.PageDto;
import com.espc.sec.dataexport.common.vo.PageVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.function.Supplier;

/**
 * 分页工具
 *
 * <AUTHOR>
 */
public class PageHelperUtil {
    /**
     * 分页
     *
     * @param pageDto  分页参数
     * @param supplier 查询提供者
     * @param <T>
     * @return
     */
    public static <T> PageVo<T> startPage(PageDto pageDto, Supplier<List<T>> supplier) {
        PageHelper.startPage(pageDto.getPageNo(), pageDto.getPageSize());
        List<T> ts = supplier.get();
        PageInfo<T> pageInfo = new PageInfo<>(ts);
        PageVo<T> pageVo = new PageVo<>();
        pageVo.setPageNo(pageDto.getPageNo());
        pageVo.setPageSize(pageDto.getPageSize());
        pageVo.setTotal(pageInfo.getTotal());
        pageVo.setList(ts);
        return pageVo;
    }
}
