package com.espc.sec.flow.util;

import com.espc.sec.flow.bean.TempFile;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;


/**
 * <AUTHOR>
 * @date 2018-06-04
 */
public class Constant {
    /**
     * 服务端接收标准文件的接口编号
     */
    public static final int FLAG_SEND_NORMAL_FILE = 101;
    /**
     * 服务端接收压缩文件的接口编号
     */
    public static final int FLAG_SEND_ZIP_FILE = 102;

    public static final BlockingQueue<TempFile> FILE_QUEUE = new ArrayBlockingQueue(ConfigUtil.config.getFileQueueSize());

}