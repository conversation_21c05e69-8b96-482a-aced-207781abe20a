package com.espc.sec.dataexport.hdfs.service;

import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.common.dto.task.HdfsTaskProperties;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import com.espc.sec.dataexport.common.service.IncrementExportService;
import com.espc.sec.dataexport.common.service.TaskExportService;
import com.espc.sec.dataexport.common.util.MultiThreadUtil;
import com.espc.sec.dataexport.hdfs.dto.HdfsExportDto;
import com.espc.sec.dataexport.hdfs.entity.HdfsBatchExportResult;
import com.espc.sec.dataexport.hdfs.helper.HdfsServiceHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.fs.FileStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * hdfs导出服务主类
 */
@Slf4j
@Service
public class HdfsExportServiceImpl implements TaskExportService<HdfsTaskProperties>, IncrementExportService {
    @Autowired
    private HdfsExporter hdfsExporter;

    /**
     * 根据参数导出数据（新增接口支持）
     *
     * @param request 导出请求参数
     * @return 导出结果
     */
    @Override
    public void taskExport(HdfsTaskProperties request) {

        List<String> fileDateList = new ArrayList<>();
        //毫秒时间
        String dataStartTime = DateUtil.format(DateUtil.parse(request.getDataStartTime()), Constants.MILLS_FORMAT);
        String dataEndTime = DateUtil.format(DateUtil.parse(request.getDataEndTime()), Constants.MILLS_FORMAT);
        //计算文件夹时间
        LocalDate fileNameDateStart = LocalDateTime.parse(request.getDataStartTime(), Constants.TIME_FORMATTER).toLocalDate();
        LocalDate fileNameDateEnd = LocalDateTime.parse(request.getDataEndTime(), Constants.TIME_FORMATTER).toLocalDate();

        Integer dataSize = request.getDataSize();
        //获取所有对应日期文件夹
        while (!fileNameDateStart.isAfter(fileNameDateEnd) && dataSize > 0) {
            String datePath = buildDatePath(fileNameDateStart);
            if (HdfsServiceHelper.exists(datePath)) {
                fileDateList.add(datePath);
            }
            fileNameDateStart = fileNameDateStart.plusDays(1);
        }
        //没有匹配到文件夹
        if (fileDateList.isEmpty()) {
            log.info("{}当前任务没有匹配到文件", LogKeyword.HDFS_EXPORT);
            return;
        }

        log.info("共：{}个目标日期文件夹内容需要导出", fileDateList.size());
        //批量导出
        syncExportDataWithParams(fileDateList, dataStartTime, dataEndTime, dataSize,request.getTaskId());
    }

    @Override
    public void incrementExport(String kafkaMessage) throws Exception {
        HdfsExportDto hdfsExportDto = new HdfsExportDto();
        hdfsExportDto.setExportModeEnum(ExportModeEnum.INCREMENT);
        hdfsExportDto.setExportTypeEnum(ExportTypeEnum.HDFS);
        hdfsExportDto.setHdfsAbsPath(kafkaMessage);
        hdfsExporter.export(hdfsExportDto);
    }

    /**
     * 异步导出指定文件夹内容文件，文件名需要符合时间范围
     *
     * @param dirList       文件夹集合
     * @param dateStartTime 开始时间
     * @param dataEndTime   结束时间
     */
    private HdfsBatchExportResult syncExportDataWithParams(List<String> dirList, String dateStartTime, String dataEndTime, Integer dataSize,Integer taskId) {
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        HdfsBatchExportResult hdfsBatchExportResult = new HdfsBatchExportResult();
        AtomicInteger success = new AtomicInteger(0);
        AtomicInteger dataCount = new AtomicInteger(dataSize);

        //遍历符合条件的文件夹
        for (String fileDate : dirList) {
            List<FileStatus> fileStatuses = HdfsServiceHelper.listFiles(fileDate);
            for (FileStatus fileStatus : fileStatuses) {
                //已到达目标值
                if (dataCount.get() <= 0) {
                    break;
                }
                futures.add(CompletableFuture.runAsync(() -> {
                    if (dataCount.get() <= 0) {
                        return;
                    }
                    //具体文件
                    String originalName = fileStatus.getPath().getName();
                    String hdfsDirFilePath = fileStatus.getPath().getParent().toString();
                    //对应时间
                    String fileDateStr = originalName.substring(0, 17);
                    if (isTimeInRange(fileDateStr, dateStartTime, dataEndTime) && originalName.endsWith(".pcap")) {
                        //执行导出
                        HdfsExportDto singalHdfs = new HdfsExportDto();
                        singalHdfs.setExportModeEnum(ExportModeEnum.TASK);
                        singalHdfs.setExportTypeEnum(ExportTypeEnum.HDFS);
                        singalHdfs.setTaskId(taskId);
                        singalHdfs.setHdfsAbsPath(hdfsDirFilePath + File.separator + originalName);

                        try {
                            if (dataCount.get() > 0) {
                                //不管是导出成功还是失败，只要符合条件这儿都需要减一，需要先减去，防止其他线程在导出期间导出不同的任务
                                dataCount.decrementAndGet();
                                hdfsExporter.export(singalHdfs);
                                success.incrementAndGet();
                            }
                        } catch (Exception e) {
                            log.error("导出失败文件名：{}{}，时间范围：{}-{}", hdfsDirFilePath, originalName, dateStartTime, dataEndTime);
                            log.error("导出失败：", e);
                        }
                    }
                }, MultiThreadUtil.executorService));
            }
        }

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        hdfsBatchExportResult.setSuccessCount(success.get());
        hdfsBatchExportResult.setAllCount(dataSize);
        return hdfsBatchExportResult;
    }

    /**
     * 构建带日期的路径
     *
     * @param fileDate 对应yyyy-MM-dd的日期
     * @return
     */
    public String buildDatePath(LocalDate fileDate) {

        String year = Constants.YEAR_FORMAT.format(fileDate);
        String month = Constants.MONTH_FORMAT.format(fileDate);
        String day = Constants.DAY_FORMAT.format(fileDate);
        return Config.exportConfig.hdfs.getListenPath() + "/" + year + "/" + month + "/" + day;
    }

    /**
     * 目标值是否在指定范围内
     *
     * @param target 目标值
     * @param start  开始范围
     * @param end    结束范围
     * @return
     */
    public boolean isTimeInRange(String target, String start, String end) {
        // 检查所有参数非空且长度均为17位
        if (StringUtils.isAnyBlank(target, start, end)) {
            throw new IllegalArgumentException("输入参数不能为null");
        }
        if (target.length() != 17 || start.length() != 17 || end.length() != 17) {
            throw new IllegalArgumentException("时间格式不正确");
        }

        // 字典序比较：满足 start <= target <= end 即在范围内
        return target.compareTo(start) >= 0 && target.compareTo(end) <= 0;
    }
}
