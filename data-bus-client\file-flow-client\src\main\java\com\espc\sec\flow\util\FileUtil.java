package com.espc.sec.flow.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2019-05-06
 */
@Slf4j
public class FileUtil {

    /**
     * 移动文件
     *
     * @param src  原始文件
     * @param dest 目标文件
     * @throws Exception 抛出异常
     */
    public static void moveFile(File src, File dest) throws Exception {
        for (int i = 0; i < 3; i++) {
            if (src.renameTo(dest)) {
                return;
            }
        }
        FileUtils.moveFile(src, dest);
    }
}
