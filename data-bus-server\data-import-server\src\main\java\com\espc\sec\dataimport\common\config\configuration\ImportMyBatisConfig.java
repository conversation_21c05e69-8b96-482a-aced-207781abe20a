package com.espc.sec.dataimport.common.config.configuration;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * Import MyBatis 配置
 * 使用 mysqlDataSource 数据源
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Configuration
@MapperScan(
        basePackages = "com.espc.sec.dataimport.**.mapper",
        sqlSessionFactoryRef = "importSqlSessionFactory"
)
public class ImportMyBatisConfig {

    /**
     * MyBatis Plus 分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 添加分页插件
        PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        // 设置最大单页限制数量，默认 500 条，-1 不受限制
        paginationInterceptor.setMaxLimit(500L);
        interceptor.addInnerInterceptor(paginationInterceptor);
        return interceptor;
    }

    /**
     * Import SqlSessionFactory
     */
    @Bean("importSqlSessionFactory")
    public SqlSessionFactory importSqlSessionFactory(
            @Qualifier("mysqlDataSource") DataSource dataSource
    ) throws Exception {
        MybatisSqlSessionFactoryBean sessionFactory = new MybatisSqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);

        // MyBatis 配置
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setLogImpl(org.apache.ibatis.logging.stdout.StdOutImpl.class);
        sessionFactory.setConfiguration(configuration);

        // 添加分页插件
        sessionFactory.setPlugins(mybatisPlusInterceptor());

        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources("classpath:mapper/*.xml"));

        log.info("Import SqlSessionFactory 配置完成，使用 mysqlDataSource");
        return sessionFactory.getObject();
    }

    /**
     * Import SqlSessionTemplate
     */
    @Bean("importSqlSessionTemplate")
    public SqlSessionTemplate importSqlSessionTemplate(
            @Qualifier("importSqlSessionFactory") SqlSessionFactory sqlSessionFactory
    ) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}