package com.espc.sec.dataexport.common.config;

import lombok.Data;

import java.util.List;


@Data
public class ExportConfig {
    public Elasticsearch elasticsearch;
    public StarRocks starRocks;
    public Tidb tidb;
    public Kafka kafka;
    public Minio minio;
    public Hdfs hdfs;

    @Data
    public static class Elasticsearch {
        private String cron;
        private List<String> indexName;
        private String timeField;
        private Boolean incrementEnable;
    }

    @Data
    public static class StarRocks {
        private String cron;
        private List<Database> database;
        private Boolean incrementEnable;

        @Data
        public static class Database {
            private String databaseName;
            private List<String> tableName;
            private String timeField;
        }
    }

    @Data
    public static class Tidb {
        private String cron;
        private List<Database> database;
        private Boolean incrementEnable;

        @Data
        public static class Database {
            private String databaseName;
            private List<String> tableName;
            private String timeField;
        }
    }

    @Data
    public static class Kafka {
        private String cron;
        private List<String> topicName;
        private Boolean incrementEnable;
    }

    @Data
    public static class Minio {
        private List<Bucket> buckets;
        private Boolean incrementEnable;

        @Data
        public static class Bucket {
            /**
             * 前缀
             */
            private String prefix;
            /**
             * 模式 d：天 m：月 y：年,与prefix用"-"拼接,如果为空则只使用prefix
             */
            private String mode;
        }
    }

    @Data
    public static class Hdfs {
        private Boolean incrementEnable;
        private String listenPath;
    }
}
