package com.espc.sec.dataimport.netty.handler;

import com.espc.sec.dataimport.netty.entity.MessageWrapper;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;

/**
 * <AUTHOR>
 * @date 2025/7/17
 **/
public class MessageEncoder extends MessageToByteEncoder<MessageWrapper> {
    @Override
    protected void encode(ChannelHandlerContext ctx, MessageWrapper msg, ByteBuf out) {
        out.writeInt(msg.getType().getCode());
        out.writeInt(msg.getOperateType().ordinal());
        out.writeInt(msg.getLength());

        if (msg.getBody() != null) {
            out.writeBytes(msg.getBody());
        }
    }
}
