package com.espc.sec.dataimport.tidb.mapper;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

/**
 * TiDB数据导入Mapper
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Mapper
public interface TidbImportMapper {

    /**
     * 测试连接 - 查询当前数据库
     */
    @Select("SELECT DATABASE() as current_database")
    Map<String, Object> getCurrentDatabase();

    /**
     * 测试查询 - 查询表数量
     */
    @Select("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = #{databaseName}")
    Map<String, Object> getTableCount(@Param("databaseName") String databaseName);

    /**
     * 检查表是否存在
     */
    @Select("SELECT COUNT(*) as table_exists FROM information_schema.tables WHERE table_schema = #{databaseName} AND table_name = #{tableName}")
    Map<String, Object> checkTableExists(@Param("databaseName") String databaseName, @Param("tableName") String tableName);

    /**
     * 执行动态SQL - 用于INSERT语句
     */
    @Insert("${sql}")
    int executeDynamicInsert(@Param("sql") String sql);

    /**
     * 执行动态SQL - 用于UPDATE语句
     */
    @Update("${sql}")
    int executeDynamicUpdate(@Param("sql") String sql);

    /**
     * 执行REPLACE INTO语句
     */
    @Insert("${sql}")
    int executeReplace(@Param("sql") String sql);

    /**
     * 查询表的主键信息
     */
    @Select("SELECT COLUMN_NAME FROM information_schema.KEY_COLUMN_USAGE WHERE table_schema = #{databaseName} AND table_name = #{tableName} AND CONSTRAINT_NAME = 'PRIMARY' ORDER BY ORDINAL_POSITION")
    List<String> getPrimaryKeyColumns(@Param("databaseName") String databaseName, @Param("tableName") String tableName);

    /**
     * 简单查询测试
     */
    @Select("SELECT * FROM ${tableName} LIMIT ${limit}")
    List<Map<String, Object>> simpleQuery(
            @Param("tableName") String tableName,
            @Param("limit") int limit
    );

    /**
     * 设置自动提交
     */
    @Select("SET autocommit = #{autocommit}")
    void setAutocommit(@Param("autocommit") int autocommit);

    /**
     * 切换数据库
     */
    @Select("USE `${databaseName}`")
    void useDatabase(@Param("databaseName") String databaseName);
}