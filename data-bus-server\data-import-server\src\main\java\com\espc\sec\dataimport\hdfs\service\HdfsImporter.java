package com.espc.sec.dataimport.hdfs.service;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.constant.Constants;
import com.espc.sec.dataimport.common.enums.ImportModeEnum;
import com.espc.sec.dataimport.common.enums.ImportTypeEnum;
import com.espc.sec.dataimport.common.service.AbstractImporter;
import com.espc.sec.dataimport.hdfs.helper.HdfsServiceHelper;
import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataimport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataimport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataimport.task.service.DataTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;

import static com.espc.sec.dataimport.common.constant.Constants.MILLS_FORMAT;

@Service
@Slf4j
public class HdfsImporter extends AbstractImporter {

    @Autowired
    private MonitorIncrementDataService monitorIncrementDataService;

    @Autowired
    private MonitorTaskDataService monitorTaskDataService;

    @Autowired
    private DataTaskService dataTaskService;
    ///data/import/hdfs/1_1_20220920164916873_shanghai_1000001213_00001.pcap
    @Override
    protected void doImport(File file) throws Exception {

        String hdfsPath = getNameByNodeConfig(file);

        HdfsServiceHelper.uploadFile(file.getAbsolutePath()
                , hdfsPath
                , true
        );
        saveImportLogToMysql(file.getName(),hdfsPath, file.length()
                //其他参数
        );
    }

    /**
     * 保存导入日志
     * @param fileName 文件名1_0_20220920164916873_shanghai_1000001213_00001.pcap
     *        hdfsPath 文件路径 /data/import/hdfs/1_0_20220920164916873_shanghai_1000001213_00001.pcap
     */
    private void saveImportLogToMysql(String fileName, String hdfsPath, Long fileSzie){

        //1_1_20220920164916873_shanghai_1000001213_00001.pcap
        String[] objectNameSplit = fileName.split("_");
        String importMode = null;
        if (objectNameSplit.length > 1) {
            importMode = objectNameSplit[0];

        }
        if (importMode != null && importMode.equals(ImportModeEnum.increment.getCode().toString())) {
            MonitorIncrementDataDto dataDto = new MonitorIncrementDataDto();
            dataDto.setNode(objectNameSplit[3]);
            dataDto.setDatabaseType(ImportTypeEnum.HDFS.getCode());
            dataDto.setTableName(hdfsPath);
            dataDto.setTime(new Date());
            dataDto.setSize(fileSzie);
            dataDto.setFileName(fileName);

            monitorIncrementDataService.create(dataDto);
        } else {
            String taskName = dataTaskService.getTaskNameByIdFromMemoryCache(Integer.parseInt(objectNameSplit[1]));

            MonitorTaskDataAddReq monitorTaskDataAddReq = new MonitorTaskDataAddReq();
            monitorTaskDataAddReq.setTaskName(taskName);
            monitorTaskDataAddReq.setDatabaseType(ImportTypeEnum.HDFS.getCode());
            monitorTaskDataAddReq.setNode(objectNameSplit[3]);
            monitorTaskDataAddReq.setTableName(hdfsPath);
            monitorTaskDataAddReq.setTime(DateUtil.format(new Date(), Constants.YYYY_MM_DD_HH_MM_SS));
            monitorTaskDataAddReq.setSize(fileSzie);
            monitorTaskDataAddReq.setFileName(fileName);
            monitorTaskDataService.add(monitorTaskDataAddReq);
        }

    }


    /**
     * 根据配置加载节点信息
     * @param file file
     * @return
     */
    private String getNameByNodeConfig(File file) {
        String[] fileNameSplit = file.getName().split("_");
        String dateStr = fileNameSplit[2];
        String node = fileNameSplit[3];

        DateTime dateTime = DateUtil.parse(dateStr, MILLS_FORMAT);

        int year = dateTime.year();
        int month = dateTime.month() + 1;
        int day = dateTime.dayOfMonth();
        if (Config.importConfig.hdfs.getDataNodeEnable()) {
            return Config.importConfig.hdfs.getHdfsUploadPath() + "/" + node + "/" + year + "/" + month + "/" + day + "/" + file.getName();
        }
        return Config.importConfig.hdfs.getHdfsUploadPath() + "/" + year + "/" + month + "/" + day + "/" + file.getName();
    }
}
