package com.espc.sec.dataexport.metadata.service;

import com.espc.sec.dataexport.metadata.entity.DataTableConfigPo;
import com.espc.sec.dataexport.metadata.entity.DataTableFieldConfigPo;

import java.util.List;

/**
 * 元数据存储服务接口
 *
 * <AUTHOR>
 * @date 2025-08-29
 */
public interface MetadataStorageService {
    
    /**
     * 保存数据表配置
     */
    void saveDataTableConfig(DataTableConfigPo dataTableConfig);
    
    /**
     * 批量保存数据表字段配置
     */
    void saveDataTableFieldConfigs(List<DataTableFieldConfigPo> fieldConfigs);
    
    /**
     * 根据数据类型、数据键和节点查询数据表配置
     */
    DataTableConfigPo getDataTableConfig(String dataType, String dataKey, String node);
    
    /**
     * 根据数据表配置ID查询字段配置列表
     */
    List<DataTableFieldConfigPo> getDataTableFieldConfigs(Integer dataTableConfigId);
    
    /**
     * 存储元数据信息（包含表配置和字段配置）
     */
    void storeMetadata(String dataType, String dataKey, String node, List<String> fieldNames, List<String> fieldTypes);
}