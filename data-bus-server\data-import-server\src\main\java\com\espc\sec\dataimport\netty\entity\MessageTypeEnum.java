package com.espc.sec.dataimport.netty.entity;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/7/17
 **/
public enum MessageTypeEnum {

    HEART_BEAT(101, "heartbeat"),
    TASK(102, "task"),
    FILE(103, "file");

    @Getter
    private int code;
    @Getter
    private String value;

    MessageTypeEnum(int code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 通过code获取枚举
     *
     * @param code
     * @return
     */
    public static MessageTypeEnum getMessageTypeEnum(int code) {
        MessageTypeEnum[] values = MessageTypeEnum.values();
        for (MessageTypeEnum value : values) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
