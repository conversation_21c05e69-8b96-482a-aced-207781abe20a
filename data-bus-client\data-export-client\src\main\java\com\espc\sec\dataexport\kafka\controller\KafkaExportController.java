package com.espc.sec.dataexport.kafka.controller;

import com.espc.sec.dataexport.common.dto.task.KafkaTaskProperties;
import com.espc.sec.dataexport.kafka.service.KafkaExportServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Kafka导出控制器
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/export/kafka")
public class KafkaExportController {

    @Autowired
    private KafkaExportServiceImpl kafkaExportServiceImpl;

    /**
     * 根据参数导出Kafka数据
     *
     * @param request 导出请求参数
     * @return 导出结果
     */
    @PostMapping("/export")
    public void exportData(@RequestBody KafkaTaskProperties request) {
        kafkaExportServiceImpl.taskExport(request);
    }
}