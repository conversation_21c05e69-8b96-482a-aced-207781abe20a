package com.espc.sec.dataexport.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 数据表字段配置实体类
 *
 * <AUTHOR>
 * @date 2025-08-29
 */
@Data
@TableName("data_table_field_config")
public class DataTableFieldConfigPo {
    
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * data_table_config.id
     */
    private Integer dataTableConfigId;
    
    /**
     * 字段名字
     */
    private String fieldName;
    
    /**
     * 字段类型
     */
    private String fieldType;
}