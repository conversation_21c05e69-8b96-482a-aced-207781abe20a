package com.espc.sec.dataexport.tidb.service;

import com.espc.sec.dataexport.common.util.DateUtil;
import com.espc.sec.dataexport.tidb.mapper.TidbDataMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * TiDB数据库操作辅助类 - 使用MyBatis实现
 * 支持任意表的数据查询和处理，具备完整的数据类型处理能力
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Slf4j
@Service
public class TidbHelper {

    @Autowired
    @Qualifier("tidbSqlSessionTemplate")
    private SqlSessionTemplate tidbSqlSessionTemplate;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private TidbDataMapper tidbDataMapper;
    
    @PostConstruct
    public void init() {
        // 手动获取TidbDataMapper，确保使用正确的SqlSessionTemplate
        this.tidbDataMapper = tidbSqlSessionTemplate.getMapper(TidbDataMapper.class);
        log.info("TidbHelper初始化完成，使用TiDB SqlSessionTemplate");
        
        // 验证连接
        try {
            Map<String, Object> dbInfo = tidbDataMapper.getCurrentDatabase();
            log.info("TidbHelper连接验证成功，当前数据库: {}", dbInfo);
        } catch (Exception e) {
            log.error("TidbHelper连接验证失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 查询表数据 - 使用MyBatis实现
     */
    public List<Map<String, Object>> queryDataForTable(String tableName, String timeField,
                                                       Date startTime, Date endTime,
                                                       int offset, int pageSize) {
        
        // 定义日期格式
        final String NEW_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

        // 将 Date 对象格式化为字符串格式
        String formattedStartTime = DateUtil.format(startTime, NEW_DATETIME_FORMAT);
        String formattedEndTime = DateUtil.format(endTime, NEW_DATETIME_FORMAT);

        try {
            log.info("使用MyBatis查询TiDB数据");
            log.info("查询参数: tableName={}, timeField={}, startTime={}, endTime={}, pageSize={}, offset={}", 
                    tableName, timeField, formattedStartTime, formattedEndTime, pageSize, offset);
            
            // 先测试连接
            testConnection();
            
            // **使用MyBatis查询数据 - 先尝试XML方式，失败则使用注解方式**
            List<Map<String, Object>> result;
            try {
                log.info("尝试使用XML映射查询...");
                result = tidbDataMapper.queryDataForTable(
                        tableName, timeField, formattedStartTime, formattedEndTime, pageSize, offset
                );
                log.info("XML映射查询成功");
            } catch (Exception e) {
                log.warn("XML映射查询失败，尝试使用注解方式: {}", e.getMessage());
                result = tidbDataMapper.queryDataForTableByAnnotation(
                        tableName, timeField, formattedStartTime, formattedEndTime, pageSize, offset
                );
                log.info("注解方式查询成功");
            }
            
            log.info("MyBatis查询完成，结果数量: {}", result.size());
            
            // 打印第一条数据作为示例（如果有数据的话）
            if (result.size() > 0) {
                Map<String, Object> firstRow = result.get(0);
                log.info("第一条数据示例: {}", firstRow);
                log.info("第一条数据字段数量: {}", firstRow.size());
            }
            
            return result;

        } catch (Exception e) {
            log.error("MyBatis查询TiDB数据失败: tableName={}, startTime={}, endTime={}, offset={}, limit={}",
                    tableName, startTime, endTime, offset, pageSize, e);
            throw new RuntimeException("MyBatis查询数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 测试数据库连接
     */
    public void testConnection() {
        try {
            // 测试1：查询当前数据库
            Map<String, Object> dbInfo = tidbDataMapper.getCurrentDatabase();
            log.info("当前连接的数据库: {}", dbInfo);
            
            // 测试2：查询表数量
            Map<String, Object> tableCount = tidbDataMapper.getTableCount("probe-center");
            log.info("probe-center数据库表数量: {}", tableCount);
            
            // 测试3：简单查询测试
            try {
                List<Map<String, Object>> testResult = tidbDataMapper.simpleQuery("probe_status", 1);
                log.info("简单查询测试成功，结果数量: {}", testResult.size());
                if (testResult.size() > 0) {
                    log.info("测试查询结果: {}", testResult.get(0));
                }
            } catch (Exception e) {
                log.warn("简单查询测试失败: {}", e.getMessage());
            }
            
        } catch (Exception e) {
            log.error("数据库连接测试失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 生成INSERT SQL语句 - 增强版本，支持复杂数据类型
     */
    public String generateInsertSql(String tableName, Map<String, Object> data) {
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("数据不能为空");
        }

        StringBuilder columnsSql = new StringBuilder();
        StringBuilder valuesSql = new StringBuilder();

        columnsSql.append("INSERT INTO ").append(tableName).append(" (");

        boolean first = true;
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String columnName = entry.getKey();
            Object value = entry.getValue();

            if (!first) {
                columnsSql.append(", ");
                valuesSql.append(", ");
            }

            columnsSql.append(columnName);
            String sqlValue = convertToSqlValue(columnName, value);
            valuesSql.append(sqlValue);

            first = false;
        }

        columnsSql.append(") VALUES (");
        columnsSql.append(valuesSql);
        columnsSql.append(")");

        return columnsSql.toString();
    }

    /**
     * 将值转换为SQL值 - 支持多种数据类型
     */
    private String convertToSqlValue(String columnName, Object value) {
        if (value == null) {
            // 对于数值类型的字段，null值转换为0
            if (isNumericColumn(columnName)) {
                return "0";
            }
            return "NULL";
        }

        // 处理时间字段 - 特殊处理create_time和update_time
        if (columnName.equals("create_time") || columnName.equals("update_time")) {
            if (value instanceof String) {
                String timeStr = (String) value;
                // 处理ISO格式时间，转换为MySQL标准格式
                if (timeStr.contains("T")) {
                    timeStr = timeStr.replace("T", " ");
                    // 如果没有秒，添加:00
                    if (timeStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}$")) {
                        timeStr += ":00";
                    }
                }
                return "'" + timeStr.replace("'", "''") + "'";
            }
        }

        // 处理时间戳字段
        if (columnName.contains("time") && value instanceof Number) {
            return value.toString();
        }

        // 处理数值类型
        if (value instanceof Number) {
            return value.toString();
        }

        // 处理布尔类型
        if (value instanceof Boolean) {
            return ((Boolean) value) ? "1" : "0";
        }

        // 处理JSON字段
        if (needJsonSerialization(columnName, value)) {
            return convertJsonField(value);
        }

        // 其他类型用sqlValue处理
        return sqlValue(value);
    }

    /**
     * 判断是否为数值类型的列
     */
    private boolean isNumericColumn(String columnName) {
        String lowerName = columnName.toLowerCase();
        return lowerName.contains("_used") ||
                lowerName.contains("_size") ||
                lowerName.contains("_speed") ||
                lowerName.contains("_count") ||
                lowerName.contains("_total") ||
                lowerName.equals("cpu_used") ||
                lowerName.equals("mem_used") ||
                lowerName.equals("trans_traffic_size") ||
                lowerName.equals("trans_traffic_speed") ||
                lowerName.contains("_id") ||
                lowerName.contains("id");
    }

    /**
     * 判断是否需要JSON序列化
     */
    private boolean needJsonSerialization(String columnName, Object value) {
        if (value == null) return false;

        // 如果已经是字符串且看起来像JSON，直接使用
        if (value instanceof String) {
            String str = (String) value;
            return str.trim().startsWith("{") || str.trim().startsWith("[");
        }

        // 根据字段名判断
        return columnName.contains("_info") ||
                columnName.contains("_statistic") ||
                columnName.contains("_config") ||
                columnName.contains("_detail") ||
                columnName.equals("disk_info") ||
                columnName.equals("monitor_traffic_statistic");
    }

    /**
     * 转换JSON字段 - 针对TiDB优化
     */
    private String convertJsonField(Object value) {
        try {
            if (value == null) {
                return "NULL";
            }

            String jsonStr;
            // 如果已经是字符串，直接使用
            if (value instanceof String) {
                jsonStr = (String) value;
            } else {
                // 序列化为JSON字符串
                jsonStr = objectMapper.writeValueAsString(value);
            }
            
            // 对于JSON字段，只转义单引号
            jsonStr = jsonStr.replace("'", "''");
            return "'" + jsonStr + "'";
        } catch (Exception e) {
            log.warn("序列化JSON字段失败: value={}, error={}", value, e.getMessage());
            return "NULL";
        }
    }

    /**
     * SQL值处理（添加引号和转义） - 针对TiDB优化
     */
    private String sqlValue(Object value) {
        if (value == null) {
            return "NULL";
        }

        String str = value.toString();
        // 只转义单引号，避免过度转义导致TiDB解析问题
        str = str.replace("'", "''");
        return "'" + str + "'";
    }
}