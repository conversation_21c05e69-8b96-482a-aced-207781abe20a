package com.espc.sec.dataexport.tidb.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * TiDB数据库操作辅助类
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
public class TidbHelper {

    @Autowired
    private JdbcTemplate tidbJdbcTemplate;

    /**
     * 1.查询表数据
     */
    public List<Map<String, Object>> queryDataForTable(String tableName, String timeField, 
                                                       Date startTime, Date endTime,
                                                       int offset, int pageSize) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ").append(tableName);
        
        if (timeField != null && startTime != null && endTime != null) {
            sql.append(" WHERE ").append(timeField)
               .append(" >= '").append(startTime).append("'")
               .append(" AND ").append(timeField)
               .append(" < '").append(endTime).append("'");
        }

        sql.append(" LIMIT ").append(offset).append(", ").append(pageSize);
        
        return tidbJdbcTemplate.queryForList(sql.toString());
    }

    /**
     * 2.生成INSERT SQL语句
     */
    public String generateInsertSql(String tableName, Map<String, Object> data) {
        if (data.isEmpty()) {
            return "";
        }

        String columns = String.join(", ", data.keySet());
        String values = data.values().stream()
                .map(this::formatValue)
                .collect(Collectors.joining(", "));

        return String.format("INSERT INTO %s (%s) VALUES (%s);", tableName, columns, values);
    }

    /**
     * 3.格式化值
     */
    private String formatValue(Object value) {
        if (value == null) {
            return "NULL";
        }
        if (value instanceof String) {
            return "'" + value.toString().replace("'", "''") + "'";
        }
        if (value instanceof Date) {
            return "'" + value.toString() + "'";
        }
        return value.toString();
    }
}