package com.espc.sec.flow.bean;

import com.espc.sec.flow.util.DataTransUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019-08-28
 */
@Data
public class StatStruct {
    /**
     * 文件数量
     */
    private long fileNum;
    /**
     * 文件的总大小
     */
    private long totalSize;

    public StatStruct() {
        this.fileNum = 0L;
        this.totalSize = 0L;
    }

    /**
     * 增加统计量
     *
     * @param num  文件个数
     * @param size 文件总大小
     */
    public void add(long num, long size) {
        synchronized (this) {
            this.fileNum = this.fileNum + num;
            this.totalSize = this.totalSize + size;
        }
    }

    /**
     * 增加统计量
     *
     * @param struct 需要增加的统计结果
     */
    public void add(StatStruct struct) {
        synchronized (this) {
            this.fileNum = this.fileNum + struct.getFileNum();
            this.totalSize = this.totalSize + struct.getTotalSize();
        }
    }

    @Override
    public String toString() {
        return "文件个数=" + fileNum + ", 文件总大小=" + totalSize + "(" + DataTransUtil.toBytesStrFormat(totalSize) + ")";
    }
}
