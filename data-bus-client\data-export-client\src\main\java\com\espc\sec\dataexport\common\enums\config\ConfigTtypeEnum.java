package com.espc.sec.dataexport.common.enums.config;

import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum ConfigTtypeEnum {
    /**
     * 配置类型
     */
    ES(ExportTypeEnum.ES.getCode(), "es导出时间点记录"),
    STAR_ROCKS(ExportTypeEnum.STAR_ROCKS.getCode(), "starRocks导出时间点记录");

    private final String code;
    private final String name;

    public static ConfigTtypeEnum getByCode(String code) {
        return Stream.of(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
