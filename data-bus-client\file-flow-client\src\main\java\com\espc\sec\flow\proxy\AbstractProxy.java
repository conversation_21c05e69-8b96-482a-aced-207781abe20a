package com.espc.sec.flow.proxy;

import com.espc.sec.flow.bean.Constant;
import com.espc.sec.flow.bean.Job;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019-04-19
 */
@Data
@Slf4j
public abstract class AbstractProxy {
    public static final int standardFileNameLen = Constant.FILE_NAME_EXAMPLE.split("_").length;
    private Set<String> dirs;
    private Set<String> nodes;
    private Set<String> upperCaseNodes;
    private Set<String> types;
    private Set<String> upperCaseTypes;
    private boolean allFlow;

    public AbstractProxy(Set<String> dirs, Set<String> nodes, Set<String> types) {
        this.dirs = dirs;
        this.nodes = nodes;
        upperCaseNodes = new HashSet<>();
        for (String node : nodes) {
            upperCaseNodes.add(node.toUpperCase());
        }
        this.types = types;
        upperCaseTypes = new HashSet<>();
        for (String type : types) {
            upperCaseTypes.add(type.toUpperCase());
        }
        boolean dirEmp = dirs.isEmpty();
        boolean nodeEmp = nodes.isEmpty();
        boolean typeEmp = types.isEmpty();
        if (dirEmp && nodeEmp && typeEmp) {
            this.allFlow = true;
        } else {
            this.allFlow = false;
        }
    }

    public abstract boolean flowOneFile(Job job);

    /**
     * 判断一个文件是否要分流
     *
     * @param job             任务
     * @param needSpecialFile 非标准格式的文件是否需要分流
     * @return 需要分流返回true，否则返回false
     */
    public boolean needFlow(Job job, boolean needSpecialFile) {
        try {
            if (!allFlow) {
                String sourceDir = job.getSourceDirPath();
                if (!dirs.isEmpty() && !dirs.contains(sourceDir)) {
                    return false;
                }
                String fileName = job.getFileName();
                String[] names = fileName.split("_");
                if (names.length != standardFileNameLen) {
                    return needSpecialFile;
                }
                String node = names[1].toUpperCase();
                String type = names[2].toUpperCase();
                if (!upperCaseNodes.isEmpty() && !upperCaseNodes.contains(node)) {
                    return false;
                }
                if (!upperCaseTypes.isEmpty() && !upperCaseTypes.contains(type)) {
                    return false;
                }
                return true;
            } else {
                if (!needSpecialFile) {
                    String fileName = job.getFileName();
                    String[] names = fileName.split("_");
                    if (names.length != standardFileNameLen) {
                        return false;
                    } else {
                        return true;
                    }
                }
                return true;
            }
        } catch (Exception e) {
            log.warn("exception when judge file:" + job.getAbsolutePath(), e);
            return false;
        }
    }
}
