package com.espc.sec.dataimport;

import com.espc.sec.dataimport.common.loader.LocalConfigLoader;
import com.espc.sec.dataimport.netty.NettyServer;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/7/14
 **/
@Slf4j
public class DataImportApp {

    public static void start() {
        try {
            // 加载配置文件
            LocalConfigLoader.loadConfig();
            // 开启netty服务端
            new Thread(new NettyServer()).start();
        } catch (Exception e) {
            log.error("Failed to start DataImportApp", e);
            System.exit(-1);
        }
    }
}
