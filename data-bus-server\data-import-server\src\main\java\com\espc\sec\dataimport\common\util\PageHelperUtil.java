package com.espc.sec.dataimport.common.util;

import com.espc.sec.dataimport.common.dto.PageDto;
import com.espc.sec.dataimport.common.vo.PageVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.function.Supplier;

public class PageHelperUtil {
    public static <T> PageVo<T> startPage(PageDto pageDto, Supplier<List<T>> supplier) {
        PageHelper.startPage(pageDto.getPageNo(), pageDto.getPageSize());
        List<T> ts = supplier.get();
        PageInfo<T> pageInfo = new PageInfo<>(ts);
        PageVo<T> pageVo = new PageVo<>();
        pageVo.setPageNo(pageDto.getPageNo());
        pageVo.setPageSize(pageDto.getPageSize());
        pageVo.setTotal(pageInfo.getTotal());
        pageVo.setList(ts);
        return pageVo;
    }
}
