package com.espc.sec.flow;

import Ice.Communicator;
import com.espc.sec.flow.bean.RevFile;
import com.espc.sec.flow.bean.TempFile;
import com.espc.sec.flow.ice.FileFlowServerImpl;
import com.espc.sec.flow.ice.UnzipRunnable;
import com.espc.sec.flow.util.ConfigUtil;
import com.espc.sec.flow.util.Constant;
import com.espc.sec.flow.util.IceUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2018-06-04
 */
@Slf4j
public class FileFlowServerMain {

    /**
     * 数据总线文件传输通道启动方法
     */
    public static void start() {
        log.info("begin to start service.");

        // new com.common.monitoring.MonitoringService();

        try {
            //启动Ice服务端
            ConfigUtil.initConfig();
            new Thread(new UnzipRunnable()).start();
            new Thread(new Runnable() {
                @Override
                public void run() {
                    zipTemp();
                }
            }).start();
            Communicator communicator = IceUtil.getCommunicator();
            startService(communicator);
        } catch (Exception e) {
            log.error("error when start service.", e);
        }
    }

    /**
     * 读取临时目录未解压的数据
     *
     * @throws InterruptedException
     */
    private static void zipTemp() {
        try {
            File temp = new File(ConfigUtil.config.getTempDir());
            File[] fileArray = temp.listFiles();
            if (fileArray != null) {
                for (File subFile : fileArray) {
                    if (subFile.isFile()) {
                        String extension = subFile.getName().contains(".") ?
                                subFile.getName().substring(subFile.getName().lastIndexOf(".") + 1) : null;
                        if (RevFile.TYPE_ZIP.equals(extension)) {
                            TempFile tempFile = new TempFile();
                            tempFile.setFile(subFile);
                            Constant.FILE_QUEUE.put(tempFile);
                            log.debug("put a zip file into queue, file:" + tempFile.getFile());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("error when zip temp.", e);
        }
    }

    public static void startService(Communicator communicator) throws Exception {
        String endPoint = "default -h " + ConfigUtil.config.getFlowServiceIp() + " -p " + ConfigUtil.config.getFlowServicePort();
        Ice.ObjectAdapter adapter = communicator.createObjectAdapterWithEndpoints("FileFlowServiceAdpater", endPoint);
        Ice.Object object = new FileFlowServerImpl();
        adapter.add(object, communicator.stringToIdentity("FileFlowService"));
        adapter.activate();
        log.info("service start success.");
    }
}