package com.espc.sec.dataexport.common.util;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件操作工具类
 */
@Slf4j
public class FileUtils {

    private static final Charset DEFAULT_CHARSET = StandardCharsets.UTF_8;

    /**
     * 创建文件（如果不存在）
     *
     * @param filePath 文件路径
     * @return 是否创建成功
     */
    public static boolean createFileIfNotExists(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            return true;
        }

        try {
            // 创建父目录
            File parent = file.getParentFile();
            if (parent != null && !parent.exists()) {
                parent.mkdirs();
            }
            return file.createNewFile();
        } catch (IOException e) {
            log.error("创建文件失败", e);
            return false;
        }
    }

    /**
     * 创建目录（如果不存在）
     *
     * @param dirPath 目录路径
     * @return 是否创建成功
     */
    public static boolean createDirIfNotExists(String dirPath) {
        File dir = new File(dirPath);
        if (dir.exists()) {
            return dir.isDirectory();
        }
        return dir.mkdirs();
    }

    /**
     * 删除文件或目录
     *
     * @param path 文件或目录路径
     * @return 是否删除成功
     */
    public static boolean delete(String path) {
        File file = new File(path);
        if (!file.exists()) {
            return true;
        }

        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (File f : files) {
                    delete(f.getAbsolutePath());
                }
            }
        }
        return file.delete();
    }

    /**
     * 复制文件
     *
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 是否复制成功
     */
    public static boolean copyFile(String sourcePath, String targetPath) {
        try {
            createFileIfNotExists(targetPath);
            Files.copy(Paths.get(sourcePath), Paths.get(targetPath), StandardCopyOption.REPLACE_EXISTING);
            return true;
        } catch (IOException e) {
            log.error("复制文件失败", e);
            return false;
        }
    }

    /**
     * 复制目录
     *
     * @param sourceDir 源目录路径
     * @param targetDir 目标目录路径
     * @return 是否复制成功
     */
    public static boolean copyDir(String sourceDir, String targetDir) {
        File srcDir = new File(sourceDir);
        if (!srcDir.exists() || !srcDir.isDirectory()) {
            return false;
        }

        createDirIfNotExists(targetDir);

        File[] files = srcDir.listFiles();
        if (files == null || files.length == 0) {
            return true;
        }

        boolean result = true;
        for (File file : files) {
            String targetPath = targetDir + File.separator + file.getName();
            if (file.isDirectory()) {
                result &= copyDir(file.getAbsolutePath(), targetPath);
            } else {
                result &= copyFile(file.getAbsolutePath(), targetPath);
            }
        }
        return result;
    }

    /**
     * 移动文件或目录
     *
     * @param sourcePath 源路径
     * @param targetPath 目标路径
     * @return 是否移动成功
     */
    public static boolean move(String sourcePath, String targetPath) {
        try {
            Files.move(Paths.get(sourcePath), Paths.get(targetPath), StandardCopyOption.REPLACE_EXISTING);
            return true;
        } catch (IOException e) {
            log.error("移动文件失败", e);
            return false;
        }
    }

    /**
     * 重命名文件或目录
     *
     * @param oldPath 原路径
     * @param newPath 新路径
     * @return 是否重命名成功
     */
    public static boolean rename(String oldPath, String newPath) {
        File oldFile = new File(oldPath);
        File newFile = new File(newPath);
        return oldFile.renameTo(newFile);
    }

    /**
     * 读取文件内容为字符串
     *
     * @param filePath 文件路径
     * @return 文件内容
     */
    public static String readFileToString(String filePath) {
        return readFileToString(filePath, DEFAULT_CHARSET);
    }

    /**
     * 读取文件内容为字符串
     *
     * @param filePath 文件路径
     * @param charset  字符编码
     * @return 文件内容
     */
    public static String readFileToString(String filePath, Charset charset) {
        try {
            byte[] bytes = Files.readAllBytes(Paths.get(filePath));
            return new String(bytes, charset);
        } catch (IOException e) {
            log.error("读取文件失败", e);
            return null;
        }
    }

    /**
     * 按行读取文件内容
     *
     * @param filePath 文件路径
     * @return 文件行列表
     */
    public static List<String> readLines(String filePath) {
        return readLines(filePath, DEFAULT_CHARSET);
    }

    /**
     * 按行读取文件内容
     *
     * @param filePath 文件路径
     * @param charset  字符编码
     * @return 文件行列表
     */
    public static List<String> readLines(String filePath, Charset charset) {
        try {
            return Files.readAllLines(Paths.get(filePath), charset);
        } catch (IOException e) {
            log.error("读取文件失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 写入字符串到文件
     *
     * @param filePath 文件路径
     * @param content  内容
     * @return 是否写入成功
     */
    public static boolean writeStringToFile(String filePath, String content) {
        return writeStringToFile(filePath, content, DEFAULT_CHARSET);
    }

    /**
     * 写入字符串到文件
     *
     * @param filePath 文件路径
     * @param content  内容
     * @param charset  字符编码
     * @return 是否写入成功
     */
    public static boolean writeStringToFile(String filePath, String content, Charset charset) {
        createFileIfNotExists(filePath);
        try {
            Files.write(Paths.get(filePath), content.getBytes(charset));
            return true;
        } catch (IOException e) {
            log.error("写入文件失败", e);
            return false;
        }
    }

    /**
     * 追加字符串到文件
     *
     * @param filePath 文件路径
     * @param content  内容
     * @return 是否追加成功
     */
    public static boolean appendStringToFile(String filePath, String content) {
        return appendStringToFile(filePath, content, DEFAULT_CHARSET);
    }

    /**
     * 追加字符串到文件
     *
     * @param filePath 文件路径
     * @param content  内容
     * @param charset  字符编码
     * @return 是否追加成功
     */
    public static boolean appendStringToFile(String filePath, String content, Charset charset) {
        createFileIfNotExists(filePath);
        try (OutputStream os = new FileOutputStream(filePath, true);
             OutputStreamWriter osw = new OutputStreamWriter(os, charset);
             BufferedWriter bw = new BufferedWriter(osw);
             PrintWriter out = new PrintWriter(bw)) {
            out.print(content);
            return true;
        } catch (IOException e) {
            log.error("写入文件失败", e);
            return false;
        }
    }

    /**
     * 获取文件大小（字节）
     *
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    public static long getFileSize(String filePath) {
        File file = new File(filePath);
        if (file.exists() && file.isFile()) {
            return file.length();
        }
        return -1;
    }

    /**
     * 获取目录大小（字节）
     *
     * @param dirPath 目录路径
     * @return 目录大小（字节）
     */
    public static long getDirSize(String dirPath) {
        File dir = new File(dirPath);
        if (!dir.exists() || !dir.isDirectory()) {
            return -1;
        }

        long size = 0;
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    size += file.length();
                } else {
                    size += getDirSize(file.getAbsolutePath());
                }
            }
        }
        return size;
    }

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 是否存在
     */
    public static boolean isFileExists(String filePath) {
        File file = new File(filePath);
        return file.exists() && file.isFile();
    }

    /**
     * 检查目录是否存在
     *
     * @param dirPath 目录路径
     * @return 是否存在
     */
    public static boolean isDirExists(String dirPath) {
        File dir = new File(dirPath);
        return dir.exists() && dir.isDirectory();
    }

    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 扩展名（不带点）
     */
    public static String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 获取文件名（不带扩展名）
     *
     * @param fileName 文件名
     * @return 文件名（不带扩展名）
     */
    public static String getFileNameWithoutExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0) {
            return fileName.substring(0, dotIndex);
        }
        return fileName;
    }

    /**
     * 拆解文件路径为目录路径和文件名
     *
     * @param fullPath 完整文件路径
     * @return String数组，第一个元素是目录路径，第二个元素是文件名
     */
    public static String[] splitFilePath(String fullPath) {
        Path path = Paths.get(fullPath);
        String directory = path.getParent().toString();
        String fileName = path.getFileName().toString();
        return new String[]{directory, fileName};
    }

    /**
     * 递归扫描目录及其子目录下的所有文件
     *
     * @param directoryPath 目录路径
     */
    public static List<String> listFiles(String directoryPath) {
        File directory = new File(directoryPath);

        if (!directory.exists()) {
            return Lists.newArrayList();
        }

        if (!directory.isDirectory()) {
            return Lists.newArrayList();
        }
        List<String> res = Lists.newArrayList();
        scanDirectory(directory, res);
        return res;
    }

    private static void scanDirectory(File directory, List<String> res) {
        File[] files = directory.listFiles();
        if (files == null) return;

        for (File file : files) {
            if (file.isFile()) {
                res.add(file.getAbsolutePath());
            } else if (file.isDirectory()) {
                scanDirectory(file, res);
            }
        }
    }
}
