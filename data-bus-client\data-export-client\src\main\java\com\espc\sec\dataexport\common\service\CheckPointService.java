package com.espc.sec.dataexport.common.service;

import com.espc.sec.dataexport.common.dto.Tricycle;
import com.espc.sec.dataexport.common.dto.Tuple;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;

import java.util.Date;

/**
 * 数据库增量数据时间记录
 *
 * <AUTHOR>
 * @date 2025/7/26
 */
public interface CheckPointService {
    /**
     * 获取上次同步数据库结束时间
     *
     * @param exportTypeEnum
     * @param tableName      starRocks: 库.表名, kafka: topic, es: 索引
     * @return
     */
    Tuple<Integer, Date> getLastTime(ExportTypeEnum exportTypeEnum, String tableName);

    /**
     * 记录同步时间
     */
    void insert(ExportTypeEnum exportTypeEnum, String tableName, Date date);

    /**
     * 更新记录时间
     */
    void update(Integer id, Date date);

    /**
     * 获取索引上次同步结束时间
     *
     * @param tableName starRocks: 库.表名, kafka: topic, es: 索引
     * @return <id,开始时间,结束时间>
     */
    Tricycle<Integer, Date, Date> getTimeRangeForIndex(ExportTypeEnum exportTypeEnum,String tableName);
}
