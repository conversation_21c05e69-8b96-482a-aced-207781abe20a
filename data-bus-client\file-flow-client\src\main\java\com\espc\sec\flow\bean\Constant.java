package com.espc.sec.flow.bean;

import com.espc.sec.flow.queue.JobPriorityQueue;
import com.espc.sec.flow.util.ConfigUtil;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.PriorityBlockingQueue;

/**
 * <AUTHOR>
 * @date 2018-01-11
 */
public class Constant {

    /**
     * 接口返回成功的状态
     */
    public static final int RESPONSE_SUCCESS = 200;

    /**
     * 服务端接收标准文件的接口编号
     */
    public static final int FLAG_SEND_NORMAL_FILE = 101;
    /**
     * 服务端接收压缩文件的接口编号
     */
    public static final int FLAG_SEND_ZIP_FILE = 102;
    /**
     * 汇聚统计文件类型
     */
    public static final String HJ_STAT_FILE_TYPE = "6005";
    /**
     * 标准文件名示例
     */
    public static final String FILE_NAME_EXAMPLE = "ss_19122026_2101_20180306083257_00000449";
    /**
     * 数据池服务端返回授权成功
     */
    public static final String SUCCESS = "success";
    /**
     * 数据池返回未授权
     */
    public static final String NOT_AUTH_CODE = "401";

    /**
     * 6005类型文件的优先级（最高）
     */
    public static final byte PRIORITY_6005 = (byte) 3;
    /**
     * 任务优先级：高
     */
    public static final byte PRIORITY_HIGH = (byte) 2;
    /**
     * 任务优先级：低
     */
    public static final byte PRIORITY_LOW = (byte) 1;
    /**
     * 任务的优先级队列
     */
    public static final BlockingQueue<Job> JOB_QUEUE;

    static {
        if (!ConfigUtil.needReadToMem()) {
            //如果不需要把文件读取到内存，表示只需要本地移动，此时移动的速度会大大的快于文件的遍历速度，因此设置队列长度不限
            JOB_QUEUE = new PriorityBlockingQueue<>();
        } else {
            JOB_QUEUE = new JobPriorityQueue(ConfigUtil.config.getFileQueueSize());
        }
    }
}