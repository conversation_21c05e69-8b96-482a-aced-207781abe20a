package com.espc.sec.dataexport.common.enums;

import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.common.util.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@AllArgsConstructor
@Getter
public enum ExportTypeEnum {
    /**
     * 导出类型
     */
    ES("elasticsearch", LogKeyword.ES_EXPORT),
    STAR_ROCKS("starrocks", LogKeyword.STAR_ROCKS_EXPORT),
    TIDB("tidb", LogKeyword.TIDB_EXPORT),
    KAFKA("kafka", LogKeyword.KAFKA_EXPORT),
    MINIO("minio", LogKeyword.MINIO_EXPORT),
    HDFS("hdfs", LogKeyword.HDFS_EXPORT),
    ;

    private final String code;
    private final String logKeyWord;

    public static ExportTypeEnum getByCode(String code) {
        return StreamUtil.getStream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public static void main(String[] args) {
        System.out.println(getByCode("minio"));
    }
}
