package com.espc.sec.flow.ice;

import com.espc.sec.flow.bean.TempFile;
import com.espc.sec.flow.util.ConfigUtil;
import com.espc.sec.flow.util.Constant;
import com.espc.sec.flow.util.ZipUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.concurrent.*;


/**
 * <AUTHOR>
 * @date 2018-06-04
 */
@Slf4j
public class UnzipRunnable implements Runnable {
    @Override
    public void run() {
        Thread.currentThread().setName("Thread-UnzipMove");
        log.info("begin to unzip");
        int poolSize = ConfigUtil.config.getUnZipThreadPoolSize();
        ThreadFactory threadFactory = Executors.defaultThreadFactory();
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1, poolSize, 60, TimeUnit.SECONDS,
                new ArrayBlockingQueue<Runnable>(10),
                threadFactory,
                new ThreadPoolExecutor.CallerRunsPolicy());
        log.info("init unzip thread pool success. pool size:" + ConfigUtil.config.getUnZipThreadPoolSize());
        while (true) {
            try {
                TempFile tempFile = Constant.FILE_QUEUE.poll(3, TimeUnit.SECONDS);
                if (tempFile == null) {
                    log.info("FILE_QUEUE is empty. unzip thread will sleep 60s.");
                    Thread.sleep(60000);
                } else {
                    log.info("get a temp file:" + tempFile.getFile() + ", queue size:" + Constant.FILE_QUEUE.size());
                    threadPoolExecutor.submit(new UnzipThread(tempFile.getFile(), ConfigUtil.config.getTargetDir()));
                }
            } catch (Exception e) {
                log.error("error when unzip and move.", e);
            }
        }
    }

    class UnzipThread implements Runnable {
        private File file;
        private String target;

        public UnzipThread(File file, String target) {
            this.file = file;
            this.target = target;
        }

        @Override
        public void run() {
            try {
                long start = System.currentTimeMillis();
                ZipUtil.unzip(file, target);
                long end = System.currentTimeMillis();
                log.info("unzip a file success, sourceDir:" + file.getAbsolutePath() + ", cost:" + (end - start));
                if (!file.delete()) {
                    log.error("delete failed. file:" + file.getAbsolutePath());
                }
            } catch (Exception e) {
                log.error("error when unzip file, path=" + file.getAbsolutePath(), e);
            }
        }
    }
}