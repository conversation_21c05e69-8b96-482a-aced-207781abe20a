package com.espc.sec.dataexport.config.service.impl;

import cn.hutool.core.util.StrUtil;
import com.espc.sec.dataexport.common.enums.config.ConfigModuleEnum;
import com.espc.sec.dataexport.common.enums.config.ConfigTtypeEnum;
import com.espc.sec.dataexport.config.mapper.ConfigMapper;
import com.espc.sec.dataexport.config.service.ConfigService;
import com.espc.sec.dataexport.config.vo.ConfigVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/28
 */
@Service
public class ConfigServiceImpl implements ConfigService {
    @Autowired
    private ConfigMapper configMapper;

    @Override
    public ConfigVo getConfig(ConfigModuleEnum moduleEnum, ConfigTtypeEnum typeEnum, String key) {
        if (StrUtil.isBlank(key)) {
            return null;
        }
        return configMapper.getConfig(moduleEnum.getCode(), typeEnum.getCode(), key);
    }

    @Override
    public void save(ConfigModuleEnum moduleEnum, ConfigTtypeEnum typeEnum, String key, String value) {
          configMapper.save(moduleEnum.getCode(), typeEnum.getCode(), key, value);
    }

    @Override
    public void update(Integer id, String value) {
        configMapper.updateValue(id, value);
    }
}
