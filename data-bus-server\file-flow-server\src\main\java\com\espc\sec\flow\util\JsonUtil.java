package com.espc.sec.flow.util;


import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Created by JAY on 2016/1/20.
 */
@Slf4j
public class JsonUtil {
    public static final ObjectMapper objectMapper;

    static {
        JsonFactory jsonFactory = new JsonFactory();
        jsonFactory.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        objectMapper = new ObjectMapper(jsonFactory);
        // 忽略未知属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);

    }

    public static String objectToString(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("error when Object to Json String", e);
        }
        return null;
    }

    public static Map stringToMap(String string) throws Exception {
        return objectMapper.readValue(string, Map.class);
    }

    public static String objectToJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            log.error("error when Object to Json String", e);
        }
        return null;
    }

    public static Object jsonToObject(String json, Class clazz) throws Exception {
        return JsonUtil.objectMapper.readValue(json, clazz);
    }
}
