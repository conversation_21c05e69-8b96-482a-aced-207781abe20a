package com.espc.sec.flow.helper;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileNotFoundException;
@Slf4j
public class SshHelper {

    private String host;
    private int port;
    private String user;
    private String pass;
    private boolean isUsePem;
    private File pemPath;

    private File pemFile;

    public SshHelper(String host, int port, String user, String pass, boolean isUsePem, File pemPath) {
        this.host = host;
        this.port = port;
        this.user = user;
        this.pass = pass;
        this.isUsePem = isUsePem;
        this.pemPath = pemPath;
        if (!pemPath.exists()) {
            pemPath.mkdirs();
        }
        this.pemFile = new File(pemPath, user + "_sftp_private_key.pem");
    }

    public ChannelSftp getChannelSftpWithRetry() {
        while (true) {
            try {
                return getChannelSftp();
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
                try {
                    Thread.sleep(10 * 1000);
                } catch (InterruptedException e1) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    public ChannelSftp getChannelSftp() throws JSchException, SftpException {
        Session session = getSession();
        Channel channel = session.openChannel("sftp");
        channel.connect();
        return (ChannelSftp) channel;
    }

    private Session getSession() throws SftpException, JSchException {
        if (isUsePem) {
            return getSessionWithPem();
        } else {
            return getSessionWithPassword();
        }
    }

    private Session getSessionWithPem() throws JSchException, SftpException {
        if (!pemFile.exists()) {
            log.info("pem file not exists. trying to download.");
            try {
                getPemFile();
            } catch (FileNotFoundException e) {
                log.error("pem file not exist on datapool or local, exit.");
                System.exit(-1);
            }
        }
        JSch jSch = new JSch();
        Session session = jSch.getSession(user, host, port);
        jSch.addIdentity(pemFile.getAbsolutePath());
        session.setConfig("strictHostKeyChecking", "no");
        session.setUserInfo(new MyUserInfo());
        session.connect();
        return session;

    }

    private Session getSessionWithPassword() throws JSchException {
        JSch jSch = new JSch();
        Session session = jSch.getSession(user, host, port);
        session.setPassword(pass);
        session.setUserInfo(new MyUserInfo());
        session.connect();
        return session;

    }

    private File getPemFile() throws FileNotFoundException, JSchException, SftpException {
        Session session = getSessionWithPassword();
        Channel channel = session.openChannel("sftp");
        channel.connect();
        ChannelSftp tmpChannel = (ChannelSftp) channel;

        String poolPemPath = String.format("/datapool/%s/public/cert", user);
        String poolPemFilePath = String.format("%s/%s_sftp_private_key.pem", poolPemPath, user);

        File pemFile = new File(pemPath, user + "_sftp_private_key.pem");

        for (Object obj : tmpChannel.ls(poolPemPath)) {
            ChannelSftp.LsEntry entry = (ChannelSftp.LsEntry) obj;
            if (entry.getFilename().equals(pemFile.getName())) {
                tmpChannel.get(poolPemFilePath, pemFile.getAbsolutePath());
                log.info("pem file download:" + pemFile.getAbsolutePath());
                return pemFile;
            }
        }
        throw new FileNotFoundException("pem file not found in:" + poolPemFilePath);

    }

    private static class MyUserInfo implements UserInfo {

        @Override
        public String getPassphrase() {
            return null;
        }

        @Override
        public String getPassword() {
            return null;
        }

        @Override
        public boolean promptPassword(String s) {
            return false;
        }

        @Override
        public boolean promptPassphrase(String s) {
            return false;
        }

        @Override
        public boolean promptYesNo(String s) {
            return true;
        }

        @Override
        public void showMessage(String s) {

        }
    }

}
