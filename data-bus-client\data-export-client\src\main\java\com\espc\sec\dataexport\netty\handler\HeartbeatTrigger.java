package com.espc.sec.dataexport.netty.handler;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.enums.OperateTypeEnum;
import com.espc.sec.dataexport.netty.entity.MessageTypeEnum;
import com.espc.sec.dataexport.netty.entity.MessageWrapper;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2025/7/17
 **/
@Slf4j
public class HeartbeatTrigger extends ChannelInboundHandlerAdapter {

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent event = (IdleStateEvent) evt;
            if (event.state() == IdleState.WRITER_IDLE) {
                // 发送心跳包
                MessageWrapper messageWrapper = new MessageWrapper();
                messageWrapper.setType(MessageTypeEnum.HEART_BEAT);
                messageWrapper.setOperateType(OperateTypeEnum.CREATE);

                MessageWrapper.HeartbeatInfo heartbeatInfo = new MessageWrapper.HeartbeatInfo();
                heartbeatInfo.setNode(Config.commonConfig.getNodeName());
                heartbeatInfo.setTime(DateUtil.now());
                byte[] body = JSONUtil.toJsonStr(heartbeatInfo).getBytes(StandardCharsets.UTF_8);
                messageWrapper.setLength(body.length);
                messageWrapper.setBody(body);

                ctx.writeAndFlush(messageWrapper);

                log.info("向服务端发送心跳, 地址: {}, 节点: {}, 时间: {}", ctx.channel().remoteAddress(), heartbeatInfo.getNode(), heartbeatInfo.getTime());
            }
        }
    }
}
