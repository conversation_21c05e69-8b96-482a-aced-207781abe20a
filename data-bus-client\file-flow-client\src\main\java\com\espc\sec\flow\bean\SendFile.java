package com.espc.sec.flow.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2018-06-04
 */
@Data
public class SendFile {
    /**
     * 常量：压缩成的zip文件格式
     */
    public static final String TYPE_ZIP = "xzip";
    /**
     * 发送的文件名
     */
    private String fileName;
    /**
     * 文件相对于临时目录的相对路径
     */
    private String filePath;
    /**
     * 文件类型
     */
    private String type;

    /**
     * 发送的文件，不用转换成json
     */
    @JsonIgnore
    private File file;

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof SendFile) {
            SendFile sendFile = (SendFile) obj;
            return sendFile.getFile().equals(file);
        }
        return false;
    }

    @Override
    public int hashCode() {
        return file.hashCode();
    }
}