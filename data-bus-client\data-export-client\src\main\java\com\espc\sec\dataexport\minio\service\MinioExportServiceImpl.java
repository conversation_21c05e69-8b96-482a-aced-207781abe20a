package com.espc.sec.dataexport.minio.service;

import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.dto.task.MinioTaskProperties;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import com.espc.sec.dataexport.common.service.IncrementExportService;
import com.espc.sec.dataexport.common.service.TaskExportService;
import com.espc.sec.dataexport.common.util.JsonUtil;
import com.espc.sec.dataexport.common.util.MultiThreadUtil;
import com.espc.sec.dataexport.minio.dto.MinioExportDto;
import com.espc.sec.dataexport.minio.entity.MinioBatchExportResult;
import com.espc.sec.dataexport.minio.entity.MinioMetaData;
import com.espc.sec.dataexport.minio.helper.MinIOServiceHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import io.minio.Result;
import io.minio.errors.*;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * minio导出服务主类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MinioExportServiceImpl implements TaskExportService<MinioTaskProperties>, IncrementExportService {

    private static final TypeReference<Map<String, Object>> TYPE_REFERENCE = new TypeReference<Map<String, Object>>() {
    };
    @Autowired
    private MinioExporter minioExporter;

    /**
     * 根据参数导出数据（新增接口支持）
     *
     * @param request 导出请求参数
     * @return 导出结果
     */
    @Override
    public void taskExport(MinioTaskProperties request) {
        Map<String, List<String>> bucketGroupMap = buildBucketName(request);
        Integer exportRecordSize = 0;
        /**
         * bucketGroupMap.keySet()  理论上三种
         */
        for (String bucketType : bucketGroupMap.keySet()) {
            //循环处理，如果一个bucket不满足数量的情况下需要从同类型的其他日期的bucket读取
            Map<String, List<String>> allObjectFileNameGroup = null;
            try {
                allObjectFileNameGroup = getAllObjectFileNameGroup(bucketGroupMap.get(bucketType), request.getDataSize(), request.getDataStartTime(), request.getDataEndTime());
            } catch (Exception e) {
                log.error("获取minio的文件失", e);

                return;
            }
            if (allObjectFileNameGroup.isEmpty()) {
                continue;
            }

            //导出对应bucket全部的对象文件
            MinioBatchExportResult minioBatchExportResult = syncExportDataWithParams(allObjectFileNameGroup,request.getTaskId());
            exportRecordSize += minioBatchExportResult.getSuccessCount();
        }

        log.info("minio导出参数：{}, 共:{}个文件，成功导出：{}个文件", request, request.getDataSize(), exportRecordSize);
        log.info("minio导出参数：{}, 共:{}个文件，成功导出：{}个文件", request, request.getDataSize(), exportRecordSize);

    }

    @Override
    public void incrementExport(String kafkaMessage) throws Exception {
        MinioExportDto dto = new MinioExportDto();
        Map<String, Object> messageMap = JsonUtil.jsonToObject(kafkaMessage, TYPE_REFERENCE);
        String bucketAndObject = messageMap.get("Key").toString();
        String[] split = bucketAndObject.split("/");

        dto.setExportTypeEnum(ExportTypeEnum.MINIO);
        dto.setExportModeEnum(ExportModeEnum.INCREMENT);
        dto.setBucketName(split[0]);
        dto.setObjectName(split[1]);
        minioExporter.export(dto);
    }


    /**
     * 真正执行导出操作
     * key:bucketName  value:[实际文件名字]
     *
     * @param allObjectFileNameGroup 需要导出文件名字
     * @param taskId
     * @return
     */
    private MinioBatchExportResult syncExportDataWithParams(Map<String, List<String>> allObjectFileNameGroup, Integer taskId) {

        MinioBatchExportResult result = new MinioBatchExportResult();

        AtomicInteger failSize = new AtomicInteger(0);
        AtomicInteger successSize = new AtomicInteger(0);
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (String bucketName : allObjectFileNameGroup.keySet()) {
            //对应bucket中需要导出的文件数量
            List<String> objectFileNames = allObjectFileNameGroup.get(bucketName);

            for (String objectFileName : objectFileNames) {

                //异步导出
                futures.add(CompletableFuture.runAsync(() -> {
                    MinioExportDto minioExportDto = new MinioExportDto();
                    minioExportDto.setBucketName(bucketName);
                    minioExportDto.setObjectName(objectFileName);
                    minioExportDto.setExportModeEnum(ExportModeEnum.TASK);
                    minioExportDto.setExportTypeEnum(ExportTypeEnum.MINIO);
                    minioExportDto.setTaskId(taskId);
                    try {
                        minioExporter.export(minioExportDto);
                        successSize.incrementAndGet();
                    } catch (Exception e) {
                        log.error("导出minio的文件失败：{}", e);
                        failSize.incrementAndGet();
                    }

                }, MultiThreadUtil.executorService));
            }

        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        //存在导出失败的数据
        result.setSuccessCount(successSize.get());
        result.setAllCount(successSize.get() + failSize.get());
        return result;
    }

    /**
     * 在指定bucket集合中获取指定数量的bucket下的对象名称
     * pr-inf-file-2025-07-19：【pr-inf-file-2025-07-19-1.file, pr-inf-file-2025-07-19-1.file】
     * pr-inf-file-2025-07-18：【pr-inf-file-2025-07-18-1.file】
     *
     * @param bucketTypeList bucket集合
     * @param dataSize       指定数量
     * @return
     */
    private Map<String, List<String>> getAllObjectFileNameGroup(List<String> bucketTypeList, Integer dataSize, String dataStartTime, String dataEndTime) throws Exception {
        Map<String, List<String>> objectGroupMap = new HashMap<>();
        Integer bucketCount = dataSize;
        for (String bucketName : bucketTypeList) {
            if (bucketCount == 0) {
                break;
            }
            List<String> objectNameList = listPageObjects(bucketName, bucketCount, dataStartTime, dataEndTime);
            //每次获取总数变化
            bucketCount = dataSize - objectNameList.size();
            //若有数据加入分组
            if (!objectNameList.isEmpty()) {
                if (objectGroupMap.containsKey(bucketName)) {
                    List<String> objectFile = objectGroupMap.get(bucketName);
                    objectNameList.addAll(objectFile);
                }
                objectGroupMap.put(bucketName, objectNameList);
            }
        }
        return objectGroupMap;
    }

    private List<String> listPageObjects(String bucketName, Integer objectCount, String dataStartTime, String dataEndTime) throws Exception{
        List<String> objectFileName = new ArrayList<>();
        if (objectCount < 1) {
            return objectFileName;
        }
        String dataStart = DateUtil.format(DateUtil.parse(dataStartTime), Constants.MILLS_FORMAT);
        String dataEnd = DateUtil.format(DateUtil.parse(dataEndTime), Constants.MILLS_FORMAT);
        //分页标识
        String marker = null;
        //每次读取数量
        int signalObjectCount = objectCount;

        while ( objectCount > 0) {
            //单次读取数量有限制
            if (signalObjectCount > Constants.MINIO_PAGE_OBJECT_MAX) {
                signalObjectCount = Constants.MINIO_PAGE_OBJECT_MAX;
            }

            List<MinioMetaData> minioMetaData = listObjectsByPage(bucketName, signalObjectCount, marker, dataStart, dataEnd);

            if (minioMetaData.isEmpty()) {
                break;
            }
            //下一次分页起点
            marker = minioMetaData.get(minioMetaData.size() - 1).getObjectName();
            objectFileName.addAll(minioMetaData.stream().map(MinioMetaData::getObjectName).collect(Collectors.toList()));
            //剩余需要的总数
            objectCount = objectCount - objectFileName.size();
            //下一次需要获取的总数
            signalObjectCount = objectCount - signalObjectCount;
        }
        return objectFileName;
    }

    /**
     * 目标值是否在指定范围内
     *
     * @param target 目标值
     * @param start  开始范围
     * @param end    结束范围
     * @return
     */
    public boolean isTimeInRange(String target, String start, String end) {
        // 检查所有参数非空且长度均为17位
        if (StringUtils.isAnyBlank(target, start, end)) {
            throw new IllegalArgumentException("输入参数不能为null");
        }
        if (target.length() != 17 || start.length() != 17 || end.length() != 17) {
            throw new IllegalArgumentException("时间格式不正确");
        }

        // 字典序比较：满足 start <= target <= end 即在范围内
        return target.compareTo(start) >= 0 && target.compareTo(end) <= 0;
    }

    /**
     * 构建build名字，并返回对应结果分组
     * pr-inf-file ： [pr-inf-file-2025-07-19,pr-inf-file-2025-07-18]
     * pr-inf-eml ： [pr-inf-eml-2025-07-19,pr-inf-eml-2025-07-18]
     * pr-inf-cert ： [pr-inf-cert-2025-07-19,pr-inf-cert-2025-07-18]
     *
     * @param request 时间范围  对应文件名称集合
     * @return
     */
    private Map<String, List<String>> buildBucketName(MinioTaskProperties request) {

        //key:pr-inf-file  value:[pr-inf-file-2025-07-19,pr-inf-file-2025-07-18]
        Map<String, List<String>> bucketGroup = new HashMap<>();

        LocalDate bucketDateStart = LocalDateTime.parse(request.getDataStartTime(), Constants.TIME_FORMATTER).toLocalDate();
        LocalDate bucketDateEnd = LocalDateTime.parse(request.getDataEndTime(), Constants.TIME_FORMATTER).toLocalDate();

        while (!bucketDateStart.isAfter(bucketDateEnd)) {
            for (String bucketPrefix : request.getBucketPrefix()) {
                //拼接日期 pr-inf-file-2025-07-19  pr-inf-eml-2025-07-19  pr-inf-cert-2025-07-19

                String bucketName = bucketPrefix + Constants.SEPARATOR_LINE + bucketDateStart.format(Constants.MILLS_DAY_MONTH_YEAR_FORMAT);
                //存在则纳入查询范围
                if (MinIOServiceHelper.doesBucketExist(bucketName)) {
                    if (bucketGroup.containsKey(bucketName)) {
                        List<String> realBucketName = bucketGroup.get(bucketName);
                        realBucketName.add(bucketName);
                        bucketGroup.put(bucketName, realBucketName);
                    } else {
                        bucketGroup.put(bucketName, Collections.singletonList(bucketName));
                    }

                }
            }
            bucketDateStart = bucketDateStart.plusDays(1);
        }
        return bucketGroup;
    }

    /**
     *按照分页获取符合条件的数据
     * @param bucketName bucketName
     * @param signalObjectCount signalObjectCount
     * @param marker marker
     * @param dataStart  dataStart
     * @param dataEnd dataEnd
     */
    private List<MinioMetaData> listObjectsByPage(String bucketName, Integer signalObjectCount, String marker, String dataStart, String dataEnd) throws Exception {
        List<MinioMetaData> objectFileName = new ArrayList<>();
        Iterable<Result<Item>> results = MinIOServiceHelper.listObjectsByPage(bucketName, signalObjectCount, marker);
        if (results == null) {
            return objectFileName;
        }
        Integer realObjectCount = signalObjectCount;
        for (Result<Item> result : results) {
            //这个results是回调的，并不是objectCount的参数作用，所以需要重新限制
            if (realObjectCount <= 0) {
                break;
            }
            Item item = result.get();
            if (!item.isDir() && item.userMetadata() != null) {
                MinioMetaData build = MinioMetaData.builder()
                        .objectName(item.objectName())
                        .userMetadata(item.userMetadata())
                        .build();
                //时间筛选
                if (isTimeInRange(build.getFileCreateTime(), dataStart, dataEnd)) {
                    objectFileName.add(build);
                    realObjectCount--;
                }

            }
        }
        return objectFileName;
    }


}
