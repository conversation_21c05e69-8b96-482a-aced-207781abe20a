package com.espc.sec.dataimport.monitor.increment.vo;

import com.espc.sec.dataimport.monitor.increment.entity.MonitorIncrementDataPo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 导入导出信息 的展示
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MonitorIncrementDataVo {

    /**
     * 节点
     */
    private String node;
    /**
     * 数据库类型 如hdfs minio es kafka starrocks
     */
    private String databaseType;
    /**
     * 表类型 对应操作的文件或者数据表名字
     */
    private String tableName;

    /**
     * 文件名字。上级结构
     */
    private String fileName;
    /**
     * 导出或者导入的条数
     */
    private Long size;
    /**
     * 导入导出的时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public MonitorIncrementDataVo(MonitorIncrementDataPo incrementDataPo) {
        this.node = incrementDataPo.getNode();
        this.databaseType = incrementDataPo.getDatabaseType();
        this.tableName = incrementDataPo.getTableName();
        this.size = incrementDataPo.getSize();
        this.time = incrementDataPo.getTime();
        this.createTime = incrementDataPo.getCreateTime();
        this.fileName = incrementDataPo.getFileName();
    }

}
