package com.espc.sec.dataexport.common.exception;

public class MinioDownLoadException extends Exception {
    public MinioDownLoadException() {
    }

    public MinioDownLoadException(String message) {
        super(message);
    }

    public MinioDownLoadException(String message, Throwable cause) {
        super(message, cause);
    }

    public MinioDownLoadException(Throwable cause) {
        super(cause);
    }
}
