package com.espc.sec.flow.queue;

import com.espc.sec.flow.aspect.impl.StatImpl;
import com.espc.sec.flow.bean.Constant;
import com.espc.sec.flow.bean.Job;
import com.espc.sec.flow.util.ConfigUtil;
import com.espc.sec.flow.util.FileUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2019-05-17
 */
@Slf4j
public class QueuePutter {
    /**
     * 把一个文件放入到队列中
     *
     * @param tempDirPath 源目录在临时目录中的路径
     * @param sourcePath  文件的源目录
     * @param file        文件
     * @param fileLength  文件大小（字节，此参数在面向切面的切入点方法中使用）
     */
    public void putOneFileIntoQueue(String tempDirPath, String sourcePath, File file, long fileLength) {
        try {
            if (file.exists()) {
                byte priority = getFilePriority(sourcePath, file.getName());
                String filePath = file.getAbsolutePath().substring(sourcePath.length() + 1);
                File tempFile = new File(tempDirPath, filePath);
                if (tempFile.exists()) {
                    if (tempFile.delete()) {
                        log.info(tempFile.getAbsolutePath() + " exists. delete success.");
                    } else {
                        log.error(tempFile.getAbsolutePath() + " exists. delete failed.");
                    }
                }
                FileUtil.moveFile(file, tempFile);
                Job job = new Job(sourcePath, tempFile.getAbsolutePath(), filePath, file.getName(), priority);
                Constant.JOB_QUEUE.put(job);
            }
        } catch (Throwable e) {
            log.error("put " + file.getAbsolutePath() + " into queue failed.", e);
        }
    }

    /**
     * 得到文件的优先级
     *
     * @param sourcePath 文件的源目录
     * @param fileName   文件名
     * @return 返回优先级
     */
    private static byte getFilePriority(String sourcePath, String fileName) {
        String[] names = fileName.split("_");
        if (names.length != StatImpl.fileNameSegSize) {
            return Constant.PRIORITY_LOW;
        }
        String type = names[2];
        if (Constant.HJ_STAT_FILE_TYPE.equalsIgnoreCase(type)) {
            return Constant.PRIORITY_6005;
        }
        if (ConfigUtil.highPriorityUpperTypes.contains(type.toUpperCase())) {
            return Constant.PRIORITY_HIGH;
        }
        if (ConfigUtil.config.getHighPriorityDirs().contains(sourcePath)) {
            return Constant.PRIORITY_HIGH;
        }
        if (ConfigUtil.highPriorityUpperNodes.contains(names[1].toUpperCase())) {
            return Constant.PRIORITY_HIGH;
        }
        return Constant.PRIORITY_LOW;
    }
}
