package com.espc.sec.dataimport.monitor.increment.mapper;

import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataReq;
import com.espc.sec.dataimport.monitor.increment.entity.MonitorIncrementDataPo;
import com.espc.sec.dataimport.monitor.increment.vo.MonitorIncrementDataGroupVo;

import java.util.List;

/**
 * 操作增量导入导出mapper
 */
public interface MonitorIncrementDataMapper  {
    List<MonitorIncrementDataPo> selectByCondition(MonitorIncrementDataReq req);

    int insert(MonitorIncrementDataPo incrementDataPo);

    List<MonitorIncrementDataGroupVo> groupQuery(MonitorIncrementDataReq dto);
}
