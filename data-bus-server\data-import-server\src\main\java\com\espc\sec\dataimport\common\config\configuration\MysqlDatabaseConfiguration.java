package com.espc.sec.dataimport.common.config.configuration;

import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.config.Environment;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * 时间戳数据库配置 - 导入项目版本
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Configuration
public class MysqlDatabaseConfiguration {

    /**
     * 时间戳数据库数据源
     */
    @Bean("mysqlDataSource")
    public DataSource mysqlDataSource() {
        try {
            Environment.MysqlDTO config = Config.environment.mysql;
            HikariConfig hikariConfig = new HikariConfig();
            hikariConfig.setJdbcUrl(config.getUrl());
            hikariConfig.setUsername(config.getUsername());
            hikariConfig.setPassword(config.getPassword());
            hikariConfig.setDriverClassName("com.mysql.cj.jdbc.Driver");

            // Hikari连接池配置
            hikariConfig.setMaximumPoolSize(10);
            hikariConfig.setMinimumIdle(5);
            hikariConfig.setConnectionTimeout(30000);
            hikariConfig.setIdleTimeout(600000);
            hikariConfig.setMaxLifetime(1800000);

            // 连接池名称
            hikariConfig.setPoolName("mysqlHikariPool");

            log.info("数据库连接池配置完成: {}", config.getUrl());
            return new HikariDataSource(hikariConfig);

        } catch (Exception e) {
            log.error("配置时间戳数据库失败", e);
            throw new RuntimeException("配置时间戳数据库失败", e);
        }
    }

    @Bean
    public PlatformTransactionManager transactionManager(@Qualifier("mysqlDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * 时间戳数据库JdbcTemplate
     */
    @Bean("mysqlJdbcTemplate")
    public JdbcTemplate timestampJdbcTemplate() {
        return new JdbcTemplate(mysqlDataSource());
    }
}