package com.espc.sec.dataimport.hdfs.schedule;

import com.espc.sec.dataimport.common.constant.LogKeyword;
import com.espc.sec.dataimport.common.enums.ImportTypeEnum;
import com.espc.sec.dataimport.hdfs.service.HdfsImporter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class HdfsScheduler {
    @Autowired
    private HdfsImporter hdfsImporter;

    @Scheduled(fixedDelayString = "#{T(com.espc.sec.dataimport.common.config.Config).importConfig.hdfs.getFixedDelayMills()}")
    public void run() {
        log.info(LogKeyword.HDFS_IMPORT + "定时任务开始");
        hdfsImporter.import0(ImportTypeEnum.HDFS);
    }
}
