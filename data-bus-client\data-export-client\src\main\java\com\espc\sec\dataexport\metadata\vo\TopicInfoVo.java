package com.espc.sec.dataexport.metadata.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Kafka主题信息VO
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopicInfoVo {
    /**
     * 主题名称
     */
    private String topicName;
    
    /**
     * 分区数
     */
    private Integer partitions;
    
    /**
     * 副本数
     */
    private Integer replicas;
    
    /**
     * 描述
     */
    private String description;
}