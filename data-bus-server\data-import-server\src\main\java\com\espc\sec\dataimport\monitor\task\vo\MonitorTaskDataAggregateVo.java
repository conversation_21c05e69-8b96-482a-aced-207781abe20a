package com.espc.sec.dataimport.monitor.task.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 监控任务数据聚合视图对象
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@ApiModel("监控任务数据聚合结果")
public class MonitorTaskDataAggregateVo {

    @ApiModelProperty("节点")
    private String node;

    @ApiModelProperty("任务名称")
    private String taskName;

    @ApiModelProperty("数据库类型")
    private String databaseType;

    @ApiModelProperty("表名称")
    private String tableName;

    @ApiModelProperty("汇总大小")
    private Long totalSize;
}