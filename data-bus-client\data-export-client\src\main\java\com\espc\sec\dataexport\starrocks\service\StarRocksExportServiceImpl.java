package com.espc.sec.dataexport.starrocks.service;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.config.ExportConfig;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.dto.Tricycle;
import com.espc.sec.dataexport.common.dto.task.StarRocksTaskProperties;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import com.espc.sec.dataexport.common.exception.BigDataException;
import com.espc.sec.dataexport.common.service.CheckPointService;
import com.espc.sec.dataexport.common.service.IncrementExportService;
import com.espc.sec.dataexport.common.service.TaskExportService;
import com.espc.sec.dataexport.common.util.DateUtil;
import com.espc.sec.dataexport.common.util.MultiThreadUtil;
import com.espc.sec.dataexport.starrocks.dto.StartRocksExportDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: zh
 * @date: 2025/7/28
 */
@Slf4j
@Service
public class StarRocksExportServiceImpl implements IncrementExportService, TaskExportService<StarRocksTaskProperties> {
    @Autowired
    private CheckPointService checkPointService;

    @Autowired
    private StarRocksExporter starRocksExporter;


    @Override
    public void taskExport(StarRocksTaskProperties request) {
        log.info("starRocks任务导出参数：{}", request);
        // 转换请求参数为TableInfo列表
        List<StartRocksExportDto> startRocksExportDtos = new ArrayList<>();
        for (StarRocksTaskProperties.DatabaseConfig databaseConfig : request.getDatabase()) {
            startRocksExportDtos.addAll(
                    buildRequestToExportParam(databaseConfig.getTableName(),
                            DateUtil.parse(databaseConfig.getDataStartTime(), Constants.YYYY_MM_DD_HH_MM_SS).toJdkDate(),
                            DateUtil.parse(databaseConfig.getDataEndTime(), Constants.YYYY_MM_DD_HH_MM_SS).toJdkDate(),
                            ExportModeEnum.TASK,
                            request.getTaskId()
                    )
            );
        }


        if (startRocksExportDtos.isEmpty()) {
            throw new BigDataException("没有有效的表配置信息");
        }
        doExport(startRocksExportDtos);
    }


    @Override
    public void incrementExport() {
        List<StartRocksExportDto> startRocksExportDtos = new ArrayList<>();
        List<ExportConfig.StarRocks.Database> databaseList = Config.exportConfig.starRocks.getDatabase();
        for (ExportConfig.StarRocks.Database database : databaseList) {
            startRocksExportDtos.addAll(
                    buildRequestToExportParam(database.getTableName(),
                            database.getDatabaseName(),
                            database.getTimeField(),
                            null, null,
                            ExportModeEnum.INCREMENT,
                            null
                    )
            );
        }
        doExport(startRocksExportDtos);
    }

    /**
     * 导出
     *
     * @param exportDtos 集合
     */
    private void doExport(List<StartRocksExportDto> exportDtos) {
        List<CompletableFuture<Tricycle<Integer, ExportModeEnum, Date>>> futures = new ArrayList<>();
        //导出
        for (StartRocksExportDto startRocksExportDto : exportDtos) {
            Integer incrementNumber = buildTimeForExportAndRuturnIncrId(startRocksExportDto);

            if (incrementNumber == null) {
                continue;
            }

            futures.add(CompletableFuture.supplyAsync(() -> {
                try {
                    starRocksExporter.export(startRocksExportDto);
                    return new Tricycle<>(incrementNumber, startRocksExportDto.getExportModeEnum(), startRocksExportDto.getEndTime());
                } catch (Exception e) {
                    log.error("starrocks导出错误：{}", startRocksExportDto);
                }
                return new Tricycle<>(incrementNumber, startRocksExportDto.getExportModeEnum(), null);
            }, MultiThreadUtil.executorService));
        }
        // 合并为一个CompletableFuture（等待所有任务完成）
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );
        //如果是增量型，需要写回最新时间到记录表， 获取所有结果
        allFutures.thenApply(v -> {
            List<Tricycle<Integer, ExportModeEnum, Date>> collect = futures.stream()
                    .map(CompletableFuture::join)
                    //更新增量型导出
                    .filter(tricyle -> tricyle.getY().equals(ExportModeEnum.INCREMENT))
                    .filter(tricyle -> tricyle.getZ() != null)
                    .peek(tricyle -> {
                        //写回最新的时间
                        checkPointService.update(tricyle.getX(), tricyle.getZ());
                    })
                    .collect(Collectors.toList());
            return collect;
        });


    }


    private Integer buildTimeForExportAndRuturnIncrId(StartRocksExportDto startRocksExportDto) {
        if (!ExportModeEnum.INCREMENT.equals(startRocksExportDto.getExportModeEnum())) {
            return 0;
        }

        Tricycle<Integer, Date, Date> timeRangeForIndex = checkPointService.getTimeRangeForIndex(ExportTypeEnum.STAR_ROCKS, startRocksExportDto.getTableKey());

        // 是否该同步
        if (timeRangeForIndex == null) {
            return null;
        }
        startRocksExportDto.setStartTime(timeRangeForIndex.getY());
        startRocksExportDto.setEndTime(timeRangeForIndex.getZ());
        return timeRangeForIndex.getX();
    }


    private List<StartRocksExportDto> buildRequestToExportParam(List<String> tableNames
            , Date startTime
            , Date endTime
            , ExportModeEnum exportModeEnum
            , Integer taskId) {
        String finaldatabaseName = Config.exportConfig.starRocks.getDatabase().get(0).getDatabaseName();
        String findaltimeField = Config.exportConfig.starRocks.getDatabase().get(0).getTimeField();
        return buildRequestToExportParam(tableNames
                , finaldatabaseName
                , findaltimeField
                , startTime
                , endTime
                , exportModeEnum
                , taskId);
    }

    private List<StartRocksExportDto> buildRequestToExportParam(List<String> tableNames
            , String databaseName
            , String timeField
            , Date startTime, Date endTime
            , ExportModeEnum exportModeEnum
            , Integer taskId) {
        if (tableNames == null || tableNames.isEmpty()) {
            return new ArrayList<>();
        }
        return tableNames.stream()
                .map(tablename -> new StartRocksExportDto(databaseName
                        , tablename
                        , timeField
                        , startTime
                        , endTime
                        , exportModeEnum
                        , taskId))
                .collect(Collectors.toList());

    }


}
