package com.espc.sec.dataexport.job.service;

import com.espc.sec.dataexport.common.dto.task.TaskProperties;
import com.espc.sec.dataexport.common.enums.JobTypeEnum;

public interface JobService {
    /**
     * 添加任务
     *
     * @param jobId          数据库自增id
     * @param jobTypeEnum    导出类型
     * @param cron           cron表达式
     * @param taskProperties 任务参数
     */
    void addJob(Integer jobId, JobTypeEnum jobTypeEnum, String cron, TaskProperties taskProperties);

    /**
     * 更新任务
     *
     * @param jobId          数据库自增id
     * @param jobTypeEnum    任务类型
     * @param cron           cron表达式
     * @param taskProperties 任务参数
     */
    void updateJob(Integer jobId, JobTypeEnum jobTypeEnum, String cron, TaskProperties taskProperties);

    /**
     * 删除任务
     *
     * @param jobId 数据库自增id
     */
    void deleteJob(Integer jobId);
}
