package com.espc.sec.dataexport.common.util;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025/7/23
 */
@Slf4j
public class MultiThreadUtil {
    public static final int CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;
    public static final ExecutorService executorService = new ThreadPoolExecutor(
            CORE_POOL_SIZE
            , CORE_POOL_SIZE * 2
            , 60
            , TimeUnit.SECONDS
            , new LinkedBlockingQueue<>(10000)
            , new ThreadFactory() {
        private final AtomicInteger threadNumber = new AtomicInteger(1);

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, "custom-pool-" + threadNumber.getAndIncrement());
            thread.setDaemon(false);
            return thread;
        }
    },
            new ThreadPoolExecutor.CallerRunsPolicy()
    );


    public static void execute(Runnable thread) {
        executorService.execute(thread);
    }

    public static <T> Future<T> submit(Callable<T> task) {
        return executorService.submit(task);
    }

    public static void executeAll(Collection<Callable<Void>> tasks) {
        List<Future<?>> futures = new ArrayList<>();
        for (Callable<Void> task : tasks) {
            futures.add(submit(task));
        }

        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (InterruptedException e) {
                log.error("执行中断", e);
            } catch (ExecutionException e) {
                log.error("执行异常", e);
            }
        }
    }

    public static <T> List<T> submitAll(Collection<Callable<T>> tasks) {
        List<Future<T>> futures = new ArrayList<>();
        for (Callable<T> task : tasks) {
            futures.add(submit(task));
        }

        List<T> results = new ArrayList<>(futures.size());
        for (Future<T> future : futures) {
            try {
                results.add(future.get());
            } catch (InterruptedException e) {
                log.error("请求被中断", e);
            } catch (ExecutionException e) {
                log.error("执行异常", e);
            }
        }
        return results;
    }
}
