package com.espc.sec.flow.business;

import cn.hutool.extra.spring.SpringUtil;
import com.espc.sec.flow.bean.Constant;
import com.espc.sec.flow.bean.Job;
import com.espc.sec.flow.queue.QueuePutter;
import com.espc.sec.flow.thread.GetFileThreadNew;
import com.espc.sec.flow.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019-08-26
 */
@Slf4j
public class FileReader implements Runnable {
    @Override
    public void run() {
        //把临时目录的文件先读到队列
        putTempFileIntoQueue();

        Set<String> nodePathSet = new HashSet<>();
        QueuePutter putter = SpringUtil.getBean("queuePutter");
        while (true) {
            for (String sourcePath : ConfigUtil.config.getSourceDir()) {
                String tempDirPath = getTempSourceDir(sourcePath);
                File source = new File(sourcePath);
                File[] files = source.listFiles();
                if (files != null && files.length > 0) {
                    for (File file : files) {
                        if (file.isFile()) {
                            putter.putOneFileIntoQueue(tempDirPath, sourcePath, file, file.length());
                        } else {
                            if (!nodePathSet.contains(file.getAbsolutePath())) {
                                new Thread(new GetFileThreadNew(sourcePath, file)).start();
                                nodePathSet.add(file.getAbsolutePath());
                            }
                        }
                    }
                }
            }
            try {
                log.info("scan source dir over. thread sleep one hour.");
                Thread.sleep(3600 * 1000);
            } catch (InterruptedException e) {
                log.error("sleep exception.", e);
            }
        }
    }

    /**
     * 得到源目录在临时目录中对应的绝对路径
     *
     * @param sourcePath 源目录的路径
     * @return 源目录在临时目录中对应的绝对路径
     */
    public static String getTempSourceDir(String sourcePath) {
        String name = sourcePath.replace(":", "").replace("\\", "_").replace("/", "_");
        File tempSourceDir = new File(ConfigUtil.config.getTempDir(), name);
        if (!tempSourceDir.exists()) {
            if (!tempSourceDir.mkdirs()) {
                log.error("make dir failed. dir path:" + tempSourceDir);
            } else {
                log.info("make dir success. path:" + tempSourceDir);
            }
        }
        return tempSourceDir.getAbsolutePath();
    }


    /**
     * 把临时目录中的文件放到队列中
     */
    public static void putTempFileIntoQueue() {
        Set<String> sourceDirs = ConfigUtil.config.getSourceDir();
        Map<String, String> sourceNameMap = new HashedMap(sourceDirs.size());
        for (String dir : sourceDirs) {
            String name = dir.replace(":", "").replace("\\", "_").replace("/", "_");
            sourceNameMap.put(name, dir);
        }

        File[] lowerFiles = new File(ConfigUtil.config.getTempDir()).listFiles();
        if (lowerFiles != null && lowerFiles.length > 0) {
            for (File lowerFile : lowerFiles) {
                if (lowerFile.isDirectory()) {
                    SimpleFileVisitor<Path> finder = new SimpleFileVisitor<Path>() {
                        @Override
                        public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                            putOneTempFileIntoQueue(file.toFile(), sourceNameMap, lowerFile.getAbsolutePath(), lowerFile.getName());
                            return FileVisitResult.CONTINUE;
                        }
                    };

                    try {
                        Files.walkFileTree(Paths.get(lowerFile.getAbsolutePath()), finder);
                    } catch (IOException e) {
                        log.info("walkFileTree failed, dir:" + lowerFile.getAbsolutePath(), e);
                    }
                }
            }
        }
    }

    /**
     * 把临时目录里的一个文件放到队列
     *
     * @param file           文件
     * @param sourceNameMap  源目录移动到临时目录的目录名对应原始目录路径的映射
     * @param tempSourceDir  源目录在临时目录中的绝对路径
     * @param tempSourceName 源目录在临时目录中的目录名
     */
    public static void putOneTempFileIntoQueue(File file, Map<String, String> sourceNameMap, String tempSourceDir, String tempSourceName) {
        try {
            if (file.exists()) {
                byte priority = Constant.PRIORITY_HIGH;
                String filePath = file.getAbsolutePath().substring(tempSourceDir.length() + 1);
                for (String name : sourceNameMap.keySet()) {
                    if (name.equals(tempSourceName)) {
                        Job job = new Job(sourceNameMap.get(name), file.getAbsolutePath(), filePath, file.getName(), priority);
                        Constant.JOB_QUEUE.put(job);
                        return;
                    }
                }
            }
        } catch (Throwable e) {
            log.error("put " + file.getAbsolutePath() + " into queue failed.", e);
        }
    }
}
