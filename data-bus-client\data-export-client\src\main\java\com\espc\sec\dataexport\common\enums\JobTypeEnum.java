package com.espc.sec.dataexport.common.enums;

import com.espc.sec.dataexport.common.dto.task.*;
import com.espc.sec.dataexport.job.task.*;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@AllArgsConstructor
@Getter
public enum JobTypeEnum {
    /**
     * 任务类型
     */
    ES(ExportTypeEnum.ES.getCode(), EsTask.class, ElasticsearchTaskProperties.class),
    STAR_ROCKS(ExportTypeEnum.STAR_ROCKS.getCode(), StarRocksTask.class, StarRocksTaskProperties.class),
    TIDB(ExportTypeEnum.TIDB.getCode(), TidbTask.class, TidbTaskProperties.class),
    KAFKA(ExportTypeEnum.KAFKA.getCode(), KafkaTask.class, KafkaTaskProperties.class),
    MINIO(ExportTypeEnum.MINIO.getCode(), MinioTask.class, MinioTaskProperties.class),
    HDFS(ExportTypeEnum.HDFS.getCode(), HdfsTask.class, HdfsTaskProperties.class),
    ;

    private final String code;
    private final Class<? extends AbstractTask<? extends TaskProperties>> taskClass;
    private final Class<? extends TaskProperties> taskProperties;

    public static JobTypeEnum getByCode(String code) {
        return Stream.of(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
