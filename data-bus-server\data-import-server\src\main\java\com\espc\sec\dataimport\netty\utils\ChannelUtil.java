package com.espc.sec.dataimport.netty.utils;

import io.netty.channel.Channel;
import io.netty.channel.group.ChannelGroup;
import io.netty.channel.group.DefaultChannelGroup;
import io.netty.util.concurrent.GlobalEventExecutor;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @date 2025/7/17
 **/
public class ChannelUtil {
    private static ChannelGroup globalGroup = new DefaultChannelGroup(GlobalEventExecutor.INSTANCE);
    private static ConcurrentMap<String, Channel> channelMap = new ConcurrentHashMap();

    public static void addChannel(String node, Channel channel) {
        globalGroup.add(channel);
        String key = node + ":" + channel.id().toString();
        channelMap.putIfAbsent(key, channel);
    }

    public static void removeChannel(Channel channel) {
        globalGroup.remove(channel);
        for (Map.Entry<String, Channel> entry : channelMap.entrySet()) {
            String key = entry.getKey();
            if (key.contains(channel.id().toString())) {
                channelMap.remove(key);
                break;
            }
        }
    }

    public static Channel getChannel(String node) {
        Channel channel = null;
        for (Map.Entry<String, Channel> entry : channelMap.entrySet()) {
            String key = entry.getKey();
            Channel value = entry.getValue();
            if (key.contains(node)) {
                channel = value;
                break;
            }
        }
        return channel;
    }
}
