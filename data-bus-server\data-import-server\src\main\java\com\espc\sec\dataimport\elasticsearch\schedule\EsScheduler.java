package com.espc.sec.dataimport.elasticsearch.schedule;


import com.espc.sec.dataimport.common.constant.LogKeyword;
import com.espc.sec.dataimport.common.enums.ImportTypeEnum;
import com.espc.sec.dataimport.elasticsearch.service.ElasticsearchImporter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 导入定时任务
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class EsScheduler {

    @Autowired
    private ElasticsearchImporter elasticsearchImporter;

    /**
     * 定时导入任务
     * 使用配置文件中的cron表达式
     */
    @Scheduled(fixedDelayString = "#{T(com.espc.sec.dataimport.common.config.Config).importConfig.elasticsearch.getFixedDelayMills()}")
    public void executeImportTask() {
        log.info(LogKeyword.ES_IMPORT + "定时任务开始");
        elasticsearchImporter.import0(ImportTypeEnum.ES);
    }

}