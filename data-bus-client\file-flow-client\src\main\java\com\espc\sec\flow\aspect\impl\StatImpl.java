package com.espc.sec.flow.aspect.impl;

import com.espc.sec.flow.aspect.IStat;
import com.espc.sec.flow.bean.Constant;
import com.espc.sec.flow.bean.StatStruct;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-09-19
 */
@Slf4j
public class StatImpl implements IStat {
    /**
     * 标准文件名按照下划线分割的分段数
     */
    public static int fileNameSegSize = Constant.FILE_NAME_EXAMPLE.split("_").length;

    /**
     * 统计分流的文件数量
     * 切入点：putOneFileIntoQueue(String tempDirPath, String sourcePath, File file)
     */
    @Override
    public void statFlow(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length < 3) {
            log.info("putOneFileIntoQueue param invalid.");
            return;
        }
        File file = (File) args[2];
        long size = (long) args[3];

        statOneFile(file, STAT_FLOW_MAP, size);
    }

    /**
     * 统计一个文件
     *
     * @param file    文件
     * @param statMap 存放统计结果
     * @param size    文件大小
     */
    private void statOneFile(File file, Map<String, Map<String, Map<String, StatStruct>>> statMap, long size) {
        String fileName = file.getName();
        String[] names = fileName.split("_");
        if (names.length != fileNameSegSize) {
            return;
        }

        String node = names[1];
        String type = names[2];

        statOneFile(statMap, node, type, size);
    }

    /**
     * 统计一个文件
     *
     * @param statMap  统计结果
     * @param node     节点
     * @param type     数据类型
     * @param fileSize 文件大小（字节）
     */
    public static void statOneFile(Map<String, Map<String, Map<String, StatStruct>>> statMap,
                                   String node, String type, long fileSize) {
        String nowHour = new SimpleDateFormat("yyyy-MM-dd HH").format(new Date());

        if (!statMap.containsKey(nowHour)) {
            statMap.put(nowHour, new HashMap<>(16));
        }
        Map<String, Map<String, StatStruct>> nodeMap = statMap.get(nowHour);
        if (!nodeMap.containsKey(node)) {
            nodeMap.put(node, new HashMap<>(16));
        }
        Map<String, StatStruct> typeMap = nodeMap.get(node);
        if (!typeMap.containsKey(type)) {
            typeMap.put(type, new StatStruct());
        }
        typeMap.get(type).add(1, fileSize);
    }
}
