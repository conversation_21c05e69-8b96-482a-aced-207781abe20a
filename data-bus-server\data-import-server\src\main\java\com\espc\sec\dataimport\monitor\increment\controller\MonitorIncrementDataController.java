package com.espc.sec.dataimport.monitor.increment.controller;

import com.espc.sec.dataimport.common.dto.Result;
import com.espc.sec.dataimport.common.vo.PageVo;
import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataReq;
import com.espc.sec.dataimport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataimport.monitor.increment.vo.MonitorIncrementDataGroupVo;
import com.espc.sec.dataimport.monitor.increment.vo.MonitorIncrementDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/monitorIncrementData")
@Api(tags = "增量导入导出监控")
public class MonitorIncrementDataController {


    @Resource
    private MonitorIncrementDataService monitorIncrementDataService;

    @PostMapping("/list")
    @ApiOperation("查询监控信息")
    public Result<PageVo<MonitorIncrementDataVo>> list(@RequestBody MonitorIncrementDataReq monitorIncrementDataReq) {
        PageVo<MonitorIncrementDataVo> taskList = monitorIncrementDataService.page(monitorIncrementDataReq);
        return Result.success(taskList);
    }

    @PostMapping("/create")
    @ApiOperation("创建监控信息")
    public Result<Integer> create(@RequestBody MonitorIncrementDataDto monitorIncrementDataDto) {
        Integer monitorIncrementDataId = monitorIncrementDataService.create(monitorIncrementDataDto);
        return Result.success(monitorIncrementDataId);
    }

    @PostMapping("/group")
    @ApiOperation("分组查询监控信息")
    public Result<PageVo<MonitorIncrementDataGroupVo>> group(@RequestBody MonitorIncrementDataReq monitorIncrementDataDto) {
        PageVo<MonitorIncrementDataGroupVo> monitorIncrementDataGroup = monitorIncrementDataService.group(monitorIncrementDataDto);
        return Result.success(monitorIncrementDataGroup);
    }


}
