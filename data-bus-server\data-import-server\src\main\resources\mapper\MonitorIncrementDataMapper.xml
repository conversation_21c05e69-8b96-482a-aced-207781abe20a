<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.espc.sec.dataimport.monitor.increment.mapper.MonitorIncrementDataMapper">

    <resultMap id="BaseResultMap" type="com.espc.sec.dataimport.monitor.increment.entity.MonitorIncrementDataPo">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="node" property="node" jdbcType="VARCHAR"/>
        <result column="database_type" property="databaseType" jdbcType="VARCHAR"/>
        <result column="table_name" property="tableName" jdbcType="VARCHAR"/>
        <result column="size" property="size" jdbcType="INTEGER"/>
        <result column="time" property="time" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="file_name" property="fileName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , node, database_type, table_name, size, time, create_time,file_name
    </sql>

    <select id="selectByCondition" resultMap="BaseResultMap" parameterType="com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataReq">
        SELECT
        <include refid="Base_Column_List"/>
        FROM monitor_increment_data
        <where>
            <if test="node != null and node != ''">
                and node = #{node, jdbcType=VARCHAR}
            </if>
            <if test="databaseType != null and databaseType != ''">
                and database_type = #{databaseType, jdbcType=VARCHAR}
            </if>
            <if test="tableName != null and tableName != ''">
                and table_name LIKE CONCAT('%', #{tableName}, '%')
            </if>
            <if test="startTime != null and startTime != ''">
                and time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and time &lt;= #{endTime}
            </if>
        </where>
        order by id desc

    </select>

    <select id="groupQuery" parameterType="com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataReq"
            resultType="com.espc.sec.dataimport.monitor.increment.vo.MonitorIncrementDataGroupVo">
        SELECT node, database_type as databaseType, table_name as tableName,sum(size) as size
        FROM monitor_increment_data
        <where>
            <if test="node != null and node != ''">
                and node = #{node, jdbcType=VARCHAR}
            </if>
            <if test="databaseType != null and databaseType != ''">
                and database_type = #{databaseType, jdbcType=VARCHAR}
            </if>
            <if test="tableName != null and tableName != ''">
                and table_name LIKE CONCAT('%', #{tableName}, '%')
            </if>
            <if test="startTime != null and startTime != ''">
                and time &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and time &lt;= #{endTime}
            </if>
        </where>
        GROUP BY node, database_type, table_name
    </select>

    <insert id="insert" parameterType="com.espc.sec.dataimport.monitor.increment.entity.MonitorIncrementDataPo">
        INSERT INTO monitor_increment_data (id,node, database_type, table_name, size,
                               time, create_time, file_name)
        VALUES (#{id,jdbcType=VARCHAR},
                #{node,jdbcType=VARCHAR},
                #{databaseType,jdbcType=VARCHAR},
                #{tableName,jdbcType=VARCHAR},
                #{size,jdbcType=INTEGER},
                #{time,jdbcType=TIMESTAMP},
                #{createTime,jdbcType=TIMESTAMP},
                #{fileName, jdbcType=VARCHAR}
              )
    </insert>


</mapper>