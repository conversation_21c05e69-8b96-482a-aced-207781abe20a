package com.espc.sec.dataexport.common.constant;


import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;

public class Constants {
    public static final String ARN_MINIO_SQS_MINIO_KAFKA = "arn:minio:sqs::minio-kafka:kafka";

    public static final String MILLS_FORMAT = "yyyyMMddHHmmssSSS";
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";


    public static final DateTimeFormatter MILLS_DAY_MONTH_YEAR_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");

    public static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public static final DateTimeFormatter YEAR_FORMAT = DateTimeFormatter.ofPattern("yyyy");

    public static final DateTimeFormatter MONTH_FORMAT = DateTimeFormatter.ofPattern("M");

    public static final DateTimeFormatter DAY_FORMAT = DateTimeFormatter.ofPattern("d");

    public static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);

    public static final Integer MINIO_PAGE_OBJECT_MAX = 1000;

    public static final String SEPARATOR_LINE = "-";
    /**
     * minio默认给了前缀和第一个字母大写
     */
    public static final String MINIO_USRMETADATA_CREATE_TIME = "Miniocreatetime";

    public static final String MINIO_METADATA_SEPARATOR = "X-Amz-Meta-";
    public static final String DATA_NODE = "node";


    public static class Quartz {
        public static final String JOB_GROUP = "kt";
        public static final String TASK_PARAMS = "TaskParams";
        public static final String EXPORT_TYPE_TASK_PARAMS = "ExportTypeTaskParams";
    }
}
