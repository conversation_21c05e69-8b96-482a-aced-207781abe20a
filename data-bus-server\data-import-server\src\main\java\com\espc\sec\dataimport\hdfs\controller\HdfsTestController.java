package com.espc.sec.dataimport.hdfs.controller;


import com.espc.sec.dataimport.common.util.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/hdfs")
public class HdfsTestController {
    /**
     * 手动上传文件到本地路径
     */
    @GetMapping("/upload")
    public void upload(String localPath) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            sb.append(i);
        }
        FileUtils.writeStringToFile(localPath, sb.toString());
    }

}