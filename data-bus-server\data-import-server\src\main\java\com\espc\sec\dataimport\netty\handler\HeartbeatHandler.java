package com.espc.sec.dataimport.netty.handler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.espc.sec.dataimport.netty.entity.MessageTypeEnum;
import com.espc.sec.dataimport.netty.entity.MessageWrapper;
import com.espc.sec.dataimport.netty.utils.ChannelUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2025/7/17
 **/
@Slf4j
public class HeartbeatHandler extends ChannelInboundHandlerAdapter {
    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) {
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent event = (IdleStateEvent) evt;
            if (event.state() == IdleState.READER_IDLE) {
                // 读写空闲超时,关闭连接
                ChannelUtil.removeChannel(ctx.channel());
                ctx.channel().close();
                log.error("客户端无心跳，断开连接:{}", ctx.channel().remoteAddress());
            }
        }
    }

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        MessageWrapper messageWrapper = (MessageWrapper) msg;
        if (messageWrapper.getType().getCode() != MessageTypeEnum.HEART_BEAT.getCode()) {
            ctx.fireChannelRead(msg);
        }
        String body = StrUtil.str(messageWrapper.getBody(), StandardCharsets.UTF_8);
        MessageWrapper.HeartbeatInfo heartbeatInfo = JSONUtil.toBean(body, MessageWrapper.HeartbeatInfo.class);

        // 保存客户端连接
        ChannelUtil.addChannel(heartbeatInfo.getNode(), ctx.channel());
        log.info("收到客户端心跳, 地址: {}, 节点: {}, 时间: {}", ctx.channel().remoteAddress(), heartbeatInfo.getNode(), heartbeatInfo.getTime());
    }
}
