package com.espc.sec.flow.thread;

import cn.hutool.extra.spring.SpringUtil;
import com.espc.sec.flow.business.FileReader;
import com.espc.sec.flow.queue.QueuePutter;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;

/**
 * <AUTHOR>
 * @date 2019-08-26
 */
@Slf4j
public class GetFileThreadNew implements Runnable {
    /**
     * 源目录的路径
     */
    private String sourcePath;
    /**
     * 需要读取文件的目录
     */
    private File nodeDir;

    public GetFileThreadNew(String sourcePath, File nodeDir) {
        this.sourcePath = sourcePath;
        this.nodeDir = nodeDir;
    }

    @Override
    public void run() {
        String tempDirPath = FileReader.getTempSourceDir(sourcePath);
        QueuePutter putter = SpringUtil.getBean("queuePutter");

        SimpleFileVisitor<Path> finder = new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) {
                try {
                    long start = System.currentTimeMillis();
                    File findFile = file.toFile();
                    putter.putOneFileIntoQueue(tempDirPath, sourcePath, findFile, findFile.length());
                    long end = System.currentTimeMillis();
                    log.info(file.toAbsolutePath() + " put into queue cost:" + (end - start));
                    return FileVisitResult.CONTINUE;
                } catch (Throwable e) {
                    log.error(file.toAbsolutePath() + " put into queue failed", e);
                    return FileVisitResult.CONTINUE;
                }
            }
        };

        while (true) {
            try {
                Files.walkFileTree(Paths.get(nodeDir.getAbsolutePath()), finder);
            } catch (IOException e) {
                log.error("walkFileTree failed, dir:" + nodeDir.getAbsolutePath(), e);
            }
            log.info("walkFileTree over, dir:" + nodeDir.getAbsolutePath() + ", thread sleep 60s.");
            try {
                Thread.sleep(60 * 1000);
            } catch (InterruptedException e) {
                log.error("sleep exception.", e);
            }
        }
    }

}
