package com.espc.sec.dataexport.job.task;

import com.espc.sec.dataexport.common.dto.task.HdfsTaskProperties;
import com.espc.sec.dataexport.common.util.SpringBeanUtil;
import com.espc.sec.dataexport.hdfs.service.HdfsExportServiceImpl;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class HdfsTask extends AbstractTask<HdfsTaskProperties> {
    @Override
    protected void doExecute(HdfsTaskProperties properties) throws Exception {
        HdfsExportServiceImpl exportService = SpringBeanUtil.getBean(HdfsExportServiceImpl.class);
        exportService.taskExport(properties);
    }
}
