package com.espc.sec.dataimport.netty.entity;

import com.espc.sec.dataimport.common.enums.OperateTypeEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/16
 **/
@Data
public class MessageWrapper {
    /**
     * 消息类型: 1-心跳 2-字符串 3-文件
     */
    private MessageTypeEnum type;
    /**
     * 数据操作类型: create update delete
     */
    private OperateTypeEnum operateType;
    /**
     * 数据体长度
     */
    private int length;
    /**
     * 数据体
     */
    private byte[] body;

    @Data
    public static class HeartbeatInfo {
        /**
         * 节点-四川 sichuan
         */
        private String node;
        /**
         * 心跳时间
         */
        private String time;
    }
}
