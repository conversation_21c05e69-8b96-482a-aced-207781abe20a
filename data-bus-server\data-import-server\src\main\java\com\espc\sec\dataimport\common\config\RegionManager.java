package com.espc.sec.dataimport.common.config;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 地区管理器 - 支持编码和全拼小写映射
 * 管理全国主要地区的编码、名称和全拼小写映射
 */
public class RegionManager {

    // 地区编码到名称的映射
    private static final Map<String, String> CODE_TO_NAME = new HashMap<>();

    // 地区编码到全拼小写的映射
    private static final Map<String, String> CODE_TO_PINYIN = new HashMap<>();

    // 地区名称到编码的映射
    private static final Map<String, String> NAME_TO_CODE = new HashMap<>();

    // 全拼小写到编码的映射
    private static final Map<String, String> PINYIN_TO_CODE = new HashMap<>();

    static {
        // 直辖市
        addRegion("BJ", "北京", "beijing");
        addRegion("SH", "上海", "shanghai");
        addRegion("TJ", "天津", "tianjin");
        addRegion("CQ", "重庆", "chongqing");

        // 省会城市和重要城市
        addRegion("GZ", "广州", "guangzhou");
        addRegion("SZ", "深圳", "shenzhen");
        addRegion("HZ", "杭州", "hangzhou");
        addRegion("NJ", "南京", "nanjing");
        addRegion("WH", "武汉", "wuhan");
        addRegion("CD", "成都", "chengdu");
        addRegion("XA", "西安", "xian");
        addRegion("SY", "沈阳", "shenyang");
        addRegion("DL", "大连", "dalian");
        addRegion("QD", "青岛", "qingdao");
        addRegion("JN", "济南", "jinan");
        addRegion("ZZ", "郑州", "zhengzhou");
        addRegion("CS", "长沙", "changsha");
        addRegion("NC", "南昌", "nanchang");
        addRegion("HF", "合肥", "hefei");
        addRegion("FZ", "福州", "fuzhou");
        addRegion("XM", "厦门", "xiamen");
        addRegion("KM", "昆明", "kunming");
        addRegion("GY", "贵阳", "guiyang");
        addRegion("NN", "南宁", "nanning");
        addRegion("HK", "海口", "haikou");
        addRegion("SJZ", "石家庄", "shijiazhuang");
        addRegion("TY", "太原", "taiyuan");
        addRegion("HH", "呼和浩特", "huhehaote");
        addRegion("CC", "长春", "changchun");
        addRegion("HEB", "哈尔滨", "haerbin");
        addRegion("LS", "拉萨", "lasa");
        addRegion("XN", "西宁", "xining");
        addRegion("YC", "银川", "yinchuan");
        addRegion("WLQ", "乌鲁木齐", "wulumuqi");

        // 其他重要城市
        addRegion("WX", "无锡", "wuxi");
        addRegion("SZ2", "苏州", "suzhou");
        addRegion("NB", "宁波", "ningbo");
        addRegion("WZ", "温州", "wenzhou");
        addRegion("DG", "东莞", "dongguan");
        addRegion("FS", "佛山", "foshan");
        addRegion("ZH", "珠海", "zhuhai");

        // 省份级别
        addRegion("SC", "四川", "sichuan");
        addRegion("GD", "广东", "guangdong");
        addRegion("JS", "江苏", "jiangsu");
        addRegion("ZJ", "浙江", "zhejiang");
        addRegion("SD", "山东", "shandong");
        addRegion("HN", "河南", "henan");
        addRegion("HB", "湖北", "hubei");
        addRegion("HN2", "湖南", "hunan");
        addRegion("AH", "安徽", "anhui");
        addRegion("FJ", "福建", "fujian");
        addRegion("JX", "江西", "jiangxi");
        addRegion("YN", "云南", "yunnan");
        addRegion("GZ2", "贵州", "guizhou");
        addRegion("GX", "广西", "guangxi");
        addRegion("HI", "海南", "hainan");
        addRegion("HE", "河北", "hebei");
        addRegion("SX", "山西", "shanxi");
        addRegion("NM", "内蒙古", "neimenggu");
        addRegion("LN", "辽宁", "liaoning");
        addRegion("JL", "吉林", "jilin");
        addRegion("HL", "黑龙江", "heilongjiang");
        addRegion("XZ", "西藏", "xizang");
        addRegion("QH", "青海", "qinghai");
        addRegion("NX", "宁夏", "ningxia");
        addRegion("XJ", "新疆", "xinjiang");
    }

    /**
     * 添加地区映射
     */
    private static void addRegion(String code, String name, String pinyin) {
        CODE_TO_NAME.put(code, name);
        CODE_TO_PINYIN.put(code, pinyin);
        NAME_TO_CODE.put(name, code);
        PINYIN_TO_CODE.put(pinyin, code);
    }

    /**
     * 根据编码获取地区名称
     */
    public static String getNameByCode(String code) {
        return CODE_TO_NAME.get(code);
    }

    /**
     * 根据编码获取全拼小写
     */
    public static String getPinyinByCode(String code) {
        return CODE_TO_PINYIN.get(code);
    }

    /**
     * 根据名称获取地区编码
     */
    public static String getCodeByName(String name) {
        return NAME_TO_CODE.get(name);
    }

    /**
     * 根据全拼小写获取地区编码
     */
    public static String getCodeByPinyin(String pinyin) {
        return PINYIN_TO_CODE.get(pinyin);
    }

    /**
     * 检查地区编码是否有效
     */
    public static boolean isValidCode(String code) {
        return CODE_TO_NAME.containsKey(code);
    }

    /**
     * 检查全拼小写是否有效
     */
    public static boolean isValidPinyin(String pinyin) {
        return PINYIN_TO_CODE.containsKey(pinyin);
    }

    /**
     * 获取所有地区编码
     */
    public static Set<String> getAllCodes() {
        return CODE_TO_NAME.keySet();
    }

    /**
     * 获取所有全拼小写
     */
    public static Set<String> getAllPinyins() {
        return PINYIN_TO_CODE.keySet();
    }

    /**
     * 获取默认地区编码
     */
    public static String getDefaultCode() {
        return "SC"; // 默认使用四川
    }

    /**
     * 获取默认地区全拼小写
     */
    public static String getDefaultPinyin() {
        return "sichuan";
    }
}