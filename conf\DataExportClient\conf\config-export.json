{"elasticsearch": {"incrementEnable": true, "cron": "0 */1 * * * ?", "indexName": ["pr-inf-http-metadata-1", "pr-inf-http-metadata-2"], "timeField": "@createtime"}, "starRocks": {"incrementEnable": true, "cron": "0 */1 * * * ?", "database": [{"databaseName": "probe_center", "tableName": ["probe_status", "probe_status1"], "timeField": "create_time"}]}, "tidb": {"incrementEnable": true, "cron": "0 */1 * * * ?", "database": [{"databaseName": "probe-center", "tableName": ["probe_status", "probe_status1"], "timeField": "create_time"}]}, "kafka": {"incrementEnable": true, "cron": "0 */1 * * * ?", "topicName": ["test-pr-v3-flow-1", "test-pr-v3-flow-2"]}, "minio": {"incrementEnable": true, "buckets": [{"prefix": "pr-inf-cert", "mode": "d"}, {"prefix": "pr-inf-file", "mode": "d"}]}, "hdfs": {"incrementEnable": true, "listenPath": "/data/pcap"}}