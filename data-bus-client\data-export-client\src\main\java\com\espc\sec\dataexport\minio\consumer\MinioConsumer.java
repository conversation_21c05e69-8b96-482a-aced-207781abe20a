package com.espc.sec.dataexport.minio.consumer;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import com.espc.sec.dataexport.common.util.JsonUtil;
import com.espc.sec.dataexport.minio.dto.MinioExportDto;
import com.espc.sec.dataexport.minio.service.MinioExportServiceImpl;
import com.espc.sec.dataexport.minio.service.MinioExporter;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

@Component
@Slf4j
public class MinioConsumer {

    @Autowired
    private MinioExportServiceImpl minioExportService;

    @KafkaListener(topics = "kt-minio", groupId = "kt-minio", containerFactory = "myKafkaListenerContainerFactory")
    public void listen(String message, Acknowledgment acknowledgment) {
        if (!Config.exportConfig.minio.getIncrementEnable()) {
            return;
        }
        log.info(LogKeyword.MINIO_EXPORT + "收到kafka消息{}", message);
        try {
            minioExportService.incrementExport(message);
        } catch (Exception e) {
            log.error("minio导出错误", e);
            return;
        }
        acknowledgment.acknowledge();
    }

}
