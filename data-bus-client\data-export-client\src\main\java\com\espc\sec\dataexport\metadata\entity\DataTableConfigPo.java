package com.espc.sec.dataexport.metadata.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 数据表配置实体类
 *
 * <AUTHOR>
 * @date 2025-08-29
 */
@Data
@TableName("data_table_config")
public class DataTableConfigPo {
    
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /**
     * 数据类型：elasticsearch、starrocks、kafka、minio、hdfs、mysql、tidb
     */
    private String dataType;
    
    /**
     * 对应库表桶等信息
     */
    private String dataKey;
    
    /**
     * 对应节点
     */
    private String node;
    
    /**
     * 首次创建时间
     */
    private Date createTime;
}