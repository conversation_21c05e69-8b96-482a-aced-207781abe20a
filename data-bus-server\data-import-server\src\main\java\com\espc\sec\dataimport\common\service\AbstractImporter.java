package com.espc.sec.dataimport.common.service;

import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.enums.ImportTypeEnum;
import com.espc.sec.dataimport.common.util.FileUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 导入抽象类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractImporter implements Importer {
    @Override
    public void import0(ImportTypeEnum importTypeEnum) {
        if (!Config.commonConfig.getSupportedImportTypes().contains(importTypeEnum.getCode())) {
            log.debug("{}导出开关关闭", importTypeEnum);
            return;
        }
        long start = System.currentTimeMillis();
        List<File> absFilePaths = FileUtils.listFiles(ImportTypeEnum.getImportPath(importTypeEnum))
                .stream()
                .limit(ImportTypeEnum.getScanFileSize(importTypeEnum))
                .map(File::new)
                .collect(Collectors.toList());
        log.info(importTypeEnum.getLogKeyWord() + "导入开始,本地文件列表{}", absFilePaths);

        for (File file : absFilePaths) {
            try {
                if (!validateFile(file)) {
                    log.error("{}文件名{}校验失败", importTypeEnum.getLogKeyWord(), file.getName());
                    moveToErrorDir(importTypeEnum, file);
                    continue;
                }
                doImport(file);
                FileUtils.delete(file.getAbsolutePath());
            } catch (Exception e) {
                log.error("{}导入失败", importTypeEnum.getLogKeyWord(), e);
                moveToErrorDir(importTypeEnum, file);
            }
        }
        log.info("{}导入结束,耗时{}ms", importTypeEnum.getLogKeyWord(), System.currentTimeMillis() - start);
    }

    private boolean validateFile(File file) {
        String node = file.getName().split("_")[3];
        return Config.commonConfig.getSupportedNodeNames().contains(node)
                && validateFileName(file.getName());
    }

    private void moveToErrorDir(ImportTypeEnum importTypeEnum, File file) {
        FileUtils.move(file.getAbsolutePath(), ImportTypeEnum.getImportErrorPath(importTypeEnum) + File.separator + file.getName());
    }

    /**
     * 执行导入
     *
     * @param file 文件
     * @return 导入数量
     * @throws Exception
     */
    protected abstract void doImport(File file) throws Exception;

    /**
     * 校验文件名
     *
     * @param fileName
     * @return
     */
    protected boolean validateFileName(String fileName) {
        return true;
    }
}
