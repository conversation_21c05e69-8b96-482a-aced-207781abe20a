package com.espc.sec.dataimport.minio.controller;


import com.espc.sec.dataimport.minio.helper.MinIOServiceHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/minio")
public class MinioTestController {
    /**
     * 手动上传hdfs文件
     */
    @GetMapping("/upload")
    public void test(String bucketName, String objectName, String localFile) throws Exception {
        MinIOServiceHelper.uploadFile(bucketName, objectName, new File(localFile));
    }

}