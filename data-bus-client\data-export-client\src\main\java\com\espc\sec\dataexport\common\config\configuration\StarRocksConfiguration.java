package com.espc.sec.dataexport.common.config.configuration;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.config.Environment;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * 自定义数据源配置
 */
@Slf4j
@Configuration
public class StarRocksConfiguration {

    @Bean
    @Primary
    public DataSource dataSource() {

        Environment.StarRocksDTO dsConfig = Config.environment.starRocks;

        HikariConfig hikariConfig = new HikariConfig();

        hikariConfig.setJdbcUrl(dsConfig.getUrl());
        hikariConfig.setUsername(dsConfig.getUsername());
        hikariConfig.setPassword(dsConfig.getPassword());

        hikariConfig.setMaximumPoolSize(10);
        hikariConfig.setMinimumIdle(2);
        hikariConfig.setConnectionTimeout(30000);
        hikariConfig.setIdleTimeout(600000);
        hikariConfig.setMaxLifetime(1800000);

        hikariConfig.setPoolName("starRocksHikariCP");

        hikariConfig.setConnectionTestQuery("SELECT 1");

        return new HikariDataSource(hikariConfig);
    }

    /**
     * 创建JdbcTemplate Bean
     */
    @Bean
    @Primary
    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}