package com.espc.sec.dataexport.job.task;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.dto.task.BaseTaskProperties;
import com.espc.sec.dataexport.common.dto.task.TaskProperties;
import com.espc.sec.dataexport.common.enums.JobTypeEnum;
import com.espc.sec.dataexport.common.enums.TaskStatusEnum;
import com.espc.sec.dataexport.common.util.JsonUtil;
import com.espc.sec.dataexport.common.util.ObjectUtil;
import com.espc.sec.dataexport.common.util.SpringBeanUtil;
import com.espc.sec.dataexport.job.service.JobService;
import com.espc.sec.dataexport.netty.utils.TaskManagerUtil;
import com.espc.sec.dataexport.task.service.DataTaskService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import static com.espc.sec.dataexport.common.constant.LogKeyword.QUARTZ_DATA_TASK;

@Slf4j
public abstract class AbstractTask<T extends BaseTaskProperties> implements Job {
    @Override
    public void execute(JobExecutionContext context) {
        if (!Config.commonConfig.getQuartzTaskSwitch()) {
            log.debug(QUARTZ_DATA_TASK + "定时任务开关关闭");
            return;
        }
        Integer jobId = Integer.parseInt(context.getJobDetail().getKey().getName());
        JobTypeEnum jobTypeEnum = (JobTypeEnum) context.getJobDetail().getJobDataMap().get(Constants.Quartz.EXPORT_TYPE_TASK_PARAMS);
        Object taskParams = context.getJobDetail().getJobDataMap().get(Constants.Quartz.TASK_PARAMS);
        log.info(QUARTZ_DATA_TASK + "{} 执行开始,配置信息:{}", jobTypeEnum.name(), JsonUtil.objectToJson(taskParams));
        JobService jobService = SpringBeanUtil.getBean(JobService.class);
        DataTaskService taskService = SpringBeanUtil.getBean(DataTaskService.class);
        try {
            T taskParamT = ObjectUtil.converObjectToT(taskParams);
            taskParamT.setTaskId(jobId);
            taskService.updateStatus(jobId, TaskStatusEnum.EXECUTING);
            // 通知服务端任务开始执行
            doExecute(taskParamT);
            jobService.deleteJob(jobId);
            taskService.updateStatus(jobId, TaskStatusEnum.SUCCESS);
            // 通知服务端任务执行成功
            TaskManagerUtil.taskStatus(jobId, TaskStatusEnum.SUCCESS);
        } catch (Exception e) {
            log.error(QUARTZ_DATA_TASK + "{} 执行异常", jobTypeEnum.name(), e);
            jobService.deleteJob(jobId);
            taskService.updateStatus(jobId, TaskStatusEnum.FAILED);
            // 调用netty消息通知服务端任务失败
            TaskManagerUtil.taskStatus(jobId, TaskStatusEnum.FAILED);
        }
        log.info(QUARTZ_DATA_TASK + "{} 执行结束", jobTypeEnum.name());
    }


    protected abstract void doExecute(T taskProperties) throws Exception;
}
