package com.espc.sec.dataexport.common.dto.task;

import lombok.Data;

import java.util.List;

/**
 * TiDB导出请求参数
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Data
public class TidbTaskProperties extends BaseTaskProperties {

    private List<DatabaseConfig> database;

    @Data
    public static class DatabaseConfig {
        private String databaseName;
        private List<String> tableName;
        private String timeField;
        private String dataStartTime;
        private String dataEndTime;
    }
}