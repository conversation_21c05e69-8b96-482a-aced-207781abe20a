{"elasticsearch": {"indexClusterName": "my_es_cluster", "indexClusterServer": "**************:9200", "indexClusterServerHttp": "**************:9200", "indexUserName": "", "indexPassword": ""}, "starRocks": {"url": "********************************************************************************************************************************************", "username": "root", "password": ""}, "tidb": {"url": "********************************************************************************************************************************************", "username": "root", "password": ""}, "mysql": {"url": "***********************************************************************************************************************************", "username": "root", "password": "root"}, "kafka": {"bootstrapServers": "**************:9092", "username": "", "password": ""}, "minio": {"endPoint": "http://**************:9000", "username": "admin", "password": "password"}, "hdfs": {"hdfsUri": "hdfs://nnCluster"}}