package com.espc.sec.dataexport.metadata.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Elasticsearch索引信息VO
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexInfoVo {
    /**
     * 索引名称
     */
    private String indexName;
    
    /**
     * 文档数量
     */
    private Long docCount;
    
    /**
     * 存储大小
     */
    private String storeSize;
    
    /**
     * 状态
     */
    private String status;
}