package com.espc.sec.dataimport.monitor.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 监控任务数据实体类
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
@TableName("monitor_task_data")
public class MonitorTaskDataPo {

    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 节点
     */
    private String node;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 数据库类型
     */
    private String databaseType;

    /**
     * 表名称
     */
    private String tableName;

    /**
     * 导出或者导入条数
     */
    private Long size;

    /**
     * 导出或者导入时间
     */
    private LocalDateTime time;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 文件名
     */
    private String fileName;
}