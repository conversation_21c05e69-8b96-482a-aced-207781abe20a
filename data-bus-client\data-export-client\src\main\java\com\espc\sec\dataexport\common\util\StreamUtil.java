package com.espc.sec.dataexport.common.util;

import cn.hutool.core.collection.CollectionUtil;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2025/8/1
 */
public class StreamUtil {

    public static <T> Stream<T> getStream(Collection<T> collection) {
        if (CollectionUtil.isEmpty(collection)) {
            return Stream.empty();
        }
        return collection.stream();
    }

    @SafeVarargs
    public static <T> Stream<T> getStream(T... values) {
        if (values == null || values.length == 0) {
            return Stream.empty();
        }
        return Stream.of(values);
    }

    public static <T, R> List<R> map(Collection<T> collection, Function<? super T, ? extends R> mapper) {
        return getStream(collection).map(mapper).collect(Collectors.toList());
    }

    public static <T> List<T> filter(Collection<T> collection, Predicate<T> predicate) {
        return getStream(collection).filter(predicate).collect(Collectors.toList());
    }

    public static <T, K, U> Map<K, U> toMap(Collection<T> collection, Function<? super T, ? extends K> keyMapper,
                                            Function<? super T, ? extends U> valueMapper) {
        return getStream(collection).collect(Collectors.toMap(keyMapper, valueMapper, (e1, e2) -> e2));
    }

    public static <T, K> Map<K, List<T>> groupBy(Collection<T> collection, Function<? super T, ? extends K> classifier) {
        return getStream(collection).collect(Collectors.groupingBy(classifier));
    }

}
