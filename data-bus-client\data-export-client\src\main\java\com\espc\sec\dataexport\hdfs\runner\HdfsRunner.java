package com.espc.sec.dataexport.hdfs.runner;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.hdfs.helper.HdfsInotifyService;
import com.espc.sec.dataexport.hdfs.helper.HdfsServiceHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executors;

/**
 * 启动添加topic监听
 */
@Component
@Slf4j
public class HdfsRunner {

    @PostConstruct
    public void run() throws Exception {
        log.info(LogKeyword.HDFS_EXPORT + "hdfs文件监听开始");
        HdfsServiceHelper.init(Config.environment.hdfs.getHdfsUri());
        Executors.newFixedThreadPool(1).execute(HdfsInotifyService::watchLoop);
        log.info(LogKeyword.HDFS_EXPORT + "hdfs文件监听成功");
    }
}
