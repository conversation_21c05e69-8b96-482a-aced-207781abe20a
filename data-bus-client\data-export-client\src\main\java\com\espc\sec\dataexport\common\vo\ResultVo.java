package com.espc.sec.dataexport.common.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ResultVo<T> {
    private Integer code;
    private String message;
    private T data;
    private Long timestamp;

    public static <T> ResultVo<T> success(T data) {
        return ResultVo.<T>builder()
                .code(200)
                .message("success")
                .data(data)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    public static <T> ResultVo<T> fail(Integer code, String message) {
        return ResultVo.<T>builder()
                .code(code)
                .message(message)
                .timestamp(System.currentTimeMillis())
                .build();
    }
}
