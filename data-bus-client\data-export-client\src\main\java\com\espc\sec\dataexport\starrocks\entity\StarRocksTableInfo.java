package com.espc.sec.dataexport.starrocks.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/29
 */
@Data
public class StarRocksTableInfo {
    private String databaseName;
    private String tableName;
    private String timeField;
    // 时间范围
    private String startTime;     // 开始时间（格式：yyyy-MM-dd HH:mm:ss）
    private String endTime;       // 结束时间（格式：yyyy-MM-dd HH:mm:ss）

    /**
     * 获取表的唯一标识
     */
    public String getTableKey() {
        return databaseName + "." + tableName;
    }
}
