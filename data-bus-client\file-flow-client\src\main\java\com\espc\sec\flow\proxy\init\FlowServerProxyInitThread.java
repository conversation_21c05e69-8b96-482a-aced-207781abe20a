package com.espc.sec.flow.proxy.init;

import com.espc.sec.flow.FileFlowClientMain;
import com.espc.sec.flow.conf.Config;
import com.espc.sec.flow.proxy.FlowServerProxy;
import com.espc.sec.flow.util.IceUtil;
import com.ice.common.CommonServicePrx;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2019-04-23
 */
@Data
@Slf4j
public class FlowServerProxyInitThread implements Runnable {

    private Config.FlowServersBean config;
    private int i;

    public FlowServerProxyInitThread(Config.FlowServersBean config, int i) {
        this.config = config;
        this.i = i;
    }

    @Override
    public void run() {
        CommonServicePrx commonServicePrx = IceUtil.getFlowServerProxy(config.getIp(), config.getPort());
        while (commonServicePrx == null) {
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
            }
            commonServicePrx = IceUtil.getFlowServerProxy(config.getIp(), config.getPort());
        }
        FlowServerProxy server = new FlowServerProxy(config, commonServicePrx);
        log.info("get FlowServerProxy success. ip:" + config.getIp() + ", port:" + config.getPort());
        FileFlowClientMain.proxyMap.put(i, server);
    }
}
