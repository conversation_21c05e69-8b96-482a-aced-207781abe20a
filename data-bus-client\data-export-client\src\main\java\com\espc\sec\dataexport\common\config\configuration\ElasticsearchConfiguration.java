package com.espc.sec.dataexport.common.config.configuration;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.config.Environment;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Elasticsearch配置类
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
@Configuration
public class ElasticsearchConfiguration {

    @Bean
    public RestHighLevelClient elasticsearchClient() {
        try {
            if (Config.environment == null || Config.environment.elasticsearch == null) {
                log.warn("Elasticsearch配置未找到");
                return null;
            }

            Environment.ElasticsearchDTO esConfig = Config.environment.elasticsearch;
            String serverHttp = esConfig.getIndexClusterServerHttp();
            String userName = esConfig.getIndexUserName();
            String password = esConfig.getIndexPassword();

            if (serverHttp == null || serverHttp.trim().isEmpty()) {
                log.warn("Elasticsearch服务器地址未配置");
                return null;
            }

            // 解析服务器地址和端口
            String[] address = serverHttp.split(":");
            if (address.length != 2) {
                log.error("Elasticsearch服务器地址格式错误: {}", serverHttp);
                return null;
            }

            String host = address[0];
            int port = Integer.parseInt(address[1]);

            log.info("正在连接Elasticsearch: {}:{}", host, port);

            // 创建认证提供者
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            if (userName != null && !userName.trim().isEmpty() && 
                password != null && !password.trim().isEmpty()) {
                credentialsProvider.setCredentials(AuthScope.ANY, 
                    new UsernamePasswordCredentials(userName, password));
            }

            // 创建RestClient构建器
            RestClientBuilder restClientBuilder = RestClient.builder(new HttpHost(host, port, "http"));
            
            // 如果有认证信息，设置认证
            if (userName != null && !userName.trim().isEmpty()) {
                restClientBuilder.setHttpClientConfigCallback(httpAsyncClientBuilder -> 
                    httpAsyncClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
            }

            return new RestHighLevelClient(restClientBuilder);

        } catch (Exception e) {
            log.error("创建Elasticsearch客户端失败", e);
            return null;
        }
    }
}