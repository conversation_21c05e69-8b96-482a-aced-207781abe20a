package com.espc.sec.dataimport.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TaskStatusEnum {
    /**
     * 任务状态
     */
    PENDING(1, "待执行"),
    EXECUTING(2, "执行中"),
    SUCCESS(3, "执行成功"),
    FAILED(4, "执行失败"),
    ;

    private final Integer code;
    private final String name;

    public static TaskStatusEnum getTaskStatusEnum(int code) {
        TaskStatusEnum[] values = TaskStatusEnum.values();
        for (TaskStatusEnum value : values) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
