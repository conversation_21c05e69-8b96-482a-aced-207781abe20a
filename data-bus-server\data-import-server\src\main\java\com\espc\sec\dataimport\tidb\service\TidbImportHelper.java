package com.espc.sec.dataimport.tidb.service;

import com.espc.sec.dataimport.tidb.mapper.TidbImportMapper;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * TiDB数据导入辅助类 - 使用MyBatis实现
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class TidbImportHelper {

    @Autowired
    @Qualifier("tidbImportSqlSessionTemplate")
    private SqlSessionTemplate tidbImportSqlSessionTemplate;
    
    private TidbImportMapper tidbImportMapper;
    
    @PostConstruct
    public void init() {
        // 手动获取TidbImportMapper，确保使用正确的SqlSessionTemplate
        this.tidbImportMapper = tidbImportSqlSessionTemplate.getMapper(TidbImportMapper.class);
        log.info("TidbImportHelper初始化完成，使用TiDB Import SqlSessionTemplate");
        
        // 验证连接
        try {
            Map<String, Object> dbInfo = tidbImportMapper.getCurrentDatabase();
            log.info("TidbImportHelper连接验证成功，当前数据库: {}", dbInfo);
        } catch (Exception e) {
            log.error("TidbImportHelper连接验证失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 测试数据库连接
     */
    public void testConnection() {
        try {
            // 测试1：查询当前数据库
            Map<String, Object> dbInfo = tidbImportMapper.getCurrentDatabase();
            log.info("当前连接的数据库: {}", dbInfo);
            
            // 测试2：查询表数量
            Map<String, Object> tableCount = tidbImportMapper.getTableCount("probe-center");
            log.info("probe-center数据库表数量: {}", tableCount);
            
            // 测试3：设置自动提交
            tidbImportMapper.setAutocommit(1);
            log.info("设置自动提交模式");
            
            // 测试4：切换数据库
            tidbImportMapper.useDatabase("probe-center");
            log.info("切换到probe-center数据库");
            
        } catch (Exception e) {
            log.error("数据库连接测试失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 执行INSERT SQL语句
     */
    public int executeInsert(String sql) {
        try {
            log.debug("执行INSERT SQL: {}", sql.length() > 200 ? sql.substring(0, 200) + "..." : sql);
            int affected = tidbImportMapper.executeDynamicInsert(sql);
            log.debug("INSERT执行结果: affected={}", affected);
            return affected;
        } catch (Exception e) {
            log.error("INSERT执行失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 执行REPLACE INTO SQL语句
     */
    public int executeReplace(String sql) {
        try {
            // 将INSERT INTO替换为REPLACE INTO
            String replaceSql = sql.replaceFirst("(?i)INSERT\\s+INTO", "REPLACE INTO");
            log.debug("执行REPLACE SQL: {}", replaceSql.length() > 200 ? replaceSql.substring(0, 200) + "..." : replaceSql);
            int affected = tidbImportMapper.executeReplace(replaceSql);
            log.debug("REPLACE执行结果: affected={}", affected);
            return affected;
        } catch (Exception e) {
            log.error("REPLACE执行失败: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 检查表是否存在
     */
    public boolean checkTableExists(String databaseName, String tableName) {
        try {
            Map<String, Object> result = tidbImportMapper.checkTableExists(databaseName, tableName);
            Object count = result.get("table_exists");
            boolean exists = count != null && ((Number) count).intValue() > 0;
            log.debug("表存在检查: {}.{} = {}", databaseName, tableName, exists);
            return exists;
        } catch (Exception e) {
            log.error("检查表是否存在失败: {}.{}", databaseName, tableName, e);
            return false;
        }
    }

    /**
     * 获取表的主键列
     */
    public List<String> getPrimaryKeyColumns(String databaseName, String tableName) {
        try {
            List<String> primaryKeys = tidbImportMapper.getPrimaryKeyColumns(databaseName, tableName);
            log.debug("表主键列: {}.{} = {}", databaseName, tableName, primaryKeys);
            return primaryKeys;
        } catch (Exception e) {
            log.error("获取表主键列失败: {}.{}", databaseName, tableName, e);
            return null;
        }
    }

    /**
     * 简��查询测试
     */
    public List<Map<String, Object>> simpleQuery(String tableName, int limit) {
        try {
            List<Map<String, Object>> result = tidbImportMapper.simpleQuery(tableName, limit);
            log.debug("简单查询测试: {} LIMIT {} = {} 条记录", tableName, limit, result.size());
            return result;
        } catch (Exception e) {
            log.error("简单查询测试失败: {}", tableName, e);
            return null;
        }
    }
}