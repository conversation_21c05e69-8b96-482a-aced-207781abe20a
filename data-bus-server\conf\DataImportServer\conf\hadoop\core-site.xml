<configuration>

    <property>
        <name>fs.azure.user.agent.prefix</name>
        <value>User-Agent: APN/1.0 Hortonworks/1.0 HDP/None</value>
    </property>

    <property>
        <name>fs.defaultFS</name>
        <value>hdfs://nnCluster</value>
        <final>true</final>
    </property>

    <property>
        <name>fs.gs.application.name.suffix</name>
        <value>(GPN:Hortonworks; version 1.0) HDP/None</value>
    </property>

    <property>
        <name>fs.gs.path.encoding</name>
        <value>uri-path</value>
    </property>

    <property>
        <name>fs.gs.working.dir</name>
        <value>/</value>
    </property>

    <property>
        <name>fs.s3a.fast.upload</name>
        <value>true</value>
    </property>

    <property>
        <name>fs.s3a.fast.upload.buffer</name>
        <value>disk</value>
    </property>

    <property>
        <name>fs.s3a.multipart.size</name>
        <value>67108864</value>
    </property>

    <property>
        <name>fs.s3a.user.agent.prefix</name>
        <value>User-Agent: APN/1.0 Hortonworks/1.0 HDP/None</value>
    </property>

    <property>
        <name>fs.trash.interval</name>
        <value>360</value>
    </property>

    <property>
        <name>ha.failover-controller.active-standby-elector.zk.op.retries</name>
        <value>120</value>
    </property>

    <property>
        <name>ha.zookeeper.quorum</name>
        <value>cie1:2181,cie2:2181,cie3:2181</value>
    </property>

    <property>
        <name>hadoop.http.authentication.simple.anonymous.allowed</name>
        <value>true</value>
    </property>

    <property>
        <name>hadoop.http.cross-origin.allowed-headers</name>
        <value>X-Requested-With,Content-Type,Accept,Origin,WWW-Authenticate,Accept-Encoding,Transfer-Encoding</value>
    </property>

    <property>
        <name>hadoop.http.cross-origin.allowed-methods</name>
        <value>GET,PUT,POST,OPTIONS,HEAD,DELETE</value>
    </property>

    <property>
        <name>hadoop.http.cross-origin.allowed-origins</name>
        <value>*</value>
    </property>

    <property>
        <name>hadoop.http.cross-origin.max-age</name>
        <value>1800</value>
    </property>

    <property>
        <name>hadoop.http.filter.initializers</name>
        <value>
            org.apache.hadoop.security.AuthenticationFilterInitializer,org.apache.hadoop.security.HttpCrossOriginFilterInitializer
        </value>
    </property>

    <property>
        <name>hadoop.proxyuser.hdfs.groups</name>
        <value>*</value>
    </property>

    <property>
        <name>hadoop.proxyuser.hdfs.hosts</name>
        <value>*</value>
    </property>

    <property>
        <name>hadoop.proxyuser.hive.groups</name>
        <value>*</value>
    </property>

    <property>
        <name>hadoop.proxyuser.hive.hosts</name>
        <value>cie2</value>
    </property>

    <property>
        <name>hadoop.proxyuser.root.groups</name>
        <value>*</value>
    </property>

    <property>
        <name>hadoop.proxyuser.root.hosts</name>
        <value>cie1</value>
    </property>

    <property>
        <name>hadoop.proxyuser.yarn.hosts</name>
        <value>cie1,cie2</value>
    </property>

    <property>
        <name>hadoop.security.auth_to_local</name>
        <value>DEFAULT</value>
    </property>

    <property>
        <name>hadoop.security.authentication</name>
        <value>simple</value>
    </property>

    <property>
        <name>hadoop.security.authorization</name>
        <value>false</value>
    </property>

    <property>
        <name>hadoop.security.instrumentation.requires.admin</name>
        <value>false</value>
    </property>

    <property>
        <name>io.compression.codecs</name>
        <value>
            org.apache.hadoop.io.compress.GzipCodec,org.apache.hadoop.io.compress.DefaultCodec,org.apache.hadoop.io.compress.SnappyCodec
        </value>
    </property>

    <property>
        <name>io.file.buffer.size</name>
        <value>131072</value>
    </property>

    <property>
        <name>io.serializations</name>
        <value>org.apache.hadoop.io.serializer.WritableSerialization</value>
    </property>

    <property>
        <name>ipc.client.connect.max.retries</name>
        <value>50</value>
    </property>

    <property>
        <name>ipc.client.connection.maxidletime</name>
        <value>30000</value>
    </property>

    <property>
        <name>ipc.client.idlethreshold</name>
        <value>8000</value>
    </property>

    <property>
        <name>ipc.server.tcpnodelay</name>
        <value>true</value>
    </property>

    <property>
        <name>mapreduce.jobtracker.webinterface.trusted</name>
        <value>false</value>
    </property>

    <property>
        <name>net.topology.script.file.name</name>
        <value>/etc/hadoop/conf/topology_script.py</value>
    </property>

</configuration>