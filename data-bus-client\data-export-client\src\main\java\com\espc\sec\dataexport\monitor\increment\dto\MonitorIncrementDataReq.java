package com.espc.sec.dataexport.monitor.increment.dto;

import com.espc.sec.dataexport.common.dto.PageDto;
import lombok.Data;

import java.util.Date;

/**
 * 查询导入导出监控
 */
@Data
public class MonitorIncrementDataReq extends PageDto {

    /**
     * 节点
     */
    private String node;
    /**
     * 数据库类型 如hdfs minio es kafka starrocks
     */
    private String databaseType;
    /**
     * 表类型 对应操作的文件或者数据表名字
     */
    private String tableName;
    /**
     * 文件名字
     */
    private String fileName;

    /**
     * 导入导出的开始时间
     */
    private String startTime;
    /**
     * 导入导出的结束时间
     */
    private String endTime;


}
