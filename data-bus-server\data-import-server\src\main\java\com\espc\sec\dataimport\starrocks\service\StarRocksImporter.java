package com.espc.sec.dataimport.starrocks.service;

import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.constant.Constants;
import com.espc.sec.dataimport.common.service.AbstractImporter;
import com.espc.sec.dataimport.common.util.FileNameParseUtil;
import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataimport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataimport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataimport.task.service.DataTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Service
@Slf4j
public class StarRocksImporter extends AbstractImporter {

    //解析sql字符串
    private static Pattern CRON_EXPRESSION = Pattern.compile(
            "(?i)\\bINSERT\\s+INTO\\s+(\\w+)\\s*\\(([^)]+)\\)\\s+VALUES\\s*\\(([^)]+)\\)",
            Pattern.CASE_INSENSITIVE
    );

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private MonitorTaskDataService monitorTaskDataService;

    @Autowired
    private MonitorIncrementDataService monitorIncrementDataService;

    @Autowired
    private DataTaskService dataTaskService;

    @Override
    protected void doImport(File file) throws Exception {
        String sqlContent = FileUtils.readFileToString(file, StandardCharsets.UTF_8);

        // 分割SQL语句（简单的分割，按分号分割）
        String[] sqlStatements = sqlContent.split(";");

        int insertedCount = 0;
        for (String sql : sqlStatements) {
            sql = sql.trim();
            if (sql.isEmpty() || sql.startsWith("--") || sql.startsWith("/*")) {
                continue;
            }
            if (sql.toUpperCase().startsWith("INSERT")) {
                sql = addNodeMessage(file.getName(), sql);
                int affected = jdbcTemplate.update(sql);
                insertedCount += affected;
            } else {
                // 执行非INSERT语句（如USE, SET, BEGIN, COMMIT等）
                jdbcTemplate.execute(sql);
            }
        }

        saveImportLogToMysql(file.getName(), insertedCount);
    }

    /**
     * 保存导入日志
     *
     * @param fileName      文件名
     * @param insertedCount 导入条数
     */
    private void saveImportLogToMysql(String fileName, int insertedCount) {
        try {
            // 解析文件名获取参数
            FileNameParseUtil.FileNameInfo fileInfo = FileNameParseUtil.parseStarRocksFileName(fileName);

            if (fileInfo.getExportMode() == 1) {
                MonitorIncrementDataDto dto = new MonitorIncrementDataDto();
                dto.setNode(fileInfo.getNodeName());
                dto.setDatabaseType(fileInfo.getDatabaseType());
                dto.setTableName(fileInfo.getTableName());
                dto.setFileName(fileName);
                dto.setSize((long)insertedCount);
                dto.setTime(DateUtil.parse(fileInfo.getTimestamp(), "yyyyMMddHHmmssSSS"));
                monitorIncrementDataService.create(dto);
            } else {
                String taskName = "import_task";
                if (fileInfo.getTaskId() != null && fileInfo.getTaskId() != 0) {
                    taskName = dataTaskService.getTaskNameByIdFromMemoryCache(fileInfo.getTaskId());
                }

                // 构建监控任务数据请求
                MonitorTaskDataAddReq req = new MonitorTaskDataAddReq();
                req.setNode(fileInfo.getNodeName());
                req.setTaskName(taskName);
                req.setDatabaseType(fileInfo.getDatabaseType());
                req.setTableName(fileInfo.getTableName());
                req.setSize((long) insertedCount);
                req.setTime(fileInfo.getFormattedTime());
                req.setFileName(fileName);

                // 调用监控服务记录导入日志
                monitorTaskDataService.add(req);
            }
            log.info("StarRocks导入日志记录成功: 文件={}, 导入条数={}", fileName, insertedCount);
        } catch (Exception e) {
            log.error("StarRocks导入日志记录失败: 文件={}, 导入条数={}", fileName, insertedCount, e);
        }
    }

    /**
     * fileName starrocks分库分表文件名格式：20220920164916873_shanghai_starrocks_probe-center_probe-info.sql
     */
    @Override
    protected boolean validateFileName(String fileName) {
        String lowerFileName = fileName.toLowerCase();
        // 支持 .sql 文件（StarRocks导出的SQL文件）和 .log 文件（兼容性）
        return lowerFileName.endsWith(".sql") || lowerFileName.endsWith(".log");
    }

    /**
     * 替换表名和增加节点字段
     * @param fileName node
     * @param sql sql
     */
    private String addNodeMessage(String fileName, String sql) {
        if (!Config.importConfig.starRocks.getDataNodeEnable() && !Config.importConfig.starRocks.getDataNodeTableEnable()) {
            return sql;
        }
        Matcher matcher = CRON_EXPRESSION.matcher(sql);
        if (!matcher.find()) {
            return sql;
        }

        try {
            String node = fileName.split("_")[3];
            // 提取原字段列表和值列表
            String originalTable = matcher.group(1);
            String columns = matcher.group(2).trim();
            String values = matcher.group(3).trim();
            // 构建新SQL - 保留原始大小写格式
            String insertPrefix = sql.substring(0, sql.indexOf(originalTable));

            String newTable = originalTable;
            //修改表信息
            if (Config.importConfig.starRocks.getDataNodeTableEnable()) {
                newTable = node + "_" + originalTable;
            }
            String newColumn = Constants.DATA_NODE;
            String newValue = node;
            //修改数据信息
            if (Config.importConfig.starRocks.getDataNodeEnable()) {
                return insertPrefix + newTable + " (" + columns + ", " + newColumn + ") VALUES (" + values + ", '" + newValue + "')";
            }
            return insertPrefix + newTable + " (" + columns +  ") VALUES (" + values +")";
        } catch (Exception e) {
            log.info("解析starrocks失败：{}", e);

        }
        return sql;
    }
}
