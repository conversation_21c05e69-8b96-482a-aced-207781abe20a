package com.espc.sec.dataexport.common.util;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 文件名工具类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public class FileNameUtil {

    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");

    /**
     * 格式化时间戳
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的时间字符串
     */
    private static String formatTimestamp(long timestamp) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
        return dateTime.format(TIMESTAMP_FORMATTER);
    }

    public static String generateIncrementalFileNameForEs(long startTime, String nodeName, String elasticsearch, String indexName, String s) {
        String startTimeStr = formatTimestamp(startTime);
        return String.format("%s_%s_%s_%s%s",
                startTimeStr, nodeName, elasticsearch, indexName, s);
    }
}