package com.espc.sec.dataimport.common.controller;

import cn.hutool.core.lang.ClassScanner;
import com.espc.sec.dataimport.common.dto.Result;
import com.espc.sec.dataimport.common.enums.ErrorCodeEnum;
import com.espc.sec.dataimport.common.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/enum")
@Api(tags = "枚举管理")
public class EnumController {

    @GetMapping("/{enumClassName}")
    @ApiOperation("获取枚举属性")
    public Result<List<Map<String, Object>>> get(@PathVariable String enumClassName) {
        try {
            Class<?> enumClass = Class.forName(enumClassName);

            if (!enumClass.isEnum()) {
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "枚举不存在");
            }

            Object[] enumConstants = enumClass.getEnumConstants();
            List<Map<String, Object>> result = new ArrayList<>();

            for (Object enumConstant : enumConstants) {
                Map<String, Object> propertyMap = new LinkedHashMap<>();
                // 1. 首先添加枚举名称
                propertyMap.put("enumName", ((Enum<?>) enumConstant).name());
                Field[] fields = enumClass.getDeclaredFields();
                for (Field field : fields) {
                    // 跳过枚举自带的$VALUES和ENUM$字段，以及静态字段
                    if (field.isSynthetic() || java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
                        continue;
                    }
                    try {
                        field.setAccessible(true);
                        Object value = field.get(enumConstant);
                        propertyMap.put(field.getName(), value);
                    } catch (IllegalAccessException e) {
                        // 如果无法访问字段，尝试通过getter方法获取
                        String getterName = "get" + capitalize(field.getName());
                        try {
                            Method getter = enumClass.getMethod(getterName);
                            Object value = getter.invoke(enumConstant);
                            propertyMap.put(field.getName(), value);
                        } catch (Exception ex) {
                            // 如果getter也不存在，跳过该字段
                        }
                    }
                }

                // 3. 补充通过getter方法定义的属性（可能没有对应的字段）
                Method[] methods = enumClass.getDeclaredMethods();
                for (Method method : methods) {
                    if (method.getParameterCount() == 0 && method.getName().startsWith("get")
                            && !method.getName().equals("getClass") && !method.isSynthetic()) {
                        String propertyName = uncapitalize(method.getName().substring(3));
                        if (!propertyMap.containsKey(propertyName)) {
                            try {
                                Object value = method.invoke(enumConstant);
                                propertyMap.put(propertyName, value);
                            } catch (Exception e) {
                                // 忽略调用失败的方法
                            }
                        }
                    }
                }

                result.add(propertyMap);
            }

            return Result.success(result);

        } catch (ClassNotFoundException e) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "枚举不存在");
        } catch (Exception e) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "获取枚举属性失败");
        }
    }

    @ApiOperation("获取包下所有枚举类")
    @GetMapping("/getEnumNames")
    public List<String> scanPackageEnums() {
        Set<Class<?>> classes = ClassScanner.scanPackage("com.espc.sec.dataimport");
        return classes.stream()
                .filter(Class::isEnum)
                .map(Class::getName)
                .collect(Collectors.toList());
    }

    private String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    private String uncapitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toLowerCase() + str.substring(1);
    }
}
