package com.espc.sec.dataexport.metadata.dto;

import com.espc.sec.dataexport.common.dto.PageDto;
import lombok.Data;

/**
 * 元数据分页查询请求
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
public class MetadataPageReq extends PageDto {
    /**
     * 数据库名称（用于查询表列表）
     */
    private String databaseName;
    
    /**
     * 表名称（用于查询字段列表）
     */
    private String tableName;
    
    /**
     * 索引名称（用于查询ES字段列表）
     */
    private String indexName;
    
    /**
     * 搜索关键字（可选，用于模糊查询）
     */
    private String keyword;
}