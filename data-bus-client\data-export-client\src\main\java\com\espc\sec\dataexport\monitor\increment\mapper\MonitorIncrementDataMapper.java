package com.espc.sec.dataexport.monitor.increment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.espc.sec.dataexport.monitor.increment.dto.MonitorIncrementDataReq;
import com.espc.sec.dataexport.monitor.increment.entity.MonitorIncrementDataPo;
import com.espc.sec.dataexport.monitor.increment.vo.MonitorIncrementDataGroupVo;

import java.util.List;

/**
 * 操作增量导入导出mapper
 */
/**
 * @Author: zh
 * @date: 2025/7/25
 */
public interface MonitorIncrementDataMapper extends BaseMapper<MonitorIncrementDataPo> {
    List<MonitorIncrementDataPo> selectByCondition(MonitorIncrementDataReq req);

    List<MonitorIncrementDataGroupVo> groupQuery(MonitorIncrementDataReq dto);
}
