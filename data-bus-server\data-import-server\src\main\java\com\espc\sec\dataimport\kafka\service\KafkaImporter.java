package com.espc.sec.dataimport.kafka.service;

import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.constant.Constants;
import com.espc.sec.dataimport.common.service.AbstractImporter;
import com.espc.sec.dataimport.common.util.FileNameParseUtil;
import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataimport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataimport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataimport.task.service.DataTaskService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * Kafka导入服务主类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class KafkaImporter extends AbstractImporter {
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private KafkaProducer<String, String> producer;

    @Autowired
    private MonitorTaskDataService monitorTaskDataService;

    @Autowired
    private MonitorIncrementDataService monitorIncrementDataService;

    @Autowired
    private DataTaskService dataTaskService;


    @Override
    protected void doImport(File file) throws Exception {
        FileNameParseUtil.FileNameInfo fileInfo = FileNameParseUtil.parseKafkaFileName(file.getName());
        //topic区分节点信息
        String topic = getNameByNodeConfig(fileInfo);
        List<String> messages = FileUtils.readLines(file, StandardCharsets.UTF_8);
        sendMessages(topic, messages, fileInfo.getNodeName());

        saveImportLogToMysql(file.getName(), messages.size());
    }

    /**
     * 保存导入日志
     *
     * @param fileName     文件名
     * @param messagesSize 导入条数
     */
    private void saveImportLogToMysql(String fileName, int messagesSize) {
        try {
            // 解析文件名获取参数
            FileNameParseUtil.FileNameInfo fileInfo = FileNameParseUtil.parseKafkaFileName(fileName);

            if (fileInfo.getExportMode() == 1) {
                MonitorIncrementDataDto dto = new MonitorIncrementDataDto();
                dto.setNode(fileInfo.getNodeName());
                dto.setDatabaseType(fileInfo.getDatabaseType());
                dto.setTableName(fileInfo.getTableName());
                dto.setFileName(fileName);
                dto.setSize((long) messagesSize);
                dto.setTime(DateUtil.parse(fileInfo.getTimestamp(), "yyyyMMddHHmmssSSS"));
                monitorIncrementDataService.create(dto);
            } else {
                String taskName = "import_task";
                if (fileInfo.getTaskId() != null && fileInfo.getTaskId() != 0) {
                    taskName = dataTaskService.getTaskNameByIdFromMemoryCache(fileInfo.getTaskId());
                }

                // 构建监控任务数据请求
                MonitorTaskDataAddReq req = new MonitorTaskDataAddReq();
                req.setNode(fileInfo.getNodeName());
                req.setTaskName(taskName);
                req.setDatabaseType(fileInfo.getDatabaseType());
                req.setTableName(fileInfo.getTableName()); // Topic名
                req.setSize((long) messagesSize);
                req.setTime(fileInfo.getFormattedTime());
                req.setFileName(fileName);

                // 调用监控服务记录导入日志
                monitorTaskDataService.add(req);

            }
            log.info("Kafka导入日志记录成功: 文件={}, 导入条数={}", fileName, messagesSize);
        } catch (Exception e) {
            log.error("Kafka导入日志记录失败: 文件={}, 导入条数={}", fileName, messagesSize, e);
        }
    }

    private int sendMessages(String topic, List<String> messages, String node) {
        if (messages == null || messages.isEmpty()) {
            return 0;
        }
        int successCount = 0;
        for (String message : messages) {
            try {
                message = addNodeMessage(node, message);
                ProducerRecord<String, String> record = new ProducerRecord<>(topic, null, message);
                producer.send(record, (metadata, exception) -> {
                    if (exception != null) {
                        log.error("批量发送消息到Kafka失败: topic={}", topic, exception);
                    }
                });
                successCount++;
            } catch (Exception e) {
                log.error("批量发送消息异常: {}", message, e);
            }
        }

        // 确保所有消息都发送完成
        producer.flush();

        log.info("批量发送消息完成: 总数={}, 成功={}", messages.size(), successCount);
        return successCount;
    }

    /**
     * 数据信息增加节点内容
     * @param node 节点
     * @param message 数据信息
     * @return
     */
    @SneakyThrows
    private String addNodeMessage(String node, String message) {
        if (Config.importConfig.kafka.getDataNodeEnable()) {
            Map<String, Object> data = objectMapper.readValue(message, Map.class);
            data.put(Constants.DATA_NODE, node);
            return objectMapper.writeValueAsString(data);
        }
        return message;
    }
    /**
     * 根据配置获取topic名字，如果有节点配置需要增加节点信息
     * @param fileInfo 文件名字解析
     * @return
     */
    private String getNameByNodeConfig(FileNameParseUtil.FileNameInfo fileInfo) {
        if (Config.importConfig.kafka.getDataNodeTableEnable()) {
            return fileInfo.getNodeName() + "-" + fileInfo.getTableName();
        }
        return fileInfo.getTableName();
    }

    /**
     * @param fileName 1_1_20250724152501046_sichuan_kafka_test-pr-v3-flow-072411.log
     * @return
     */
    @Override
    protected boolean validateFileName(String fileName) {
        if (!fileName.endsWith(".log") || fileName.endsWith(".tmp")) {
            return false;
        }
        String[] parts = fileName.split("_");
        String databaseType = parts[4];
        return "kafka".equals(databaseType);
    }
}