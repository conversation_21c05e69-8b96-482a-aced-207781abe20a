package com.espc.sec.flow.util;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * Created by Administrator on 2016/3/8.
 */
@Slf4j
public class ThreadUtil {

    /**
     * 执行多个线程，并等待线程执行结束
     *
     * @param threads  线程列表
     * @param poolSize 最大线程数
     */
    public static void executeMultiThread(java.util.List<Runnable> threads, int poolSize) {
        if (threads.size() <= 0) {
            return;
        }
        ExecutorService executorService = createThreadPool(poolSize);

        for (int i = 0; i < threads.size(); i++) {
            executorService.submit(threads.get(i));
        }
        executorService.shutdown();//退出使用线程池
        try {
            executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.DAYS);
        } catch (InterruptedException e) {
            try {
                executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.DAYS);
            } catch (InterruptedException e1) {
            }
        }
    }

    /**
     * 执行多个线程，并等待线程执行结束
     *
     * @param threads 线程列表
     */
    public static void executeMultiThread(java.util.List<Runnable> threads) {
        if (threads.size() <= 0) {
            return;
        }
        ExecutorService executorService = createThreadPool(threads.size());

        for (int i = 0; i < threads.size(); i++) {
            executorService.submit(threads.get(i));
        }
        executorService.shutdown();//退出使用线程池
        try {
            executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.DAYS);
        } catch (InterruptedException e) {
            try {
                executorService.awaitTermination(Long.MAX_VALUE, TimeUnit.DAYS);
            } catch (InterruptedException e1) {
            }
        }
    }

    /**
     * 创建一个线程池
     *
     * @param size 线程池的大小
     * @return 返回一个线程池
     */
    public static ThreadPoolExecutor createThreadPool(int size) {
        ThreadFactory threadFactory = Executors.defaultThreadFactory();
        return new ThreadPoolExecutor(size, size, 0, TimeUnit.SECONDS,
                new LinkedBlockingQueue<Runnable>(), threadFactory, new ThreadPoolExecutor.AbortPolicy());
    }

}
