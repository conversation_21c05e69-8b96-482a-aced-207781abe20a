package com.espc.sec.dataimport.common.config;

import lombok.Data;

import java.util.Map;

public class ImportConfig {

    public ElasticsearchDTO elasticsearch;

    public StarRocksDTO starRocks;

    public TidbDTO tidb;

    public KafkaDTO kafka;

    public MinioDTO minio;

    public HdfsDTO hdfs;

    public Map<String, Integer> scanFileLimit;


    @Data
    public static class ElasticsearchDTO {
        private Integer fixedDelayMills;
        private Boolean dataNodeEnable;
        private Boolean dataNodeTableEnable;
    }


    @Data
    public static class StarRocksDTO {
        private Integer fixedDelayMills;
        private Boolean dataNodeEnable;
        private Boolean dataNodeTableEnable;
    }

    @Data
    public static class TidbDTO {
        private Integer fixedDelayMills;
        private Boolean dataNodeEnable;
        private Boolean dataNodeTableEnable;
    }

    @Data
    public static class KafkaDTO {
        private Integer fixedDelayMills;
        private Boolean dataNodeEnable;
        private Boolean dataNodeTableEnable;
    }


    @Data
    public static class MinioDTO {
        private Integer fixedDelayMills;
        private String uploadBucketPrefix;
        private Boolean dataNodeEnable;
        private Boolean dataNodeTableEnable;
    }


    @Data
    public static class HdfsDTO {
        private Integer fixedDelayMills;
        private String hdfsUploadPath;
        private Boolean dataNodeEnable;
    }
}
