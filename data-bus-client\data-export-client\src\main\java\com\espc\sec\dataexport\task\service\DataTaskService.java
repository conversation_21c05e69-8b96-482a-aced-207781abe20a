package com.espc.sec.dataexport.task.service;


import com.espc.sec.dataexport.common.enums.TaskStatusEnum;
import com.espc.sec.dataexport.common.vo.PageVo;
import com.espc.sec.dataexport.task.dto.DataTaskDto;
import com.espc.sec.dataexport.task.dto.DataTaskReq;
import com.espc.sec.dataexport.task.entity.DataTaskPo;
import com.espc.sec.dataexport.task.vo.DataTaskVo;

import java.util.List;

/**
 * 数据任务服务接口
 */
public interface DataTaskService {
    /**
     * 创建任务
     *
     * @param dataTaskDto 任务DTO
     * @return 任务ID
     */
    Integer createTask(DataTaskDto dataTaskDto);

    /**
     * 更新任务
     *
     * @param dataTaskDto 任务DTO
     * @return 是否成功
     */
    Boolean updateTask(DataTaskDto dataTaskDto);

    /**
     * 删除任务
     *
     * @param id 任务ID
     * @return 是否成功
     */
    Boolean deleteTask(Integer id);

    /**
     * 分页查询
     *
     * @param dataTaskReq
     * @return 任务VO列表
     */
    PageVo<DataTaskVo> pageTasks(DataTaskReq dataTaskReq);

    /**
     * 根据状态查询任务
     */
    List<DataTaskPo> listTasksByStatus(TaskStatusEnum statusEnum);

    /**
     * 更新任务状态
     */
    void updateStatus(Integer id, TaskStatusEnum statusEnum);

    /**
     * 根据任务id查询名称
     * @param id
     * @return
     */
    String getTaskNameByIdFromMemoryCache(Integer id);
}
