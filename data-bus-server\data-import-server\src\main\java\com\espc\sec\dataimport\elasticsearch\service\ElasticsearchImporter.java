package com.espc.sec.dataimport.elasticsearch.service;


import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.constant.Constants;
import com.espc.sec.dataimport.common.enums.ImportModeEnum;
import com.espc.sec.dataimport.common.service.AbstractImporter;
import com.espc.sec.dataimport.common.util.FileNameParseUtil;
import com.espc.sec.dataimport.elasticsearch.util.EsFileNameUtil;
import com.espc.sec.dataimport.elasticsearch.util.EsUtils;
import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataimport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataimport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataimport.task.service.DataTaskService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.elasticsearch.action.bulk.BulkProcessor;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Elasticsearch导入服务
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class ElasticsearchImporter extends AbstractImporter {

    private final ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    private BulkProcessor bulkProcessor;

    @Autowired
    private MonitorTaskDataService monitorTaskDataService;

    @Autowired
    private MonitorIncrementDataService monitorIncrementDataService;

    @Autowired
    private DataTaskService dataTaskService;


    @Override
    protected void doImport(File file) throws Exception {
        AtomicInteger importedCount = new AtomicInteger(0);
        EsFileNameUtil.FileNameInfo fileNameInfo = EsFileNameUtil.parseFileName(file.getName());

        String targetIndexName = getNameByNodeConfig(fileNameInfo);

        EsUtils.createIndexIfNotExists(targetIndexName);

        List<String> lines = FileUtils.readLines(file, StandardCharsets.UTF_8);

        for (String line : lines) {
            if (line.trim().isEmpty()) {
                continue;
            }
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> document = objectMapper.readValue(line, Map.class);
                addNodeMessage(document, fileNameInfo.getNodeName());

                IndexRequest indexRequest = new IndexRequest(targetIndexName)
                        .source(document, XContentType.JSON);

                bulkProcessor.add(indexRequest);
                importedCount.incrementAndGet();

            } catch (Exception e) {
                log.error("解析JSON行失败: {}", line, e);
            }
        }

        bulkProcessor.flush();

        saveImportLogToMysql(file.getName(), lines.size());
    }
    /**
     * 数据信息增加节点信息
     * @param document 数据信息
     * @param noeName 节点信息
     */
    private void addNodeMessage(Map<String, Object> document, String noeName) {
        if (Config.importConfig.elasticsearch.getDataNodeEnable()) {
            document.put(Constants.DATA_NODE, noeName);
        }
    }
    /**
     * 导入文件信息是否加入节点,根据配置判断
     * @param fileNameInfo 导入文件
     */
    private String getNameByNodeConfig(EsFileNameUtil.FileNameInfo fileNameInfo) {
        if (Config.importConfig.elasticsearch.getDataNodeTableEnable()) {
            return fileNameInfo.getNodeName() + "-" + fileNameInfo.getIndexName();
        }
        return fileNameInfo.getIndexName();
    }

    /**
     * 保存导入日志
     *
     * @param fileName  文件名
     * @param linesSize 导入条数
     */
    private void saveImportLogToMysql(String fileName, int linesSize) {
        try {
            // 解析文件名获取参数
            FileNameParseUtil.FileNameInfo fileInfo = FileNameParseUtil.parseElasticsearchFileName(fileName);

            if (ImportModeEnum.increment.equals(ImportModeEnum.getByCode(fileInfo.getExportMode()))) {
                MonitorIncrementDataDto dto = new MonitorIncrementDataDto();
                dto.setNode(fileInfo.getNodeName());
                dto.setDatabaseType(fileInfo.getDatabaseType());
                dto.setTableName(fileInfo.getTableName());
                dto.setFileName(fileName);
                dto.setSize((long) linesSize);
                dto.setTime(DateUtil.parse(fileInfo.getTimestamp(), "yyyyMMddHHmmssSSS"));
                monitorIncrementDataService.create(dto);
            } else {
                String taskName = "import_task";
                if (fileInfo.getTaskId() != null && fileInfo.getTaskId() != 0) {
                    taskName = dataTaskService.getTaskNameByIdFromMemoryCache(fileInfo.getTaskId());
                }
                // 构建监控任务数据请求
                MonitorTaskDataAddReq req = new MonitorTaskDataAddReq();
                req.setNode(fileInfo.getNodeName());
                req.setTaskName(taskName);
                req.setDatabaseType(fileInfo.getDatabaseType());
                req.setTableName(fileInfo.getTableName()); // 索引名
                req.setSize((long) linesSize);
                req.setTime(fileInfo.getFormattedTime());
                req.setFileName(fileName);
                // 调用监控服务记录导入日志
                monitorTaskDataService.add(req);
            }
            log.info("Elasticsearch导入日志记录成功: 文件={}, 导入条数={}", fileName, linesSize);
        } catch (Exception e) {
            log.error("Elasticsearch导入日志记录失败: 文件={}, 导入条数={}", fileName, linesSize, e);
        }
    }

    @Override
    protected boolean validateFileName(String fileName) {
//        if (!fileName.endsWith(".log") || fileName.startsWith(".")) {
//            return false;
//        }
//        if (!EsFileNameUtil.validate(fileName)) {
//            return false;
//        }
        return true;
    }
}