package com.espc.sec.dataexport.elasticsearch.util;

import com.espc.sec.dataexport.common.config.Config;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.action.bulk.BulkProcessor;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.ByteSizeUnit;
import org.elasticsearch.common.unit.ByteSizeValue;
import org.elasticsearch.common.unit.TimeValue;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Slf4j
public class EsClientHolder {

    public static RestHighLevelClient normalRestClient = null;
    public static BulkProcessor normalBulkProcessor = null;

    public static void init() {
        // 类似developer:elastic_developer
//        String[] normalTokens = Config.config.getElastic().get("xpack.token").toString().split(":");
        String indexUserName = Config.environment.elasticsearch.getIndexUserName();
        String indexPassword = Config.environment.elasticsearch.getIndexPassword();
        // 类似***************:9205
//        String normalHosts = Config.config.getEsHttpUrl().replace("http://", "");
//        initNormal(normalHosts, normalTokens[0], normalTokens[1]);
        initNormal(Config.environment.elasticsearch.getIndexClusterServerHttp(), indexUserName, indexPassword);

    }

    private static RestHighLevelClient getRestHighLevelClient(String host, int port, String userName, String password) {
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(userName, password));
        RestClientBuilder restClientBuilder = RestClient.builder(new HttpHost(host, port, "http"))
                .setHttpClientConfigCallback(httpAsyncClientBuilder -> httpAsyncClientBuilder.setDefaultCredentialsProvider(credentialsProvider));
        return new RestHighLevelClient(restClientBuilder);
    }

    private static BulkProcessor getBulkProcessor(RestHighLevelClient client) {
        BulkProcessor.Listener listener = new EsBulkListener(client);
        return BulkProcessor.builder(((bulkRequest, bulkResponseActionListener) -> {
            client.bulkAsync(bulkRequest, RequestOptions.DEFAULT, bulkResponseActionListener);
        }), listener).setBulkActions(**********)
                .setFlushInterval(new TimeValue(60, TimeUnit.SECONDS))
                .setConcurrentRequests(10)
                .setBulkSize(new ByteSizeValue(10, ByteSizeUnit.MB)).build();
    }

    private static void initNormal(String hosts, String userName, String password) {
        try {
            String[] address = hosts.split(":");
            normalRestClient = getRestHighLevelClient(address[0], Integer.parseInt(address[1]), userName, password);
            normalBulkProcessor = getBulkProcessor(normalRestClient);

            log.info("normal bulk processor init ok, start to add shutdown hook");
            Runtime.getRuntime().addShutdownHook(new ShutdownHookThread(normalRestClient, normalBulkProcessor));
        } catch (Throwable t) {
            log.error("error while init normal es client. host:" + hosts + ", user:" + userName + ", password:" + password, t);
            System.exit(-1);
        }
    }

    public static class EsBulkListener implements BulkProcessor.Listener {

        private RestHighLevelClient client;

        public EsBulkListener(RestHighLevelClient client) {
            this.client = client;
        }

        @Override
        public void beforeBulk(long l, BulkRequest bulkRequest) {
        }

        @Override
        public void afterBulk(long l, BulkRequest bulkRequest, BulkResponse bulkResponse) {
            if (bulkResponse.hasFailures()) {
                log.error("向Elasticsearch写入数据有错误: {}", bulkResponse.buildFailureMessage());

            }
        }

        @Override
        public void afterBulk(long l, BulkRequest bulkRequest, Throwable throwable) {
            log.error("向Elasticsearch写入数据有错误", throwable);

        }
    }

    public static class ShutdownHookThread extends Thread {

        private RestHighLevelClient client;
        private BulkProcessor bulkProcessor;

        public ShutdownHookThread(RestHighLevelClient client, BulkProcessor bulkProcessor) {
            this.client = client;
            this.bulkProcessor = bulkProcessor;
        }

        @Override
        public void run() {
            log.info("flush before exit");
            bulkProcessor.flush();
            log.info("close before exit");
            try {
                bulkProcessor.awaitClose(Long.MAX_VALUE, TimeUnit.DAYS);
            } catch (Exception e) {
                // skip
            }
            try {
                client.close();
            } catch (IOException e) {
            }
            log.info("close ok");
        }
    }

}