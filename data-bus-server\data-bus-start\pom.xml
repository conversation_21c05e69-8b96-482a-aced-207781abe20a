<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>data-bus-server</artifactId>
        <groupId>com.espc.sec</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>data-bus-start</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.espc.sec</groupId>
            <artifactId>file-flow-server</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.espc.sec</groupId>
            <artifactId>data-import-server</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.yml</include>
                </includes>
                <filtering>false</filtering>
                <targetPath>${basedir}/target/conf</targetPath>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>${basedir}/../conf</directory>
                <targetPath>${basedir}/target/conf</targetPath>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>${basedir}/deploy</directory>
                <targetPath>${basedir}/target/deploy</targetPath>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>${basedir}/libs</directory>
                <targetPath>${basedir}/target/java-libs</targetPath>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>${basedir}/../log</directory>
                        </fileset>
                        <fileset>
                            <directory>${basedir}/../conf/target</directory>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.2</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <!--编译的时候引入外部的jar包-->
                    <compilerArguments>
                        <extdirs>${basedir}/target/java-libs</extdirs>
                    </compilerArguments>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin.version}</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>${maven-dependency-plugin.version}</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includeTypes>jar</includeTypes>
                    <overWriteIfNewer>true</overWriteIfNewer>
                    <overWriteSnapshots>true</overWriteSnapshots>
                    <type>jar</type>
                    <outputDirectory>${basedir}/target/java-libs</outputDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>${maven-antrun-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <delete dir="${basedir}/target/maven-archiver" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/classes" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/test-classes" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/generated-sources" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/generated-test-sources" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/site" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/antrun" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/surefire-reports" includeemptydirs="true"/>
                                <exec executable="cmd" spawn="true">
                                    <arg value="/c"/>
                                    <arg value="deploy\getStartBat.bat"/>
                                    <arg value="${project.artifactId}-${project.version}.jar"/>
                                </exec>
                                <delete file="${basedir}/target/deploy/getStartBat.bat" includeemptydirs="true"/>
                                <delete file="${basedir}/target/jacoco.exec" includeemptydirs="true"/>
                                <move file="${basedir}/target/${project.artifactId}-${project.version}.jar"
                                      todir="${basedir}/target/libs"/>
                                <delete file="${basedir}/target/${project.artifactId}-${project.version}-sources.jar"
                                        includeemptydirs="true"/>
                                <delete dir="${basedir}/../conf/target" includeemptydirs="true"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.21.0</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>