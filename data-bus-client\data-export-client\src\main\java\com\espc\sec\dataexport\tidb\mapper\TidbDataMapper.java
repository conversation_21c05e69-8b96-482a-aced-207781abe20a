package com.espc.sec.dataexport.tidb.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * TiDB数据查询Mapper
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Mapper
public interface TidbDataMapper {

    /**
     * 动态查询表数据 - 使用XML映射
     *
     * @param tableName 表名
     * @param timeField 时间字段名
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param pageSize  页大小
     * @param offset    偏移量
     * @return 查询结果
     */
    List<Map<String, Object>> queryDataForTable(
            @Param("tableName") String tableName,
            @Param("timeField") String timeField,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("pageSize") int pageSize,
            @Param("offset") int offset
    );

    /**
     * 测试连接 - 查询当前数据库
     */
    @Select("SELECT DATABASE() as current_database")
    Map<String, Object> getCurrentDatabase();

    /**
     * 测试查询 - 查询表数量
     */
    @Select("SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = #{databaseName}")
    Map<String, Object> getTableCount(@Param("databaseName") String databaseName);

    /**
     * 简单查询测试 - 使用注解方式作为备选
     */
    @Select("SELECT * FROM ${tableName} LIMIT ${limit}")
    List<Map<String, Object>> simpleQuery(
            @Param("tableName") String tableName,
            @Param("limit") int limit
    );

    /**
     * 备选查询方法 - 使用注解方式，如果XML方式失败可以使用这个
     */
    @Select("SELECT * FROM ${tableName} WHERE ${timeField} >= #{startTime} AND ${timeField} < #{endTime} ORDER BY ${timeField} ASC LIMIT #{pageSize} OFFSET #{offset}")
    List<Map<String, Object>> queryDataForTableByAnnotation(
            @Param("tableName") String tableName,
            @Param("timeField") String timeField,
            @Param("startTime") String startTime,
            @Param("endTime") String endTime,
            @Param("pageSize") int pageSize,
            @Param("offset") int offset
    );
}