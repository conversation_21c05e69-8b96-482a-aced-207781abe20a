package com.espc.sec.dataexport.monitor.task.controller;

import com.espc.sec.dataexport.common.vo.ResultVo;
import com.espc.sec.dataexport.common.vo.PageVo;
import com.espc.sec.dataexport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataexport.monitor.task.dto.MonitorTaskDataBaseReq;
import com.espc.sec.dataexport.monitor.task.dto.MonitorTaskDataReq;
import com.espc.sec.dataexport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataexport.monitor.task.vo.MonitorTaskDataAggregateVo;
import com.espc.sec.dataexport.monitor.task.vo.MonitorTaskDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 监控任务数据控制器
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/api/monitor/task")
@Api(tags = "监控任务数据管理")
public class MonitorTaskDataController {

    @Resource
    private MonitorTaskDataService monitorTaskDataService;

    /**
     * 新增监控任务数据
     */
    @PostMapping("/add")
    @ApiOperation("新增监控任务数据")
    public ResultVo<Boolean> add(@RequestBody @Validated MonitorTaskDataAddReq req) {
        Boolean result = monitorTaskDataService.add(req);
        return ResultVo.success(result);
    }

    @PostMapping("/list")
    @ApiOperation("查询所有任务")
    public ResultVo<PageVo<MonitorTaskDataVo>> getPageList(@RequestBody MonitorTaskDataReq dataTaskReq) {
        PageVo<MonitorTaskDataVo> taskList = monitorTaskDataService.pageTasks(dataTaskReq);
        return ResultVo.success(taskList);
    }

    /**
     * 列表查询导出任务数据（不分页）
     */
    @PostMapping("/listAll")
    @ApiOperation("列表查询导出任务数据")
    public ResultVo<List<MonitorTaskDataVo>> getList(@RequestBody MonitorTaskDataBaseReq req) {
        List<MonitorTaskDataVo> result = monitorTaskDataService.getList(req);
        return ResultVo.success(result);
    }

    /**
     * 聚合查询导出任务数据
     * 根据node、task_name、database_type、table_name分组汇总size
     */
    @PostMapping("/aggregate")
    @ApiOperation("聚合查询导出任务数据")
    public ResultVo<List<MonitorTaskDataAggregateVo>> getAggregateList(@RequestBody MonitorTaskDataBaseReq req) {
        List<MonitorTaskDataAggregateVo> result = monitorTaskDataService.getAggregateList(req);
        return ResultVo.success(result);
    }
}