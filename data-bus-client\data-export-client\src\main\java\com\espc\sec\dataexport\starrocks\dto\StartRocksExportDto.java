package com.espc.sec.dataexport.starrocks.dto;

import com.espc.sec.dataexport.common.dto.ExportDto;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Author: zh
 * @date: 2025/7/29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StartRocksExportDto extends ExportDto {

    private List<String> filePath;

    private String file;

    private String databaseName;
    private String tableName;
    private String timeField;

    private Date startTime;

    private Date endTime;

    public StartRocksExportDto(String databaseName, String tableName, String timeField, Date startTime, Date endTime, ExportModeEnum exportModeEnum,Integer taskId) {
        this.databaseName = databaseName;
        this.tableName = tableName;
        this.timeField = timeField;
        this.startTime = startTime;
        this.endTime = endTime;
        this.setTaskId(taskId);
        this.setExportModeEnum(exportModeEnum);
        init();
    }

    private void init() {
        this.setExportTypeEnum(ExportTypeEnum.STAR_ROCKS);
    }

    public String getTableKey() {
        return this.databaseName + "." + this.tableName;
    }
}
