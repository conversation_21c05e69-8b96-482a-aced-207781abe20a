<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
        <relativePath/>
    </parent>

    <groupId>com.espc.sec</groupId>
    <artifactId>data-bus-server</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <!-- plugin version -->
        <maven-compiler-plugin.version>2.5.1</maven-compiler-plugin.version>
        <maven-dependency-plugin.version>2.8</maven-dependency-plugin.version>
        <maven-resources-plugin.version>2.6</maven-resources-plugin.version>
        <maven-antrun-plugin.version>1.8</maven-antrun-plugin.version>
        <okhttp3.version>4.11.0</okhttp3.version>
    </properties>

    <modules>
        <module>data-bus-start</module>
        <module>file-flow-server</module>
        <module>data-import-server</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.6.2</version>
        </dependency>
    </dependencies>

</project>