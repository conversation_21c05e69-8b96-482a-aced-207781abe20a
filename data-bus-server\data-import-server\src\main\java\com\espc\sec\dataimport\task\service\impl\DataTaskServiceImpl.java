package com.espc.sec.dataimport.task.service.impl;


import com.espc.sec.dataimport.common.constant.Constants;
import com.espc.sec.dataimport.common.enums.*;
import com.espc.sec.dataimport.common.exception.BusinessException;
import com.espc.sec.dataimport.common.util.DateUtil;
import com.espc.sec.dataimport.common.util.JsonUtil;
import com.espc.sec.dataimport.common.util.PageHelperUtil;
import com.espc.sec.dataimport.common.vo.PageVo;
import com.espc.sec.dataimport.netty.utils.TaskManagerUtil;
import com.espc.sec.dataimport.task.dto.DataTaskDto;
import com.espc.sec.dataimport.task.dto.DataTaskReq;
import com.espc.sec.dataimport.task.entity.DataTaskPo;
import com.espc.sec.dataimport.task.mapper.DataTaskMapper;
import com.espc.sec.dataimport.task.service.DataTaskService;
import com.espc.sec.dataimport.task.vo.DataTaskVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 数据任务服务实现
 */
@Slf4j
@Service
public class DataTaskServiceImpl implements DataTaskService {

    @Autowired
    private DataTaskMapper dataTaskMapper;
    private static final ConcurrentHashMap<Integer, String> taskIdAndTaskNameMap = new ConcurrentHashMap<>();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createTask(DataTaskDto dataTaskDto) {
        DataTaskPo taskByName = dataTaskMapper.selectByName(dataTaskDto.getName());
        if (taskByName != null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "任务名称不能重复");
        }
        DataTaskPo dataTaskPo = new DataTaskPo();
        BeanUtils.copyProperties(dataTaskDto, dataTaskPo);
        dataTaskPo.setBusinessConfig(JsonUtil.objectToJson(dataTaskDto.getBusinessConfig()));

        if (TaskTypeEnum.SCHEDULED.getCode().equals(dataTaskDto.getType())) {
            try {
                dataTaskPo.setCron(DateUtil.dateTimeToCron(dataTaskDto.getCron()));
            } catch (ParseException e) {
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "cron表达式错误,格式为yyyy-MM-dd HH:mm:ss");
            }
            paramCheck(dataTaskDto);
        }

        dataTaskPo.setStatus(TaskStatusEnum.PENDING.getCode());
        dataTaskPo.setIsDelete(YesOrNoEnum.NO.getCode());
        dataTaskPo.setCreateTime(new Date());
        dataTaskPo.setUpdateTime(new Date());

        dataTaskMapper.insert(dataTaskPo);
        dataTaskDto.setId(dataTaskPo.getId());
        //调用netty接口,参数为dataTaskDto
        TaskManagerUtil.taskManager(dataTaskDto.getNode(), OperateTypeEnum.CREATE, dataTaskDto);

        return dataTaskPo.getId();
    }

    private void paramCheck(DataTaskDto dataTaskDto) {
        boolean after1Minutes = DateUtil.parse(dataTaskDto.getCron(), Constants.SECONDS_FORMAT).isAfter(DateUtil.offsetMinute(new Date(), 1));
        if (!after1Minutes) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "定时任务执行时间必须大于服务器当前时间");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTask(DataTaskDto dataTaskDto) {
        DataTaskPo taskByName = dataTaskMapper.selectByNameNotId(dataTaskDto.getName(), dataTaskDto.getId());
        if (taskByName != null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "任务名称不能重复");
        }
        DataTaskPo dataTaskPo = new DataTaskPo();
        BeanUtils.copyProperties(dataTaskDto, dataTaskPo);

        if (TaskTypeEnum.SCHEDULED.getCode().equals(dataTaskDto.getType())) {
            try {
                dataTaskPo.setCron(DateUtil.dateTimeToCron(dataTaskDto.getCron()));
            } catch (ParseException e) {
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "cron表达式错误,格式为yyyy-MM-dd HH:mm:ss");
            }
            paramCheck(dataTaskDto);
        }

        dataTaskPo.setUpdateTime(new Date());
        dataTaskPo.setBusinessConfig(JsonUtil.objectToJson(dataTaskDto.getBusinessConfig()));
        dataTaskMapper.updateById(dataTaskPo);
        //调用netty接口
        TaskManagerUtil.taskManager(dataTaskDto.getNode(), OperateTypeEnum.UPDATE, dataTaskDto);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTask(Integer id) {
        DataTaskPo deleteTask = dataTaskMapper.getById(id);
        if (deleteTask == null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "任务不存在");
        }
        dataTaskMapper.deleteById(id);
        String node = deleteTask.getNode();
        // 调用netty接口
        Map<String, Integer> map = new HashMap<>(1);
        map.put("taskId", id);
        TaskManagerUtil.taskManager(node, OperateTypeEnum.DELETE, map);
        return true;
    }

    @Override
    public PageVo<DataTaskVo> listAllTasks(DataTaskReq req) {
        PageVo<DataTaskPo> pageList = PageHelperUtil.startPage(req, () -> dataTaskMapper.selectByCondition(req));

        List<DataTaskVo> dataTaskPos = pageList.getList()
                .stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        return new PageVo<>(req.getPageNo(), req.getPageSize(), pageList.getTotal(), dataTaskPos);
    }

    @Override
    public void updateTaskStatus(Integer id, TaskStatusEnum taskStatusEnum) {
        dataTaskMapper.updateStatus(id, taskStatusEnum.getCode());
    }

    @Override
    public String getTaskNameByIdFromMemoryCache(Integer id) {
        if (taskIdAndTaskNameMap.containsKey(id)) {
            return taskIdAndTaskNameMap.get(id);
        }
        DataTaskPo taskPo = dataTaskMapper.getById(id);
        if (taskPo == null) {
            return "";
        }
        taskIdAndTaskNameMap.put(id, taskPo.getName());
        return taskPo.getName();
    }

    /**
     * 将PO转换为VO
     *
     * @param dataTaskPo PO对象
     * @return VO对象
     */
    private DataTaskVo convertToVO(DataTaskPo dataTaskPo) {
        DataTaskVo dataTaskVo = new DataTaskVo();
        BeanUtils.copyProperties(dataTaskPo, dataTaskVo);
        if (StringUtils.isNotBlank(dataTaskVo.getCron())) {
            dataTaskVo.setCron(DateUtil.cronToDateTime(dataTaskVo.getCron()));
        }
        dataTaskVo.setBusinessConfig(JsonUtil.jsonToObject(dataTaskPo.getBusinessConfig(), Object.class));
        return dataTaskVo;
    }
}
