package com.espc.sec.flow.aspect;

import com.espc.sec.flow.bean.StatStruct;
import org.aspectj.lang.JoinPoint;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2018-09-18
 */
public interface IStat {

    /**
     * 统计每个小时，每个节点，每种类型的分流数量，每天0点要把内容清空
     */
    Map<String, Map<String, Map<String, StatStruct>>> STAT_FLOW_MAP = new ConcurrentHashMap<>(40);
    Map<String, Map<String, Map<String, StatStruct>>> THIS_MACHINE_STAT_MAP = new ConcurrentHashMap<>(40);
    Map<String, Map<String, Map<String, StatStruct>>> FLOW_SERVER_STAT_MAP = new ConcurrentHashMap<>(40);
    Map<String, Map<String, Map<String, StatStruct>>> ICE_DATA_POOL_STAT_MAP = new ConcurrentHashMap<>(40);
    Map<String, Map<String, Map<String, StatStruct>>> SFTP_DATA_POOL_STAT_MAP = new ConcurrentHashMap<>(40);

    /**
     * 统计分流的文件数量
     *
     * @param joinPoint 切入点
     */
    void statFlow(JoinPoint joinPoint);
}
