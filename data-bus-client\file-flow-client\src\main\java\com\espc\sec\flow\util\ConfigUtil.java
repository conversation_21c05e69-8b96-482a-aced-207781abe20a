package com.espc.sec.flow.util;

import com.espc.sec.flow.conf.Config;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2017-07-26
 * 配置util
 */
@Slf4j
public class ConfigUtil {

    /**
     * 系统配置
     */
    public static Config config;

    /**
     * 需要优先入库的节点
     */
    public static Set<String> highPriorityUpperNodes;
    /**
     * 需要优先入库的文件类型
     */
    public static Set<String> highPriorityUpperTypes;

    /**
     * 是否要把文件读到内存（如果只有一个本机代理，且类型是move，就无需读到内存）
     */
    private static boolean needReadToMem = false;
    /**
     * 判断在分流完之后是否要删除文件（如果有本地move就不用删除，否则就要删除文件）
     */
    private static boolean needDeleteFile = true;

    /**
     * 加载配置文件
     *
     * @return 返回系统配置
     * @throws Exception 抛出异常
     */
    public static void initConf() throws Exception {
        Config config = JsonUtil.objectMapper.readValue(new File("conf/FileFlowClient/conf/config.json"), Config.class);
        config.setSourceDir(formatPathConfig(config.getSourceDir()));
        config.setTempDir(formatPathConfig(config.getTempDir()));
        config.setHighPriorityDirs(formatPathConfig(config.getHighPriorityDirs()));
        config.setHighPriorityNodes(formatNodeAndTypeConfig(config.getHighPriorityNodes()));
        config.setHighPriorityTypes(formatNodeAndTypeConfig(config.getHighPriorityTypes()));
        List<Config.ThisMachineServersBean> thisMachineList = config.getThisMachineServers();
        for (Config.ThisMachineServersBean bean : thisMachineList) {
            bean.setDirs(formatPathConfig(bean.getDirs()));
            bean.setTarget(formatPathConfig(bean.getTarget()));
            bean.setNodes(formatNodeAndTypeConfig(bean.getNodes()));
            bean.setTypes(formatNodeAndTypeConfig(bean.getTypes()));
        }
        List<Config.FlowServersBean> flowServerList = config.getFlowServers();
        for (Config.FlowServersBean bean : flowServerList) {
            bean.setDirs(formatPathConfig(bean.getDirs()));
            bean.setNodes(formatNodeAndTypeConfig(bean.getNodes()));
            bean.setTypes(formatNodeAndTypeConfig(bean.getTypes()));
        }

        log.debug("init conf success");
        ConfigUtil.config = config;
        if (!thisMachineList.isEmpty()) {
            for (Config.ThisMachineServersBean bean : thisMachineList) {
                if ("move".equalsIgnoreCase(bean.getMoveType())) {
                    needDeleteFile = false;
                    break;
                }
            }
        }

        if (!flowServerList.isEmpty()) {
            needReadToMem = true;
        } else if (!thisMachineList.isEmpty()) {
            for (Config.ThisMachineServersBean bean : thisMachineList) {
                if (!"move".equalsIgnoreCase(bean.getMoveType())) {
                    needReadToMem = true;
                    break;
                }
            }
        }

        highPriorityUpperNodes = new HashSet<>();
        for (String s : config.getHighPriorityNodes()) {
            highPriorityUpperNodes.add(s.toUpperCase());
        }
        highPriorityUpperTypes = new HashSet<>();
        for (String s : config.getHighPriorityTypes()) {
            highPriorityUpperTypes.add(s.toUpperCase());
        }
    }

    /**
     * 检查配置是否正确
     *
     * @return 配置正确返回true，否则返回false
     */
    public static boolean checkConfig() {
        Set<String> sourceDir = ConfigUtil.config.getSourceDir();
        if (sourceDir.isEmpty()) {
            log.error("sourceDir no config.");
            return false;
        }
        for (String dir : sourceDir) {
            File file = new File(dir);
            if (!file.exists()) {
                log.error(dir + " does not exists.");
                return false;
            }
            if (!file.isDirectory()) {
                log.error(dir + " is not a dir.");
                return false;
            }
        }

        Set<String> highPriorityDirs = ConfigUtil.config.getHighPriorityDirs();
        for (String dir : highPriorityDirs) {
            if (!sourceDir.contains(dir)) {
                log.error("highPriorityDirs config error. " + dir + " is not in sourceDir.");
                return false;
            }
        }

        List<Config.ThisMachineServersBean> thisMachines = ConfigUtil.config.getThisMachineServers();
        List<Config.FlowServersBean> flowServers = ConfigUtil.config.getFlowServers();
        if (thisMachines.isEmpty() && flowServers.isEmpty()) {
            log.error("no proxy config.");
            return false;
        }
        int moveNum = 0;
        for (Config.ThisMachineServersBean bean : thisMachines) {
            if ("move".equalsIgnoreCase(bean.getMoveType())) {
                moveNum += 1;
            }
        }
        if (moveNum > 1) {
            log.error("this machine move type is more than 1.");
            return false;
        }

        return true;
    }

    /**
     * 格式化路径的配置
     *
     * @param path 配置的路径
     * @return 格式化之后的路径, 格式如：D:\Data\saveRessbleData，没有最后的斜杠
     */
    private static String formatPathConfig(String path) {
        if (path.isEmpty()) {
            return "";
        }
        File file = new File(path);
        return file.getAbsolutePath();
    }

    /**
     * 格式化路径的配置
     *
     * @param pathSet 配置的路径列表
     * @return 格式化之后的路径列表
     */
    private static Set<String> formatPathConfig(Set<String> pathSet) {
        Set<String> list = new HashSet<>();
        for (String path : pathSet) {
            list.add(formatPathConfig(path));
        }
        return list;
    }

    /**
     * 格式化节点和类型的配置，把字符串都转换成大写
     *
     * @param set 原始的配置
     * @return 返回转换之后的配置
     */
    private static Set<String> formatNodeAndTypeConfig(Set<String> set) {
        Set<String> list = new HashSet<>();
        for (String str : set) {
            list.add(str);
        }
        return list;
    }

    /**
     * 判断是否需要把文件读取到内存
     *
     * @return 返回needReadToMem
     */
    public static boolean needReadToMem() {
        return needReadToMem;
    }

    /**
     * 判读是否分流完成之后要删除文件
     *
     * @return 返回needDeleteFile
     */
    public static boolean needDeleteFile() {
        return needDeleteFile;
    }

}