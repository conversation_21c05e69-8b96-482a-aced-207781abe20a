package com.espc.sec.flow.helper;

import com.espc.sec.flow.bean.AuthRequest;
import com.espc.sec.flow.bean.Constant;
import com.espc.sec.flow.bean.OperateFileRequest;
import com.espc.sec.flow.util.JsonUtil;
import com.ice.datapool.DatapoolServicePrx;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2017-11-06
 */
@Slf4j
public class DataPoolHelper {

    public DataPoolHelper() {
    }

    /**
     * 用户认证示例
     * request
     * {
     * "username":"zhang<PERSON>",
     * "password":"123456",
     * }
     * <p>
     * response
     * {
     * "status": 200,
     * "msg": "success"
     * }
     * 或者
     * {
     * "status": 403,
     * "msg": "auth failed"
     * }
     */
    public static boolean userAuth(DatapoolServicePrx prxy, String username, String password) {
        log.info("begin userAuth..............");
        AuthRequest request = new AuthRequest();
        try {
            request.setUsername(username);
            request.setPassword(password);
            String reqJson = JsonUtil.objectToJson(request);
            String respJson = prxy.authenticate(reqJson);
            log.info("authenticate result: " + respJson);
            if (!respJson.contains(Constant.SUCCESS)) {
                log.info("user auth failed.");
                return false;
            } else {
                log.info("user auth success.");
                return true;
            }
        } catch (Exception e) {
            log.error("get authenticate error ", e);
            return false;
        }
    }

    /**
     * 指定具体文件上传至数据池
     */
    public static boolean uploadFile(DatapoolServicePrx prxy, String username, String password,
                                     String targetPath, byte[] fileBytes) throws Exception {
        try {
            OperateFileRequest request = new OperateFileRequest();
            request.setTargetPath(targetPath);
            String reqJson = JsonUtil.objectToJson(request);
            String resJson = prxy.upload(reqJson, fileBytes);
            if (resJson.contains(Constant.SUCCESS)) {
                return true;
            }
            if (resJson.contains(Constant.NOT_AUTH_CODE)) {
                log.error("user not authed or expired. response:" + resJson);
                userAuth(prxy, username, password);
                return false;
            } else {
                log.error("upload file failed. response:" + resJson);
                return false;
            }
        } catch (Exception e) {
            log.error("upload file error ", e);
            throw e;
        }
    }
}
