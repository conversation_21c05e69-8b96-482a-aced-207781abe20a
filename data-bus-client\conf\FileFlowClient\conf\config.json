{
  "sourceDir": ["/data/databus/client"], //源目录
  "tempDir": "/data/databus/flowclient/temp",
  "clientBaseDirSwitch": false, //客户端需要传输根目录是否需要分流
  "highPriorityDirs":[], //需要优先入库的源目录，如果为空，则各目录优先级相同
  "highPriorityNodes":[], //需要优先入库的节点，如果为空，则各节点优先级相同
  "highPriorityTypes":[], //需要优先入库的文件类型，如果为空，则各类型优先级相同
  "fileQueueSize": 2000,
  "maxFileSize": 50, // 允许分流的最大文件大小(M)
  "threadPoolSize": 20, // 线程池的总大小
  "flowServers":[
    {
      "dirs": [], //分流的源目录，如果为空表示分流所有的目录
      "nodes":[], //分流的节点，如果为空表示分流所有的节点
      "types":[], //分流的文件类型，如果为空表示分流所有类型的文件
      "ip":"127.0.0.1", //服务端的ip
      "port": 17666, //服务端程序的端口
      "needZip":false, //是否需要压缩
      "zipFileMaxSize": 1, // 压缩阈值(M)(目录文件累计达到该值后开始压缩),如果无需压缩，则此项无效
      "needSpecialFile":true //非标准格式的文件名是否需要分流
    }
  ],
  //向本机移动的服务端配置
  "thisMachineServers": [
    //    {
    //      "moveType":"move", //文件移动类型（move、copy）,只能有一个配置move
    //      "dirs": [], //分流的源目录，如果为空表示分流所有的目录
    //      "nodes":[], //分流的节点，如果为空表示分流所有的节点
    //      "types":[], //分流的文件类型，如果为空表示分流所有类型的文件
    //      "target":"E:/Data/Client/target", //本机移动的目标目录
    //      "needSpecialFile":true //非标准格式的文件名是否需要分流
    //    }
  ]
}
