create database if not exists data_bus character set utf8mb4 collate utf8mb4_0900_ai_ci;
use data_bus;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- `data_bus`.data_task definition
CREATE TABLE IF NOT EXISTS `data_task` (
 `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
 `name` varchar(200) NOT NULL COMMENT 'job名称',
 `type` int NOT NULL COMMENT '任务类型 1-立即执行，2-定时执行',
 `cron` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'cron表达式 如果taskType=1则为空',
 `business_type` varchar(20) NOT NULL COMMENT '业务类型',
 `business_config` varchar(500) NOT NULL COMMENT '任务配置信息,根据业务类型动态变化',
 `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '任务描述',
 `status` tinyint NOT NULL COMMENT '1-待执行，2-执行中，3-执行成功，4-执行失败 默认待执行',
 `node` varchar(20) NOT NULL COMMENT '节点',
 `create_time` datetime DEFAULT NULL COMMENT '首次创建时间',
 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
 `delete_time` datetime DEFAULT NULL COMMENT '删除时间',
 `is_delete` tinyint(1) DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
 `extra_filed` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '预留字段',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET = utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务表';

-- `data_bus`.monitor_increment_data definition
CREATE TABLE IF NOT EXISTS `monitor_increment_data` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `node` varchar(20) NOT NULL COMMENT '节点',
  `database_type` varchar(20) NOT NULL COMMENT '数据库类型',
  `table_name` varchar(200) NOT NULL COMMENT '表类型',
  `size` int NOT NULL COMMENT '导出或者导入条数',
  `time` datetime DEFAULT NULL COMMENT '导出或者导入时间',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `file_name` varchar(200) DEFAULT NULL COMMENT '文件名字',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET = utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='监控增量导出导入数据表';

-- `data_bus`.monitor_task_data definition
CREATE TABLE IF NOT EXISTS `monitor_task_data` (
 `id` int NOT NULL AUTO_INCREMENT COMMENT '自增id',
 `node` varchar(20) NOT NULL COMMENT '节点',
 `task_name` varchar(200) NOT NULL COMMENT '任务名称',
 `database_type` varchar(20) NOT NULL COMMENT '数据库类型',
 `table_name` varchar(200) NOT NULL COMMENT '表类型',
 `size` int NOT NULL COMMENT '导出或者导入条数',
 `time` datetime DEFAULT NULL COMMENT '导出或者导入时间',
 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
 `file_name` varchar(500) NOT NULL COMMENT '文件名',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 CHARACTER SET = utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='监控任务导出导入数据表';

SET FOREIGN_KEY_CHECKS = 1;
