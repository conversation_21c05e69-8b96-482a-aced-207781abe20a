package com.espc.sec.dataexport.job.task;

import com.espc.sec.dataexport.common.dto.task.TidbTaskProperties;
import com.espc.sec.dataexport.common.util.SpringBeanUtil;
import com.espc.sec.dataexport.tidb.service.TidbExportServiceImpl;

/**
 * TiDB导出任务
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
public class TidbTask extends AbstractTask<TidbTaskProperties> {
    
    @Override
    protected void doExecute(TidbTaskProperties taskProperties) throws Exception {
        TidbExportServiceImpl exportService = SpringBeanUtil.getBean(TidbExportServiceImpl.class);
        exportService.taskExport(taskProperties);
    }
}