package com.espc.sec.flow.util;

import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-01-11
 */
@Slf4j
public class DateUtil {

    /**
     * 得到当前的小时
     *
     * @return 返回yyyy-MM-dd HH格式的时间
     */
    public static synchronized String getHHDate() {
        return new SimpleDateFormat("yyyy-MM-dd HH").format(new Date());
    }


    /**
     * 返回当前的时间
     *
     * @return 返回yyyyMMddHHmmss格式的时间戳
     */
    public static synchronized String getNow() {
        return new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    }


}