package com.espc.sec.dataimport.common.util;

import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.config.Environment;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;

/**
 * 配置诊断工具
 * 用于检查和诊断配置问题
 */
@Slf4j
@Component
public class ConfigDiagnosticUtil {

    /**
     * 诊断所有配置
     */
    public void diagnoseAllConfigurations() {
        log.info("=== 开始配置诊断 ===");

        if (Config.environment == null) {
            log.error("❌ EnvironmentConfig未加载");
            return;
        }

        log.info("✅ EnvironmentConfig已加载");

        // 诊断StarRocks配置
        diagnoseStarRocksConfig();

        // 诊断数据源配置
        diagnoseDatasourceConfig();

        log.info("=== 配置诊断完成 ===");
    }


    /**
     * 诊断StarRocks配置
     */
    public void diagnoseStarRocksConfig() {
        log.info("--- 诊断StarRocks配置 ---");

        Environment config = Config.environment;
        if (config.getStarRocks() == null) {
            log.warn("⚠️ StarRocks配置缺失");
            return;
        }

        var srConfig = config.getStarRocks();
        if (srConfig == null) {
            log.warn("⚠️ StarRocks导入配置缺失");
            return;
        }
    }

    /**
     * 诊断数据源配置
     */
    public void diagnoseDatasourceConfig() {
        log.info("--- 诊断数据源配置 ---");

        Environment config = Config.environment;
        if (config.getMysql() == null) {
            log.warn("⚠️ 数据源配置缺失");
            return;
        }

        var dsConfig = config.getMysql();
        log.info("Database URL: {}", dsConfig.getUrl());
        log.info("Database Username: {}", dsConfig.getUsername());
        log.info("Database Driver: {}", "com.mysql.cj.jdbc.Driver");

        // 检查数据库连接
        checkDatabaseConnection(dsConfig.getUrl(), dsConfig.getUsername(), dsConfig.getPassword());
    }

    /**
     * 检查数据库连接
     */
    private void checkDatabaseConnection(String url, String username, String password) {
        log.info("检查数据库连接: {}", url);

        try (Connection conn = DriverManager.getConnection(url, username, password)) {
            log.info("✅ 数据库连接正常");
        } catch (Exception e) {
            log.error("❌ 数据库连接失败: {}", e.getMessage());
            log.error("请检查：");
            log.error("1. 数据库服务是否启动");
            log.error("2. 连接URL是否正确");
            log.error("3. 用户名密码是否正确");
            log.error("4. 网络连接是否正常");
        }
    }

    /**
     * 打印配置摘要
     */
    public void printConfigurationSummary() {
        log.info("=== 配置摘要 ===");

        if (Config.environment == null) {
            log.error("配置未加载");
            return;
        }

        Environment config = Config.environment;

        log.info("应用名称: {}", "starrocks-import");
        log.info("服务端口: {}", 5118);

        if (config.getMysql() != null) {
            log.info("数据源配置: URL={}", config.getMysql().getUrl());
        }
    }
}