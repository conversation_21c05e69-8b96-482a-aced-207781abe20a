package com.espc.sec.flow;

import com.espc.sec.flow.business.Consumer;
import com.espc.sec.flow.business.FileReader;
import com.espc.sec.flow.conf.Config;
import com.espc.sec.flow.proxy.AbstractProxy;
import com.espc.sec.flow.proxy.ThisMachineProxy;
import com.espc.sec.flow.proxy.init.FlowServerProxyInitThread;
import com.espc.sec.flow.schedule.WriteStatInfoScheduler;
import com.espc.sec.flow.util.ConfigUtil;
import com.espc.sec.flow.util.IceUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2018-09-20
 */
@Slf4j
public class FileFlowClientMain {

    /**
     * spring框架的bean容器
     */
    // public static AbstractApplicationContext context;

    public static Map<Integer, AbstractProxy> proxyMap = new ConcurrentHashMap<>();

    // static {
    //     context = new ClassPathXmlApplicationContext("spring.xml");
    //     context.registerShutdownHook();
    // }

    /**
     * 程序入口
     *
     */
    public static void start() {
        log.info("begin to start.");
        // try {
        //     String name = ManagementFactory.getRuntimeMXBean().getName();
        //     String pid = name.split("@")[0];
        //     String startTime = DateUtil.getNow();
        //     String content = "start time:" + startTime + ", pid:" + pid + "\r\n";
        //     IoUtil.writeStringToFile("Self.pid", content, "UTF-8", true);
        // } catch (IOException e) {
        //     log.error(e.getMessage());
        // }

        // new com.common.monitoring.MonitoringService();

        try {
            ConfigUtil.initConf();
        } catch (Exception e) {
            log.error("init config failed.", e);
            System.exit(-1);
        }

        if (!ConfigUtil.checkConfig()) {
            log.error("config is invalid.");
            System.exit(-1);
        }

        if (!IceUtil.iceInit()) {
            log.error("init ice communicator failed.");
            System.exit(-1);
        }

        mkdirs();
        log.info("start success.");

        new WriteStatInfoScheduler().schedule();

        initProxyList();

        new Thread(new FileReader()).start();

        new Thread(new Consumer()).start();
    }


    /**
     * 创建必要的目录
     */
    public static void mkdirs() {
        File tempDir = new File(ConfigUtil.config.getTempDir());
        if (!tempDir.exists()) {
            if (!tempDir.mkdirs()) {
                log.error("make temp dir failed. dir path:" + ConfigUtil.config.getTempDir());
            } else {
                log.info("make temp dir success.");
            }
        }

        List<Config.ThisMachineServersBean> thisMachineServers = ConfigUtil.config.getThisMachineServers();
        for (Config.ThisMachineServersBean bean : thisMachineServers) {
            File targetDir = new File(bean.getTarget());
            if (!targetDir.exists()) {
                if (!targetDir.mkdirs()) {
                    log.error("make this machine target dir failed. dir path:" + bean.getTarget());
                } else {
                    log.info("make this machine target dir success.");
                }
            }
        }
    }

    /**
     * 初始化代理列表
     */
    private static void initProxyList() {
        int i = 1;
        List<Config.ThisMachineServersBean> thisMachineServers = ConfigUtil.config.getThisMachineServers();
        for (Config.ThisMachineServersBean bean : thisMachineServers) {
            ThisMachineProxy server = new ThisMachineProxy(bean.getTarget(), bean.getDirs(), bean.getNodes(),
                    bean.getTypes(), bean.getMoveType(), bean.isNeedSpecialFile());
            proxyMap.put(i++, server);
        }
        List<Config.FlowServersBean> remoteServers = ConfigUtil.config.getFlowServers();
        for (Config.FlowServersBean bean : remoteServers) {
            new Thread(new FlowServerProxyInitThread(bean, i++)).start();
        }
    }
}