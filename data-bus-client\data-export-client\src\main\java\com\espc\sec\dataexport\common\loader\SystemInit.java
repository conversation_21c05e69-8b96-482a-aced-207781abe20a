package com.espc.sec.dataexport.common.loader;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import com.espc.sec.dataexport.common.enums.JobTypeEnum;
import com.espc.sec.dataexport.common.enums.TaskStatusEnum;
import com.espc.sec.dataexport.common.util.FileUtils;
import com.espc.sec.dataexport.common.util.JsonUtil;
import com.espc.sec.dataexport.job.service.JobService;
import com.espc.sec.dataexport.task.entity.DataTaskPo;
import com.espc.sec.dataexport.task.service.DataTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@Component
@Slf4j
public class SystemInit {

    @Autowired
    private DataTaskService dataTaskService;
    @Autowired
    private JobService jobService;
    @Autowired
    @Qualifier("mysqlJdbcTemplate")
    private JdbcTemplate jdbcTemplate;
    @Value("classpath:sql/*.sql")
    private Resource[] sqlResources;

    @PostConstruct
    public void init() {
        initDirectory();
        initTasks();
        initMysqlDb();
    }

    /**
     * 初始化导出必要目录
     */
    private void initDirectory() {
        log.info("导出目录初始化开始");
        String exportPath = Config.commonConfig.getOutputPath();
        String exportTempPath = Config.commonConfig.getOutputTempPath();
        for (ExportTypeEnum exportTypeEnum : ExportTypeEnum.values()) {
            FileUtils.createDirIfNotExists(exportPath + File.separator + exportTypeEnum.getCode());
            FileUtils.createDirIfNotExists(exportTempPath + File.separator + exportTypeEnum.getCode());
        }
        log.info("导出目录初始化结束");
    }

    /**
     * 任务初始化
     */
    private void initTasks() {
        log.info(LogKeyword.QUARTZ_DATA_TASK + "任务系统初始化开始");
        List<DataTaskPo> dataTasks = dataTaskService.listTasksByStatus(TaskStatusEnum.PENDING);
        dataTasks.forEach(task -> {
            JobTypeEnum jobTypeEnum = JobTypeEnum.getByCode(task.getBusinessType());
            jobService.addJob(task.getId()
                    , jobTypeEnum
                    , task.getCron()
                    , JsonUtil.fileToObject(task.getBusinessConfig(), jobTypeEnum.getTaskProperties())
            );
        });
        log.info(LogKeyword.QUARTZ_DATA_TASK + "任务系统初始化完成");
    }

    /**
     * mysql语句初始化
     */
    private void initMysqlDb() {
        try {
            Arrays.sort(sqlResources, Comparator.comparing(Resource::getFilename));
            for (Resource resource : sqlResources) {
                String sqlContent = new String(Files.readAllBytes(Paths.get(resource.getURI())), StandardCharsets.UTF_8);

                String[] sqlStatements = sqlContent.split(";\\s*\\n");

                for (String sql : sqlStatements) {
                    if (!sql.trim().isEmpty()) {
                        try {
                            jdbcTemplate.execute(sql);
                            log.info("mysql语句初始化成功,{}", sql.trim());
                        } catch (DataAccessException e) {
                            log.error("Error executing SQL from {}: {}", resource.getFilename(), sql.trim(), e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("mysql表初始化失败", e);
        }
    }

}
