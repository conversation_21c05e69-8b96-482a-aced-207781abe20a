package com.espc.sec.dataimport.task.dto;

import com.espc.sec.dataimport.common.dto.PageDto;
import lombok.Data;

/**
 * 数据任务查询参数
 */
@Data
public class DataTaskReq extends PageDto {
    /**
     * job名称
     */
    private String name;

    /**
     * 任务类型 1-立即执行，2-定时执行
     */
    private Integer type;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 状态 1-待执行，2-执行中，3-执行成功，4-执行失败
     */
    private Integer status;

    /**
     * 节点
     */
    private String node;

}
