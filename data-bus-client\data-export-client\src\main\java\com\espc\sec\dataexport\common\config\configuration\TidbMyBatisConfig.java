package com.espc.sec.dataexport.common.config.configuration;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * TiDB MyBatis 配置
 * 使用 tidbDataSource 数据源
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Configuration
@MapperScan(
        basePackages = "com.espc.sec.dataexport.tidb.mapper",
        sqlSessionFactoryRef = "tidbSqlSessionFactory"
)
public class TidbMyBatisConfig {

    /**
     * MyBatis Plus 分页插件
     */
    @Bean("tidbMybatisPlusInterceptor")
    public MybatisPlusInterceptor tidbMybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 添加分页插件
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());
        return interceptor;
    }

    /**
     * TiDB SqlSessionFactory
     */
    @Bean("tidbSqlSessionFactory")
    public SqlSessionFactory tidbSqlSessionFactory(
            @Qualifier("tidbDataSource") DataSource dataSource
    ) throws Exception {
        MybatisSqlSessionFactoryBean sessionFactory = new MybatisSqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);

        // MyBatis 配置
        MybatisConfiguration configuration = new MybatisConfiguration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setLogImpl(org.apache.ibatis.logging.slf4j.Slf4jImpl.class);
        sessionFactory.setConfiguration(configuration);

        // 添加分页插件
        sessionFactory.setPlugins(tidbMybatisPlusInterceptor());

        // 设置mapper xml文件位置 - 修复路径配置
        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            Resource[] resources = resolver.getResources("classpath:mapper/tidb/*.xml");
            log.info("找到TiDB Mapper XML文件数量: {}", resources.length);
            for (Resource resource : resources) {
                log.info("TiDB Mapper XML文件: {}", resource.getFilename());
            }
            sessionFactory.setMapperLocations(resources);
        } catch (Exception e) {
            log.error("加载TiDB Mapper XML文件失败", e);
            throw e;
        }

        log.info("初始化TiDB MyBatis SqlSessionFactory");
        return sessionFactory.getObject();
    }

    /**
     * TiDB SqlSessionTemplate
     */
    @Bean("tidbSqlSessionTemplate")
    public SqlSessionTemplate tidbSqlSessionTemplate(
            @Qualifier("tidbSqlSessionFactory") SqlSessionFactory sqlSessionFactory
    ) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}