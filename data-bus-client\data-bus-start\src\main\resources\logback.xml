<?xml version="1.0" encoding="utf-8"?>
<configuration scan="true" scanPeriod="60 seconds">
    <property file="conf/logback.properties"/>

    <appender name="CONSOLE-LOG" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>${LOG_HIDE_FILTER}</expression>
            </evaluator>
            <onMismatch>NEUTRAL</onMismatch>
            <onMatch>DENY</onMatch>
        </filter>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>${LOG_DISPLAY_FILTER}</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd' 'HH:mm:ss.sss} [%p] [%t] %C [%L] - %m%n</pattern>
        </encoder>
    </appender>

    <!--info日志配置，但除error级别-->
    <appender name="INFO-LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--路径-->
        <file>log/log.txt</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>${LOG_HIDE_FILTER}</expression>
            </evaluator>
            <onMismatch>NEUTRAL</onMismatch>
            <onMatch>DENY</onMatch>
        </filter>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>${LOG_DISPLAY_FILTER}</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>

        <!--滚动策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>log/log%i.txt</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>20</maxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>${LOG_SPLIT_FILE_SIZE}</maxFileSize>
        </triggeringPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd' 'HH:mm:ss.sss} [%p] [%t] %C [%L] - %m%n</pattern>
        </encoder>
    </appender>

    <!--error日志配置-->
    <appender name="ERROR-LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--路径-->
        <file>log/error.txt</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>${LOG_HIDE_FILTER}</expression>
            </evaluator>
            <onMismatch>NEUTRAL</onMismatch>
            <onMatch>DENY</onMatch>
        </filter>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>${LOG_DISPLAY_FILTER}</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>

        <!--滚动策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>log/error%i.txt</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>20</maxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>${LOG_SPLIT_FILE_SIZE}</maxFileSize>
        </triggeringPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd' 'HH:mm:ss.sss} [%p] [%t] %C [%L] - %m%n</pattern>
        </encoder>
    </appender>

    <!--STAT日志配置-->
    <appender name="STAT-INFO-LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--路径-->
        <file>log/stat.txt</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>${LOG_HIDE_FILTER}</expression>
            </evaluator>
            <onMismatch>NEUTRAL</onMismatch>
            <onMatch>DENY</onMatch>
        </filter>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>${LOG_DISPLAY_FILTER}</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>

        <!--滚动策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>log/stat%i.txt</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>20</maxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>${LOG_SPLIT_FILE_SIZE}</maxFileSize>
        </triggeringPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd' 'HH:mm:ss.sss} [%p] [%t] %C [%L] - %m%n</pattern>
        </encoder>
    </appender>
    <logger name="WriteStatInfoScheduler.stat" addititivity="false" level="${LOG_LEVEL}">
        <appender-ref ref="STAT-INFO-LOG"/>
    </logger>

    <!--flowServerStat日志配置-->
    <appender name="FLOWSERVERSTAT-INFO-LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!--路径-->
        <file>log/flowServerStat.txt</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>DENY</onMatch>
            <onMismatch>ACCEPT</onMismatch>
        </filter>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>${LOG_HIDE_FILTER}</expression>
            </evaluator>
            <onMismatch>NEUTRAL</onMismatch>
            <onMatch>DENY</onMatch>
        </filter>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>${LOG_DISPLAY_FILTER}</expression>
            </evaluator>
            <onMismatch>DENY</onMismatch>
            <onMatch>ACCEPT</onMatch>
        </filter>

        <!--滚动策略-->
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>log/flowServerStat%i.txt</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>20</maxIndex>
        </rollingPolicy>

        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>${LOG_SPLIT_FILE_SIZE}</maxFileSize>
        </triggeringPolicy>

        <encoder>
            <pattern>%d{yyyy-MM-dd' 'HH:mm:ss.sss} [%p] [%t] %C [%L] - %m%n</pattern>
        </encoder>
    </appender>
    <logger name="WriteStatInfoScheduler.flowServerStat" addititivity="false" level="${LOG_LEVEL}">
        <appender-ref ref="FLOWSERVERSTAT-INFO-LOG"/>
    </logger>

    <root level="${LOG_LEVEL}">
        <appender-ref ref="CONSOLE-LOG"/>
        <appender-ref ref="INFO-LOG"/>
        <appender-ref ref="ERROR-LOG"/>
    </root>

</configuration>
