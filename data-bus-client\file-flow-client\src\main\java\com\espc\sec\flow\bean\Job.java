package com.espc.sec.flow.bean;

import lombok.Data;

import java.io.File;

/**
 * <AUTHOR>
 * @date 2019-04-19
 */
@Data
public class Job implements Comparable<Job> {
    /**
     * 文件的源目录
     */
    private String sourceDirPath;
    /**
     * 文件的绝对路径（在临时目录的绝对路径）
     */
    private String absolutePath;
    /**
     * 文件的相对路径
     */
    private String filePath;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 文件移动的优先级（数字越大表示优先级越高）
     */
    private byte priority;
    /**
     * 文件内容（仅在需要读取到内存的时候才有此字段的内容）
     */
    private byte[] content = null;

    public Job(String sourceDirPath, String absolutePath, String filePath, String fileName, byte priority) {
        this.sourceDirPath = sourceDirPath;
        this.absolutePath = absolutePath;
        this.filePath = filePath;
        this.fileName = fileName;
        this.priority = priority;
    }

    /**
     * 得到文件
     *
     * @return 返回文件
     */
    public File getFile() {
        return new File(absolutePath);
    }

    /**
     * 得到文件名
     *
     * @return 返回文件名
     */
    public String getFileName() {
        File file = getFile();
        return file.getName();
    }

    @Override
    public int compareTo(Job o) {
        return o.getPriority() - this.priority;
    }
}
