package com.espc.sec.dataexport.metadata.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表信息VO
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TableInfoVo {
    /**
     * 表名称
     */
    private String tableName;
    
    /**
     * 表类型
     */
    private String tableType;
    
    /**
     * 行数
     */
    private Long rowCount;
    
    /**
     * 存储引擎
     */
    private String engine;
    
    /**
     * 注释
     */
    private String comment;
}