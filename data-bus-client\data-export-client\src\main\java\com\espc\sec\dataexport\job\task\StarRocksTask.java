package com.espc.sec.dataexport.job.task;

import com.espc.sec.dataexport.common.dto.task.StarRocksTaskProperties;
import com.espc.sec.dataexport.common.util.SpringBeanUtil;
import com.espc.sec.dataexport.starrocks.service.StarRocksExportServiceImpl;

public class StarRocksTask extends AbstractTask<StarRocksTaskProperties> {
    @Override
    protected void doExecute(StarRocksTaskProperties taskProperties) throws Exception {
        StarRocksExportServiceImpl exportService = SpringBeanUtil.getBean(StarRocksExportServiceImpl.class);
        exportService.taskExport(taskProperties);
    }
}
