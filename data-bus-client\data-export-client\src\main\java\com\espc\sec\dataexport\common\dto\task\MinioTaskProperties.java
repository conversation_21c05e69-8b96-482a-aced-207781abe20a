package com.espc.sec.dataexport.common.dto.task;

import lombok.Data;

import java.util.List;

@Data
public class MinioTaskProperties extends BaseTaskProperties {
    /**
     * bucket名字pr-inf-cert  pr-inf-file  pr-inf-eml
     */
    private List<String> bucketPrefix;
    /**
     * 具体需要导出的数量大小
     */
    private Integer dataSize;
    /**
     * 开始时间
     */
    private String dataStartTime;
    /**
     * 结束时间
     */
    private String dataEndTime;
}
