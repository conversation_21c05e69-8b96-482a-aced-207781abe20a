package com.espc.sec.flow.util;

/**
 * <AUTHOR>
 * @date 2018-06-04
 */
public class Config {
    private String flowServiceIp;
    private int flowServicePort;
    private String tempDir;
    private String targetDir;
    private int fileQueueSize;
    private int unZipThreadPoolSize;
    private String acceptThreadSold;
    private int sleepTime;

    public String getFlowServiceIp() {
        return flowServiceIp;
    }

    public void setFlowServiceIp(String flowServiceIp) {
        this.flowServiceIp = flowServiceIp;
    }


    public int getFlowServicePort() {
        return flowServicePort;
    }

    public void setFlowServicePort(int flowServicePort) {
        this.flowServicePort = flowServicePort;
    }

    public String getTempDir() {
        return tempDir;
    }

    public void setTempDir(String tempDir) {
        this.tempDir = tempDir;
    }

    public String getTargetDir() {
        return targetDir;
    }

    public void setTargetDir(String targetDir) {
        this.targetDir = targetDir;
    }

    public int getFileQueueSize() {
        return fileQueueSize;
    }

    public void setFileQueueSize(int fileQueueSize) {
        this.fileQueueSize = fileQueueSize;
    }

    /**
     * Getter method for property <tt>unZipThreadPoolSize</tt>.
     *
     * @return property value of unZipThreadPoolSize
     */

    public int getUnZipThreadPoolSize() {
        return unZipThreadPoolSize;
    }

    /**
     * Setter method for property <tt>unZipThreadPoolSize</tt>.
     *
     * @param unZipThreadPoolSize value to be assigned to property unZipThreadPoolSize
     */
    public void setUnZipThreadPoolSize(int unZipThreadPoolSize) {
        this.unZipThreadPoolSize = unZipThreadPoolSize;
    }

    /**
     * Getter method for property <tt>acceptThreadSold</tt>.
     *
     * @return property value of acceptThreadSold
     */

    public String getAcceptThreadSold() {
        return acceptThreadSold;
    }

    /**
     * Setter method for property <tt>acceptThreadSold</tt>.
     *
     * @param acceptThreadSold value to be assigned to property acceptThreadSold
     */
    public void setAcceptThreadSold(String acceptThreadSold) {
        this.acceptThreadSold = acceptThreadSold;
    }

    /**
     * Getter method for property <tt>sleepTime</tt>.
     *
     * @return property value of sleepTime
     */

    public int getSleepTime() {
        return sleepTime;
    }

    /**
     * Setter method for property <tt>sleepTime</tt>.
     *
     * @param sleepTime value to be assigned to property sleepTime
     */
    public void setSleepTime(int sleepTime) {
        this.sleepTime = sleepTime;
    }
}