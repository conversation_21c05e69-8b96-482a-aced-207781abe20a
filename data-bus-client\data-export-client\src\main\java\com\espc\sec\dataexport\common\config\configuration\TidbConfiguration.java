package com.espc.sec.dataexport.common.config.configuration;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.config.Environment;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * TiDB数据源配置
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Slf4j
@Configuration
public class TidbConfiguration {

    @Bean(name = "tidbDataSource")
    @ConditionalOnProperty(name = "tidb.enabled", havingValue = "true", matchIfMissing = true)
    public DataSource tidbDataSource() {
        Environment.TidbDTO dsConfig = Config.environment.tidb;
        if (dsConfig == null) {
            log.warn("TiDB数据源配��为空，跳过初始化");
            return null;
        }

        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(dsConfig.getUrl());
        hikariConfig.setUsername(dsConfig.getUsername());
        hikariConfig.setPassword(dsConfig.getPassword());
        hikariConfig.setDriverClassName("com.mysql.cj.jdbc.Driver");
        
        // 1.连接池配置
        hikariConfig.setMaximumPoolSize(10);
        hikariConfig.setMinimumIdle(2);
        hikariConfig.setConnectionTimeout(30000);
        hikariConfig.setIdleTimeout(600000);
        hikariConfig.setMaxLifetime(1800000);
        hikariConfig.setPoolName("tidbHikariCP");

        log.info("初始化TiDB数据源: {}", dsConfig.getUrl());
        return new HikariDataSource(hikariConfig);
    }

    @Bean(name = "tidbJdbcTemplate")
    public JdbcTemplate tidbJdbcTemplate() {
        DataSource dataSource = tidbDataSource();
        if (dataSource == null) {
            log.warn("TiDB数据源为空，无法创建JdbcTemplate");
            return null;
        }
        return new JdbcTemplate(dataSource);
    }
}