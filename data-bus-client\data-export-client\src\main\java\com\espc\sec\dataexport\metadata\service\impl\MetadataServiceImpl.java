package com.espc.sec.dataexport.metadata.service.impl;

import com.espc.sec.dataexport.common.vo.PageVo;
import com.espc.sec.dataexport.metadata.dto.MetadataPageReq;
import com.espc.sec.dataexport.metadata.service.MetadataService;
import com.espc.sec.dataexport.metadata.service.MetadataStorageService;
import com.espc.sec.dataexport.metadata.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.DescribeTopicsResult;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.admin.TopicDescription;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 元数据服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Service
public class MetadataServiceImpl implements MetadataService {

    @Autowired(required = false)
    private AdminClient kafkaAdminClient;

    @Autowired(required = false)
    private RestHighLevelClient elasticsearchClient;

    @Autowired(required = false)
    @Qualifier("starRocksJdbcTemplate")
    private JdbcTemplate starRocksJdbcTemplate;

    @Autowired(required = false)
    @Qualifier("tidbJdbcTemplate")
    private JdbcTemplate tidbJdbcTemplate;
    
    @Autowired
    private MetadataStorageService metadataStorageService;
    
    @Value("${spring.application.name:data-export-client}")
    private String currentNode;

    @Override
    public PageVo<TopicInfoVo> getKafkaTopics(MetadataPageReq req) {
        try {
            if (kafkaAdminClient == null) {
                log.warn("Kafka AdminClient未配置");
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }

            ListTopicsResult listTopicsResult = kafkaAdminClient.listTopics();
            Set<String> topicNames = listTopicsResult.names().get();
            
            // 直接排序，不进行关键字筛选
            List<String> sortedTopics = topicNames.stream()
                    .sorted()
                    .collect(Collectors.toList());

            // 分页处理
            int start = (req.getPageNo() - 1) * req.getPageSize();
            int end = Math.min(start + req.getPageSize(), sortedTopics.size());
            List<String> pagedTopics = sortedTopics.subList(start, end);

            // 获取详细信息
            DescribeTopicsResult describeResult = kafkaAdminClient.describeTopics(pagedTopics);
            Map<String, TopicDescription> descriptions = describeResult.all().get();

            List<TopicInfoVo> topicInfos = pagedTopics.stream()
                    .map(topicName -> {
                        TopicDescription desc = descriptions.get(topicName);
                        
                        // 存储元数据到数据库
                        try {
                            List<String> fieldNames = Arrays.asList("partitions", "replicas", "description");
                            List<String> fieldTypes = Arrays.asList("INTEGER", "INTEGER", "VARCHAR");
                            metadataStorageService.storeMetadata("kafka", topicName, currentNode, fieldNames, fieldTypes);
                        } catch (Exception e) {
                            log.warn("存储Kafka主题元数据失败: {}", topicName, e);
                        }
                        
                        return TopicInfoVo.builder()
                                .topicName(topicName)
                                .partitions(desc != null ? desc.partitions().size() : 0)
                                .replicas(desc != null && !desc.partitions().isEmpty() ? 
                                         desc.partitions().get(0).replicas().size() : 0)
                                .description("Kafka主题")
                                .build();
                    })
                    .collect(Collectors.toList());

            return new PageVo<>(req.getPageNo(), req.getPageSize(), (long) sortedTopics.size(), topicInfos);

        } catch (InterruptedException | ExecutionException e) {
            log.error("获取Kafka主题列表失败", e);
            return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
        }
    }

    @Override
    public PageVo<IndexInfoVo> getElasticsearchIndices(MetadataPageReq req) {
        try {
            if (elasticsearchClient == null) {
                log.warn("Elasticsearch客户端未配置");
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }

            // 1) 获取所有索引名
            org.elasticsearch.client.indices.GetIndexRequest getIndexRequest = new org.elasticsearch.client.indices.GetIndexRequest();
            getIndexRequest.indices("*");
            org.elasticsearch.client.indices.GetIndexResponse getIndexResponse = elasticsearchClient.indices().get(getIndexRequest, RequestOptions.DEFAULT);

            String[] indices = getIndexResponse.getIndices();
            if (indices == null || indices.length == 0) {
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }

            // 2) 直接排序，不进行关键字筛选
            List<String> sortedIndices = Arrays.stream(indices)
                    .sorted()
                    .collect(Collectors.toList());

            // 3) 分页
            int start = Math.max(0, (req.getPageNo() - 1) * req.getPageSize());
            int end = Math.min(start + req.getPageSize(), sortedIndices.size());
            if (start >= end) {
                return new PageVo<>(req.getPageNo(), req.getPageSize(), (long) sortedIndices.size(), new ArrayList<>());
            }

            List<String> pagedIndices = sortedIndices.subList(start, end);

            // 4) 使用低阶客户端调用 _cat/indices 获取文档数和存储大小（返回 JSON 便于解析）
            String indicesString = String.join(",", pagedIndices);
            // 构造 path，注意对路径段做 URL 编码
            String path = "/_cat/indices";
            if (!indicesString.isEmpty()) {
                path += "/" + java.net.URLEncoder.encode(indicesString, java.nio.charset.StandardCharsets.UTF_8.toString());
            }
            path += "?format=json&h=index,docs.count,store.size";

            org.elasticsearch.client.Request catRequest = new org.elasticsearch.client.Request("GET", path);
            org.elasticsearch.client.Response catResponse = elasticsearchClient.getLowLevelClient().performRequest(catRequest);

            // 5) 解析响应（使用 EntityUtils + ObjectMapper.readTree，避免 TypeReference 报错）
            String jsonResponse = org.apache.http.util.EntityUtils.toString(catResponse.getEntity(), java.nio.charset.StandardCharsets.UTF_8);
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            com.fasterxml.jackson.databind.JsonNode rootNode = objectMapper.readTree(jsonResponse);

            // 6) 转成 Map 以便快速查找
            Map<String, IndexInfoVo> statsMap = new HashMap<>();
            if (rootNode != null && rootNode.isArray()) {
                for (com.fasterxml.jackson.databind.JsonNode item : rootNode) {
                    String indexName = item.path("index").asText(null);
                    if (indexName == null) {
                        continue;
                    }
                    String docsCountStr = item.path("docs.count").asText("0");
                    // 有些场景 docs.count 可能带逗号，去掉再解析
                    docsCountStr = docsCountStr.replaceAll(",", "").trim();
                    long docCount = 0L;
                    try {
                        docCount = Long.parseLong(docsCountStr);
                    } catch (NumberFormatException ignored) {
                    }

                    String storeSize = item.path("store.size").asText("0b");

                    statsMap.put(indexName, IndexInfoVo.builder()
                            .indexName(indexName)
                            .docCount(docCount)
                            .storeSize(storeSize)
                            .status("open")
                            .build());
                }
            }

            // 7) 构建最终分页返回（若 cat 没返回某索引信息，使用默认值）
            List<IndexInfoVo> indexInfos = pagedIndices.stream()
                    .map(indexName -> statsMap.getOrDefault(indexName,
                            IndexInfoVo.builder()
                                    .indexName(indexName)
                                    .docCount(0L)
                                    .storeSize("0b")
                                    .status("unknown")
                                    .build()))
                    .collect(Collectors.toList());

            return new PageVo<>(req.getPageNo(), req.getPageSize(), (long) sortedIndices.size(), indexInfos);

        } catch (Exception e) {
            log.error("获取Elasticsearch索引列表失败", e);
            return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
        }
    }

    @Override
    public PageVo<IndexFieldInfoVo> getElasticsearchFields(MetadataPageReq req) {
        try {
            if (elasticsearchClient == null || req.getIndexName() == null) {
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }

            // 使用低级客户端调用 _mapping API，兼容7.10.2版本
            String path = "/" + req.getIndexName() + "/_mapping";
            org.elasticsearch.client.Request request = new org.elasticsearch.client.Request("GET", path);
            org.elasticsearch.client.Response response = elasticsearchClient.getLowLevelClient().performRequest(request);
            
            // 解析响应
            String jsonResponse = org.apache.http.util.EntityUtils.toString(response.getEntity(), java.nio.charset.StandardCharsets.UTF_8);
            com.fasterxml.jackson.databind.ObjectMapper objectMapper = new com.fasterxml.jackson.databind.ObjectMapper();
            com.fasterxml.jackson.databind.JsonNode rootNode = objectMapper.readTree(jsonResponse);
            
            // 获取索引的mapping信息
            com.fasterxml.jackson.databind.JsonNode indexNode = rootNode.path(req.getIndexName());
            if (indexNode.isMissingNode()) {
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }
            
            com.fasterxml.jackson.databind.JsonNode mappingsNode = indexNode.path("mappings");
            if (mappingsNode.isMissingNode()) {
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }
            
            com.fasterxml.jackson.databind.JsonNode propertiesNode = mappingsNode.path("properties");
            if (propertiesNode.isMissingNode()) {
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }

            List<IndexFieldInfoVo> fieldInfos = new ArrayList<>();
            
            // 遍历字段
            propertiesNode.fields().forEachRemaining(entry -> {
                String fieldName = entry.getKey();
                com.fasterxml.jackson.databind.JsonNode fieldNode = entry.getValue();
                
                String fieldType = fieldNode.path("type").asText("object");
                
                fieldInfos.add(IndexFieldInfoVo.builder()
                        .fieldName(fieldName)
                        .fieldType(fieldType)
                        .isSearchable(true)
                        .isAggregatable(!"text".equals(fieldType))
                        .build());
            });

            // 排序
            fieldInfos.sort(Comparator.comparing(IndexFieldInfoVo::getFieldName));
            
            // 分页处理
            int start = (req.getPageNo() - 1) * req.getPageSize();
            int end = Math.min(start + req.getPageSize(), fieldInfos.size());
            List<IndexFieldInfoVo> pagedFields = fieldInfos.subList(start, end);

            return new PageVo<>(req.getPageNo(), req.getPageSize(), (long) fieldInfos.size(), pagedFields);

        } catch (Exception e) {
            log.error("获取Elasticsearch字段列表失败", e);
            return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
        }
    }

    @Override
    public PageVo<DatabaseInfoVo> getStarRocksDatabases(MetadataPageReq req) {
        try {
            if (starRocksJdbcTemplate == null) {
                log.warn("StarRocks JdbcTemplate未配置");
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }

            String sql = "SHOW DATABASES";
            List<String> databases = starRocksJdbcTemplate.queryForList(sql, String.class);

            // 直接排序，不进行关键字筛选
            List<String> sortedDatabases = databases.stream()
                    .sorted()
                    .collect(Collectors.toList());

            // 分页处理
            int start = (req.getPageNo() - 1) * req.getPageSize();
            int end = Math.min(start + req.getPageSize(), sortedDatabases.size());
            List<String> pagedDatabases = sortedDatabases.subList(start, end);

            List<DatabaseInfoVo> databaseInfos = pagedDatabases.stream()
                    .map(dbName -> {
                        // 获取表数量
                        Integer tableCount = 0;
                        try {
                            String countSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ?";
                            tableCount = starRocksJdbcTemplate.queryForObject(countSql, Integer.class, dbName);
                        } catch (Exception e) {
                            log.warn("获取数据库{}表数量失败", dbName, e);
                        }

                        return DatabaseInfoVo.builder()
                                .databaseName(dbName)
                                .tableCount(tableCount)
                                .charset("utf8")
                                .description("StarRocks数据库")
                                .build();
                    })
                    .collect(Collectors.toList());

            return new PageVo<>(req.getPageNo(), req.getPageSize(), (long) sortedDatabases.size(), databaseInfos);

        } catch (Exception e) {
            log.error("获取StarRocks数据库列表失败", e);
            return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
        }
    }

    @Override
    public PageVo<TableInfoVo> getStarRocksTables(MetadataPageReq req) {
        try {
            if (starRocksJdbcTemplate == null || req.getDatabaseName() == null) {
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }

            String sql = "SELECT table_name, table_type, table_comment FROM information_schema.tables WHERE table_schema = ?";
            List<Map<String, Object>> tables = starRocksJdbcTemplate.queryForList(sql, req.getDatabaseName());

            // 直接排序，不进行关键字筛选
            List<Map<String, Object>> sortedTables = tables.stream()
                    .sorted((t1, t2) -> t1.get("table_name").toString().compareTo(t2.get("table_name").toString()))
                    .collect(Collectors.toList());

            // 分页处理
            int start = (req.getPageNo() - 1) * req.getPageSize();
            int end = Math.min(start + req.getPageSize(), sortedTables.size());
            List<Map<String, Object>> pagedTables = sortedTables.subList(start, end);

            List<TableInfoVo> tableInfos = pagedTables.stream()
                    .map(table -> TableInfoVo.builder()
                            .tableName(table.get("table_name").toString())
                            .tableType(table.get("table_type") != null ? table.get("table_type").toString() : "BASE TABLE")
                            .rowCount(0L) // StarRocks可能需要特殊查询获取行数
                            .engine("StarRocks")
                            .comment(table.get("table_comment") != null ? table.get("table_comment").toString() : "")
                            .build())
                    .collect(Collectors.toList());

            return new PageVo<>(req.getPageNo(), req.getPageSize(), (long) sortedTables.size(), tableInfos);

        } catch (Exception e) {
            log.error("获取StarRocks表列表失败", e);
            return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
        }
    }

    @Override
    public PageVo<FieldInfoVo> getStarRocksFields(MetadataPageReq req) {
        try {
            if (starRocksJdbcTemplate == null || req.getDatabaseName() == null || req.getTableName() == null) {
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }

            String sql = "SELECT column_name, data_type, is_nullable, column_default, column_comment, column_key " +
                        "FROM information_schema.columns WHERE table_schema = ? AND table_name = ? ORDER BY ordinal_position";
            List<Map<String, Object>> columns = starRocksJdbcTemplate.queryForList(sql, req.getDatabaseName(), req.getTableName());

            // 直接使用查询结果，不进行关键字筛选（已按ordinal_position排序）
            List<Map<String, Object>> sortedColumns = columns;

            // 分页处理
            int start = (req.getPageNo() - 1) * req.getPageSize();
            int end = Math.min(start + req.getPageSize(), sortedColumns.size());
            List<Map<String, Object>> pagedColumns = sortedColumns.subList(start, end);

            List<FieldInfoVo> fieldInfos = pagedColumns.stream()
                    .map(column -> FieldInfoVo.builder()
                            .fieldName(column.get("column_name").toString())
                            .fieldType(column.get("data_type").toString())
                            .isPrimaryKey("PRI".equals(column.get("column_key")))
                            .isNullable("YES".equals(column.get("is_nullable")))
                            .defaultValue(column.get("column_default") != null ? column.get("column_default").toString() : null)
                            .comment(column.get("column_comment") != null ? column.get("column_comment").toString() : "")
                            .build())
                    .collect(Collectors.toList());

            return new PageVo<>(req.getPageNo(), req.getPageSize(), (long) sortedColumns.size(), fieldInfos);

        } catch (Exception e) {
            log.error("获取StarRocks字段列表失败", e);
            return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
        }
    }

    @Override
    public PageVo<DatabaseInfoVo> getTidbDatabases(MetadataPageReq req) {
        try {
            if (tidbJdbcTemplate == null) {
                log.warn("TiDB JdbcTemplate未配置");
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }

            String sql = "SHOW DATABASES";
            List<String> databases = tidbJdbcTemplate.queryForList(sql, String.class);

            // 直接排序，不进行系统数据库过滤和关键字筛选
            List<String> sortedDatabases = databases.stream()
                    .sorted()
                    .collect(Collectors.toList());

            // 分页处理
            int start = (req.getPageNo() - 1) * req.getPageSize();
            int end = Math.min(start + req.getPageSize(), sortedDatabases.size());
            List<String> pagedDatabases = sortedDatabases.subList(start, end);

            List<DatabaseInfoVo> databaseInfos = pagedDatabases.stream()
                    .map(dbName -> {
                        // 获取表数量
                        Integer tableCount = 0;
                        try {
                            String countSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ?";
                            tableCount = tidbJdbcTemplate.queryForObject(countSql, Integer.class, dbName);
                        } catch (Exception e) {
                            log.warn("获取数据库{}表数量失败", dbName, e);
                        }

                        return DatabaseInfoVo.builder()
                                .databaseName(dbName)
                                .tableCount(tableCount)
                                .charset("utf8mb4")
                                .description("TiDB数据库")
                                .build();
                    })
                    .collect(Collectors.toList());

            return new PageVo<>(req.getPageNo(), req.getPageSize(), (long) sortedDatabases.size(), databaseInfos);

        } catch (Exception e) {
            log.error("获取TiDB数据库列表失败", e);
            return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
        }
    }

    @Override
    public PageVo<TableInfoVo> getTidbTables(MetadataPageReq req) {
        try {
            if (tidbJdbcTemplate == null || req.getDatabaseName() == null) {
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }

            String sql = "SELECT table_name, table_type, engine, table_comment FROM information_schema.tables WHERE table_schema = ?";
            List<Map<String, Object>> tables = tidbJdbcTemplate.queryForList(sql, req.getDatabaseName());

            // 直接排序，不进行关键字筛选
            List<Map<String, Object>> sortedTables = tables.stream()
                    .sorted((t1, t2) -> t1.get("table_name").toString().compareTo(t2.get("table_name").toString()))
                    .collect(Collectors.toList());

            // 分页处理
            int start = (req.getPageNo() - 1) * req.getPageSize();
            int end = Math.min(start + req.getPageSize(), sortedTables.size());
            List<Map<String, Object>> pagedTables = sortedTables.subList(start, end);

            List<TableInfoVo> tableInfos = pagedTables.stream()
                    .map(table -> TableInfoVo.builder()
                            .tableName(table.get("table_name").toString())
                            .tableType(table.get("table_type") != null ? table.get("table_type").toString() : "BASE TABLE")
                            .rowCount(0L) // 可以通过额外查询获取准确行数
                            .engine(table.get("engine") != null ? table.get("engine").toString() : "InnoDB")
                            .comment(table.get("table_comment") != null ? table.get("table_comment").toString() : "")
                            .build())
                    .collect(Collectors.toList());

            return new PageVo<>(req.getPageNo(), req.getPageSize(), (long) sortedTables.size(), tableInfos);

        } catch (Exception e) {
            log.error("获取TiDB表列表失败", e);
            return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
        }
    }

    @Override
    public PageVo<FieldInfoVo> getTidbFields(MetadataPageReq req) {
        try {
            if (tidbJdbcTemplate == null || req.getDatabaseName() == null || req.getTableName() == null) {
                return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
            }

            String sql = "SELECT column_name, data_type, is_nullable, column_default, column_comment, column_key " +
                        "FROM information_schema.columns WHERE table_schema = ? AND table_name = ? ORDER BY ordinal_position";
            List<Map<String, Object>> columns = tidbJdbcTemplate.queryForList(sql, req.getDatabaseName(), req.getTableName());

            // 直接使用查询结果，不进行关键字筛选（已按ordinal_position排序）
            List<Map<String, Object>> sortedColumns = columns;

            // 分页处理
            int start = (req.getPageNo() - 1) * req.getPageSize();
            int end = Math.min(start + req.getPageSize(), sortedColumns.size());
            List<Map<String, Object>> pagedColumns = sortedColumns.subList(start, end);

            List<FieldInfoVo> fieldInfos = pagedColumns.stream()
                    .map(column -> FieldInfoVo.builder()
                            .fieldName(column.get("column_name").toString())
                            .fieldType(column.get("data_type").toString())
                            .isPrimaryKey("PRI".equals(column.get("column_key")))
                            .isNullable("YES".equals(column.get("is_nullable")))
                            .defaultValue(column.get("column_default") != null ? column.get("column_default").toString() : null)
                            .comment(column.get("column_comment") != null ? column.get("column_comment").toString() : "")
                            .build())
                    .collect(Collectors.toList());

            return new PageVo<>(req.getPageNo(), req.getPageSize(), (long) sortedColumns.size(), fieldInfos);

        } catch (Exception e) {
            log.error("获取TiDB字段列表失败", e);
            return new PageVo<>(req.getPageNo(), req.getPageSize(), 0L, new ArrayList<>());
        }
    }
}