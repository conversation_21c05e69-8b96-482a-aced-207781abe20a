package com.espc.sec.dataimport.common.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DateUtil extends cn.hutool.core.date.DateUtil {
    private static Pattern CRON_EXPRESSION = Pattern.compile("^(\\d+)\\s(\\d+)\\s(\\d+)\\s(\\d+)\\s(\\d+)\\s\\?\\s(\\d+)$");

    /**
     * 将日期时间字符串转换为Cron表达式
     *
     * @param dateTimeStr 日期时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
     * @return Cron表达式
     * @throws ParseException 如果日期格式不正确
     */
    public static String dateTimeToCron(String dateTimeStr) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = sdf.parse(dateTimeStr);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int second = calendar.get(Calendar.SECOND);
        int minute = calendar.get(Calendar.MINUTE);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        int month = calendar.get(Calendar.MONTH) + 1;
        int year = calendar.get(Calendar.YEAR);

        return String.format("%d %d %d %d %d ? %d",
                second, minute, hour, day, month, year);
    }

    /**
     * 将Cron表达式转换为日期时间字符串
     *
     * @param cronExpression Cron表达式，格式为"秒 分 时 日 月 ? 年"
     * @return 日期时间字符串，格式为"yyyy-MM-dd HH:mm:ss"
     * @throws IllegalArgumentException 如果Cron表达式格式不正确
     */
    public static String cronToDateTime(String cronExpression) throws IllegalArgumentException {
        Matcher matcher = CRON_EXPRESSION.matcher(cronExpression.trim());

        if (!matcher.matches()) {
            throw new IllegalArgumentException("Invalid cron expression format. Expected format: 'sec min hour day month ? year'");
        }

        try {
            // 解析Cron表达式各部分
            int second = Integer.parseInt(matcher.group(1));
            int minute = Integer.parseInt(matcher.group(2));
            int hour = Integer.parseInt(matcher.group(3));
            int day = Integer.parseInt(matcher.group(4));
            int month = Integer.parseInt(matcher.group(5)) - 1; // Calendar月份从0开始
            int year = Integer.parseInt(matcher.group(6));

            // 验证日期时间有效性
            Calendar calendar = Calendar.getInstance();
            calendar.setLenient(false); // 严格模式，自动验证日期有效性
            calendar.set(year, month, day, hour, minute, second);

            // 格式化日期时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(calendar.getTime());

        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Cron expression contains invalid numbers", e);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid date/time values in cron expression", e);
        }
    }

}
