package com.espc.sec.dataexport.elasticsearch.task;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.elasticsearch.service.ElasticsearchExportServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 导出定时任务
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Component
public class EsScheduler {

    @Autowired
    private ElasticsearchExportServiceImpl exportService;


    /**
     * 定时导出任务
     * 使用配置文件中的cron表达式
     */
    @Scheduled(cron = "#{T(com.espc.sec.dataexport.common.config.Config).exportConfig.elasticsearch.getCron()}")
    public void incrementTask() {
        if (!Config.exportConfig.elasticsearch.getIncrementEnable()) {
            return;
        }
        log.info("定时导出任务开始执行");

        try {
            exportService.incrementExport();
            log.info("定时导出任务执行完成");
        } catch (Exception e) {
            log.error("定时导出任务执行失败", e);
        }
    }

}