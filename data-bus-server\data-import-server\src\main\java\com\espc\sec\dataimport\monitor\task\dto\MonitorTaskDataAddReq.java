package com.espc.sec.dataimport.monitor.task.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 监控任务数据新增请求对象
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
public class MonitorTaskDataAddReq {

    /**
     * 节点
     */
    @NotBlank(message = "节点不能为空")
    private String node;

    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    private String taskName;

    /**
     * 数据库类型
     */
    @NotBlank(message = "数据库类型不能为空")
    private String databaseType;

    /**
     * 表名称
     */
    @NotBlank(message = "表名称不能为空")
    private String tableName;

    /**
     * 导出或者导入条数
     */
    @NotNull(message = "数据条数不能为空")
    private Long size;

    /**
     * 导出或者导入时间
     */
    private String time;


    /**
     * 文件名
     */
    @NotBlank(message = "文件名不能为空")
    private String fileName;
}