package com.espc.sec.dataexport.common.exception;


import com.espc.sec.dataexport.common.entity.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @date 2023/2/14
 **/
@Slf4j
@RestControllerAdvice
public class BigDataxceptionHandler {

    /**
     * 处理自定义异常
     */
    @ExceptionHandler(BigDataException.class)
    public Result handleMemuException(BigDataException e) {
        Result result = new Result();
        result.put("code", e.getCode());
        result.put("msg", e.getMessage());
        return result;
    }

    @ExceptionHandler({Exception.class})
    public Result handleException(Exception e) {
        log.error("服务器繁忙:", e);
        return Result.error(e.getMessage());
    }
}
