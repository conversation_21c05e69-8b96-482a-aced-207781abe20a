package com.espc.sec.dataexport.kafka.service;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.dto.task.KafkaTaskProperties;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import com.espc.sec.dataexport.common.service.IncrementExportService;
import com.espc.sec.dataexport.common.service.TaskExportService;
import com.espc.sec.dataexport.common.util.MultiThreadUtil;
import com.espc.sec.dataexport.kafka.dto.KafkaExportDto;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.Callable;

/**
 * Kafka导出服务主类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@Service
public class KafkaExportServiceImpl implements IncrementExportService, TaskExportService<KafkaTaskProperties> {
    @Autowired
    private KafkaExporter kafkaExporter;

    /**
     * 手动导出数据
     *
     * @return 导出结果
     */
    @Override
    public void incrementExport() throws Exception {
        List<String> topicList = Config.exportConfig.kafka.getTopicName();
        if (null == topicList || topicList.isEmpty()) {
            log.error("kafka导出topic配置异常");
            return;
        }
        KafkaExportDto kafkaExportDto = new KafkaExportDto();
        kafkaExportDto.setExportModeEnum(ExportModeEnum.INCREMENT);
        kafkaExportDto.setExportTypeEnum(ExportTypeEnum.KAFKA);
        kafkaExportDto.setTopicNames(topicList);
        kafkaExporter.export(kafkaExportDto);
    }


    /**
     * 根据参数导出数据（新增接口支持）
     *
     * @param request 导出请求参数
     * @return 导出结果
     */
    @Override
    public void taskExport(KafkaTaskProperties request) {
        if (request.getTopicName().isEmpty()) {
            log.warn("没有配置任何主题");
            return;
        }

        int dataSize = request.getDataSize();
        List<Callable<Void>> callableList = Lists.newArrayList();
        for (String topicName : request.getTopicName()) {
            callableList.add(() -> exportSingleTopicWithParams(topicName, dataSize,request.getTaskId()));
        }
        MultiThreadUtil.executeAll(callableList);
    }


    /**
     * 导出单个主题的数据（参数化版本）
     *
     * @param topicName 主题名称
     * @param dataSize  数据条数
     * @param taskId
     * @return 导出结果
     */
    private Void exportSingleTopicWithParams(String topicName, int dataSize, Integer taskId) throws Exception {
        KafkaExportDto kafkaExportDto = new KafkaExportDto();
        kafkaExportDto.setExportModeEnum(ExportModeEnum.TASK);
        kafkaExportDto.setExportTypeEnum(ExportTypeEnum.KAFKA);
        kafkaExportDto.setSize(dataSize);
        kafkaExportDto.setTaskId(taskId);
        kafkaExportDto.setTopicNames(Lists.newArrayList(topicName));
        kafkaExporter.export(kafkaExportDto);
        return null;
    }


    /**
     * 定时导出任务
     * 注意：Kafka不需要增量导出，每次都是消费新消息
     */
    @Scheduled(cron = "#{T(com.espc.sec.dataexport.common.config.Config).exportConfig.kafka.getCron()}")
    public void scheduledExport() {
        if (!Config.exportConfig.kafka.getIncrementEnable()) {
            return;
        }
        try {
            incrementExport();
        } catch (Exception e) {
            log.error("kafka导出异常", e);
        }
    }
}