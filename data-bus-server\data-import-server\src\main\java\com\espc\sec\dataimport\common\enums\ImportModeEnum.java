package com.espc.sec.dataimport.common.enums;

import com.espc.sec.dataimport.common.util.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 导入模式
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ImportModeEnum {
    /**
     * 增量
     */
    increment(1, "增量"),
    /**
     * 任务型
     */
    task(2, "任务型"),
    ;
    private final Integer code;
    private final String name;

    public static ImportModeEnum getByCode(Integer code) {
        return StreamUtil.getStream(values())
                .filter(r -> r.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }
}
