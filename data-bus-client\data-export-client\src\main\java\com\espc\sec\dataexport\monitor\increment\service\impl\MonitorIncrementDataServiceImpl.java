package com.espc.sec.dataexport.monitor.increment.service.impl;

import com.espc.sec.dataexport.common.util.PageHelperUtil;
import com.espc.sec.dataexport.common.vo.PageVo;
import com.espc.sec.dataexport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataexport.monitor.increment.dto.MonitorIncrementDataReq;
import com.espc.sec.dataexport.monitor.increment.entity.MonitorIncrementDataPo;
import com.espc.sec.dataexport.monitor.increment.mapper.MonitorIncrementDataMapper;
import com.espc.sec.dataexport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataexport.monitor.increment.vo.MonitorIncrementDataGroupVo;
import com.espc.sec.dataexport.monitor.increment.vo.MonitorIncrementDataVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
/**
 * @Author: zh
 * @date: 2025/7/25
 *  * 增量导入导出监控服务类
 */
@Slf4j
@Service
public class MonitorIncrementDataServiceImpl implements MonitorIncrementDataService {

    @Autowired
    private MonitorIncrementDataMapper monitorIncrementDataMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer create(MonitorIncrementDataDto dto) {
        dto.check();

        MonitorIncrementDataPo incrementDataPo = new MonitorIncrementDataPo();
        BeanUtils.copyProperties(dto, incrementDataPo);
        incrementDataPo.setCreateTime(new Date());

        monitorIncrementDataMapper.insert(incrementDataPo);

        return incrementDataPo.getId();
    }

    @Override
    public PageVo<MonitorIncrementDataGroupVo> group(MonitorIncrementDataReq req) {
        PageVo<MonitorIncrementDataGroupVo> pageList = PageHelperUtil.startPage(req, () -> monitorIncrementDataMapper.groupQuery(req));
        return new PageVo<>(req.getPageNo(), req.getPageSize(), pageList.getTotal(), pageList.getList());
    }

    @Override
    public PageVo<MonitorIncrementDataVo> page(MonitorIncrementDataReq req) {
        PageVo<MonitorIncrementDataPo> pageList = PageHelperUtil.startPage(req, () -> monitorIncrementDataMapper.selectByCondition(req));
        List<MonitorIncrementDataVo> monitorIncrementDataList = pageList.getList().stream().map(MonitorIncrementDataVo::new ).collect(Collectors.toList());
        return new PageVo<>(req.getPageNo(), req.getPageSize(), pageList.getTotal(), monitorIncrementDataList);

    }

}
