package com.espc.sec.dataimport.elasticsearch.controller;

import com.espc.sec.dataimport.common.enums.ImportTypeEnum;
import com.espc.sec.dataimport.elasticsearch.service.ElasticsearchImporter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 导入控制器
 * 提供手动触发导入和管理接口
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/import")
public class ImportController {

    @Autowired
    private ElasticsearchImporter elasticsearchImporter;

    /**
     * 手动触发导入
     */
    @PostMapping("/trigger")
    public Map<String, Object> triggerImport() {
        Map<String, Object> result = new HashMap<>();
        try {
            log.info("手动触发导入任务");
            elasticsearchImporter.import0(ImportTypeEnum.ES);

            result.put("success", true);
            result.put("message", "导入任务执行成功");
        } catch (Exception e) {
            log.error("手动触发导入失败", e);
            result.put("success", false);
            result.put("message", "导入任务执行失败: " + e.getMessage());
        }

        return result;
    }
}