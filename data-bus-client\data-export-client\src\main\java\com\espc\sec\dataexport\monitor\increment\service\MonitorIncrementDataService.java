package com.espc.sec.dataexport.monitor.increment.service;

import com.espc.sec.dataexport.common.vo.PageVo;
import com.espc.sec.dataexport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataexport.monitor.increment.dto.MonitorIncrementDataReq;
import com.espc.sec.dataexport.monitor.increment.vo.MonitorIncrementDataGroupVo;
import com.espc.sec.dataexport.monitor.increment.vo.MonitorIncrementDataVo;

/**
 * @Author: zh
 * @date: 2025/7/25
 */
public interface MonitorIncrementDataService {
    /**
     * 分页
     */
    PageVo<MonitorIncrementDataVo> page(MonitorIncrementDataReq monitorIncrementDataReq);

    /**
     * 插入
     */
    Integer create(MonitorIncrementDataDto dto);
    /**
     * 分组
     */
    PageVo<MonitorIncrementDataGroupVo> group(MonitorIncrementDataReq dto);
}
