package com.espc.sec.dataexport.minio.entity;

import com.espc.sec.dataexport.common.constant.Constants;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@Data
@Builder
public class MinioMetaData {


    private String objectName;

    private Map<String, String> userMetadata;


    /**
     * 文件创建时间 yyyyMMddHHmmssSSS source: userMetadata
     */
    private String fileCreateTime;

    /**
     * 自定义参数：文件创建时间 ，来源于 userMetadata
     *
     * @return 文件创建时间
     */
    public String getFileCreateTime() {
        if (userMetadata == null || userMetadata.isEmpty()) {
            return StringUtils.EMPTY;
        }
        if (StringUtils.isNoneBlank(userMetadata.get(Constants.MINIO_METADATA_SEPARATOR + Constants.MINIO_USRMETADATA_CREATE_TIME))) {
            return userMetadata.get(Constants.MINIO_METADATA_SEPARATOR + Constants.MINIO_USRMETADATA_CREATE_TIME);
        }
        return StringUtils.EMPTY;
    }
}
