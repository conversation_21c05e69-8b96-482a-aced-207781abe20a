server:
  port: 5117

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 日志配置
logging:
  level:
    com.espc.sec: DEBUG
    com.starrocks: DEBUG
    org.elasticsearch: INFO
    org.springframework.jdbc: DEBUG
    org.springframework: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    #    name: logs/starrocks-export.log
    name: logs/elasticsearch-export.log
    max-size: 100MB
    max-history: 30


