package com.espc.sec.dataimport.task.mapper;


import com.espc.sec.dataimport.task.dto.DataTaskReq;
import com.espc.sec.dataimport.task.entity.DataTaskPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据任务Mapper接口
 */
public interface DataTaskMapper {

    List<DataTaskPo> selectByCondition(DataTaskReq dataTaskReq);

    DataTaskPo selectByName(@Param("name") String name);

    DataTaskPo selectByNameNotId(@Param("name") String name, @Param("id") Integer id);

    int insert(DataTaskPo dataTask);

    int updateById(DataTaskPo dataTask);

    int updateStatus(@Param("id") Integer id, @Param("status") Integer status);

    int deleteById(Integer id);

    DataTaskPo getById(@Param("id") Integer id);
}
