package com.espc.sec.dataexport.common.controller;

import com.espc.sec.dataexport.common.vo.ResultVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/system")
@Api(tags = "系统相关接口")
public class SystemController {


    @ApiOperation("是否是总中心")
    @GetMapping("/isMainCenter")
    public ResultVo<Boolean> scanPackageEnums() {
        return ResultVo.success(false);
    }
}
