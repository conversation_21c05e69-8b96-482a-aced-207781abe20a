package com.espc.sec.flow.util;

import Ice.Communicator;
import lombok.extern.slf4j.Slf4j;


/**
 * Created by JAY on 2016/3/30.
 */
@Slf4j
public class IceUtil {



    public static void destroyCommunicator(Communicator communicator) {
        if (null != communicator) {
            try {
                communicator.shutdown();
                communicator.destroy();
            } catch (Exception e) {
                log.error("error when destroy communicator", e);
            }
        }
    }

    public static void log(Communicator communicator) {
        log.debug(communicator.getProperties().getPropertiesForPrefix("Ice").toString());
    }

    public static synchronized Communicator getCommunicator() {
        Communicator communicator = null;
        try {
            String[] args = new String[]{"--Ice.Config=conf/FileFlowServer/conf/ice.config"};
            communicator = Ice.Util.initialize(args);
            log.debug(communicator.getProperties().getPropertiesForPrefix("Ice").toString());
       } catch (Exception e) {
            destroyCommunicator(communicator);
            log.error("error when getCommunicator", e);
            throw e;
        }
        return communicator;
    }



}
