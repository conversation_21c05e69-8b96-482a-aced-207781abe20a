package com.espc.sec.dataexport.job.util;

import com.espc.sec.dataexport.job.dto.JobInfo;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class JobManager {

    private final Scheduler scheduler;

    @Autowired
    public JobManager(Scheduler scheduler) {
        this.scheduler = scheduler;
    }

    /**
     * 创建定时任务
     */
    public void createJob(JobInfo jobInfo) throws Exception {
        // 构建JobDetail
        JobDetail jobDetail = JobBuilder.newJob(getJobClass(jobInfo.getJobClassName()))
                .withIdentity(jobInfo.getJobName(), jobInfo.getJobGroup())
                .withDescription(jobInfo.getDescription())
                .build();

        // 设置Job参数
        if (jobInfo.getJobData() != null && !jobInfo.getJobData().isEmpty()) {
            jobDetail.getJobDataMap().putAll(jobInfo.getJobData());
        }

        // 构建Trigger
        Trigger trigger = TriggerBuilder.newTrigger()
                .withIdentity(jobInfo.getJobName(), jobInfo.getJobGroup())
                .withSchedule(CronScheduleBuilder.cronSchedule(jobInfo.getCronExpression()))
                .build();

        // 调度任务
        scheduler.scheduleJob(jobDetail, trigger);
    }

    /**
     * 暂停任务
     */
    public void pauseJob(String jobName, String jobGroup) throws SchedulerException {
        scheduler.pauseJob(JobKey.jobKey(jobName, jobGroup));
    }

    /**
     * 恢复任务
     */
    public void resumeJob(String jobName, String jobGroup) throws SchedulerException {
        scheduler.resumeJob(JobKey.jobKey(jobName, jobGroup));
    }

    /**
     * 更新任务
     */
    public void updateJob(JobInfo jobInfo) throws Exception {
        deleteJob(jobInfo.getJobName(), jobInfo.getJobGroup());
        createJob(jobInfo);
    }

    /**
     * 删除任务
     */
    public void deleteJob(String jobName, String jobGroup) throws SchedulerException {
        scheduler.deleteJob(JobKey.jobKey(jobName, jobGroup));
    }

    /**
     * 获取任务状态
     */
    public String getJobState(String jobName, String jobGroup) throws SchedulerException {
        TriggerKey triggerKey = TriggerKey.triggerKey(jobName, jobGroup);
        return scheduler.getTriggerState(triggerKey).name();
    }

    /**
     * 获取所有任务
     */
    public List<JobInfo> getAllJobs() throws SchedulerException {
        List<JobInfo> jobInfos = new ArrayList<>();
        for (String groupName : scheduler.getJobGroupNames()) {
            for (JobKey jobKey : scheduler.getJobKeys(GroupMatcher.jobGroupEquals(groupName))) {
                JobInfo jobInfo = new JobInfo();
                jobInfo.setJobName(jobKey.getName());
                jobInfo.setJobGroup(jobKey.getGroup());

                JobDetail jobDetail = scheduler.getJobDetail(jobKey);
                jobInfo.setDescription(jobDetail.getDescription());
                jobInfo.setJobClassName(jobDetail.getJobClass().getName());
                jobInfo.setJobData(jobDetail.getJobDataMap());

                List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
                if (!triggers.isEmpty()) {
                    Trigger trigger = triggers.get(0);
                    if (trigger instanceof CronTrigger) {
                        jobInfo.setCronExpression(((CronTrigger) trigger).getCronExpression());
                    }
                }

                jobInfos.add(jobInfo);
            }
        }
        return jobInfos;
    }

    /**
     * 根据类名获取Job类
     */
    @SuppressWarnings("unchecked")
    private Class<? extends Job> getJobClass(String className) throws ClassNotFoundException {
        return (Class<? extends Job>) Class.forName(className);
    }
}
