package com.espc.sec.dataexport.starrocks.task;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.starrocks.service.StarRocksExportServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * @Author: zh
 * @date: 2025/7/29
 * StarRocks导出服务主类
 */
@Slf4j
@Service
public class StarrocksScheduler {
    @Autowired
    private StarRocksExportServiceImpl starRocksExportServiceImpl;

    @Scheduled(cron = "#{T(com.espc.sec.dataexport.common.config.Config).exportConfig.starRocks.getCron()}")
    public void scheduledIncrementalExport() {
        if (!Config.exportConfig.starRocks.getIncrementEnable()) {
            return;
        }
        log.info("{}定时任务开始", LogKeyword.STAR_ROCKS_EXPORT);
        starRocksExportServiceImpl.incrementExport();
        log.info("{}定时任务结束", LogKeyword.STAR_ROCKS_EXPORT);
    }
}
