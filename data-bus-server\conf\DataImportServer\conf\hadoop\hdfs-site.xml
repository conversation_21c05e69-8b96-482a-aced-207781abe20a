<configuration>

    <property>
        <name>dfs.block.access.token.enable</name>
        <value>true</value>
    </property>

    <property>
        <name>dfs.blockreport.initialDelay</name>
        <value>120</value>
    </property>

    <property>
        <name>dfs.blocksize</name>
        <value>134217728</value>
    </property>

    <property>
        <name>dfs.client.failover.proxy.provider.nnCluster</name>
        <value>org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider</value>
    </property>

    <property>
        <name>dfs.client.read.shortcircuit</name>
        <value>true</value>
    </property>

    <property>
        <name>dfs.client.read.shortcircuit.streams.cache.size</name>
        <value>4096</value>
    </property>

    <property>
        <name>dfs.client.retry.policy.enabled</name>
        <value>false</value>
    </property>

    <property>
        <name>dfs.cluster.administrators</name>
        <value>hdfs</value>
    </property>

    <property>
        <name>dfs.content-summary.limit</name>
        <value>5000</value>
    </property>

    <property>
        <name>dfs.datanode.address</name>
        <value>0.0.0.0:50010</value>
    </property>

    <property>
        <name>dfs.datanode.balance.bandwidthPerSec</name>
        <value>6250000</value>
    </property>

    <property>
        <name>dfs.datanode.data.dir</name>
        <value>/data/hdfs/data</value>
        <final>true</final>
    </property>

    <property>
        <name>dfs.datanode.data.dir.perm</name>
        <value>750</value>
    </property>

    <property>
        <name>dfs.datanode.du.reserved</name>
        <value>78050112512</value>
    </property>

    <property>
        <name>dfs.datanode.failed.volumes.tolerated</name>
        <value>0</value>
        <final>true</final>
    </property>

    <property>
        <name>dfs.datanode.http.address</name>
        <value>0.0.0.0:50075</value>
    </property>

    <property>
        <name>dfs.datanode.https.address</name>
        <value>0.0.0.0:50475</value>
    </property>

    <property>
        <name>dfs.datanode.ipc.address</name>
        <value>0.0.0.0:8010</value>
    </property>

    <property>
        <name>dfs.datanode.max.transfer.threads</name>
        <value>16384</value>
    </property>

    <property>
        <name>dfs.domain.socket.path</name>
        <value>/var/lib/hadoop-hdfs/dn_socket</value>
    </property>

    <property>
        <name>dfs.encrypt.data.transfer.cipher.suites</name>
        <value>AES/CTR/NoPadding</value>
    </property>

    <property>
        <name>dfs.ha.automatic-failover.enabled</name>
        <value>true</value>
    </property>

    <property>
        <name>dfs.ha.fencing.methods</name>
        <value>shell(/bin/true)</value>
    </property>

    <property>
        <name>dfs.ha.namenodes.nnCluster</name>
        <value>nn1,nn2</value>
    </property>

    <property>
        <name>dfs.heartbeat.interval</name>
        <value>3</value>
    </property>

    <property>
        <name>dfs.hosts.exclude</name>
        <value>/etc/hadoop/conf/dfs.exclude</value>
    </property>

    <property>
        <name>dfs.http.policy</name>
        <value>HTTP_ONLY</value>
    </property>

    <property>
        <name>dfs.https.port</name>
        <value>50470</value>
    </property>

    <property>
        <name>dfs.internal.nameservices</name>
        <value>nnCluster</value>
    </property>

    <property>
        <name>dfs.journalnode.edits.dir</name>
        <value>/data/hdfs/journal</value>
    </property>

    <property>
        <name>dfs.journalnode.http-address</name>
        <value>0.0.0.0:8480</value>
    </property>

    <property>
        <name>dfs.journalnode.https-address</name>
        <value>0.0.0.0:8481</value>
    </property>

    <property>
        <name>dfs.namenode.accesstime.precision</name>
        <value>0</value>
    </property>

    <property>
        <name>dfs.namenode.acls.enabled</name>
        <value>true</value>
    </property>

    <property>
        <name>dfs.namenode.audit.log.async</name>
        <value>true</value>
    </property>

    <property>
        <name>dfs.namenode.avoid.read.stale.datanode</name>
        <value>true</value>
    </property>

    <property>
        <name>dfs.namenode.avoid.write.stale.datanode</name>
        <value>true</value>
    </property>

    <property>
        <name>dfs.namenode.checkpoint.dir</name>
        <value>/data/hdfs/namesecondary</value>
    </property>

    <property>
        <name>dfs.namenode.checkpoint.edits.dir</name>
        <value>${dfs.namenode.checkpoint.dir}</value>
    </property>

    <property>
        <name>dfs.namenode.checkpoint.period</name>
        <value>21600</value>
    </property>

    <property>
        <name>dfs.namenode.checkpoint.txns</name>
        <value>1000000</value>
    </property>

    <property>
        <name>dfs.namenode.fslock.fair</name>
        <value>false</value>
    </property>

    <property>
        <name>dfs.namenode.handler.count</name>
        <value>400</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.nnCluster.nn1</name>
        <value>cie1:50070</value>
    </property>

    <property>
        <name>dfs.namenode.http-address.nnCluster.nn2</name>
        <value>cie2:50070</value>
    </property>

    <property>
        <name>dfs.namenode.https-address.nnCluster.nn1</name>
        <value>cie1:50470</value>
    </property>

    <property>
        <name>dfs.namenode.https-address.nnCluster.nn2</name>
        <value>cie2:50470</value>
    </property>

    <property>
        <name>dfs.namenode.name.dir</name>
        <value>/data/hdfs/namenode</value>
        <final>true</final>
    </property>

    <property>
        <name>dfs.namenode.name.dir.restore</name>
        <value>true</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.nnCluster.nn1</name>
        <value>cie1:8020</value>
    </property>

    <property>
        <name>dfs.namenode.rpc-address.nnCluster.nn2</name>
        <value>cie2:8020</value>
    </property>

    <property>
        <name>dfs.namenode.safemode.threshold-pct</name>
        <value>0.99</value>
    </property>

    <property>
        <name>dfs.namenode.shared.edits.dir</name>
        <value>qjournal://cie3:8485;cie1:8485;cie2:8485/nnCluster</value>
    </property>

    <property>
        <name>dfs.namenode.stale.datanode.interval</name>
        <value>30000</value>
    </property>

    <property>
        <name>dfs.namenode.startup.delay.block.deletion.sec</name>
        <value>3600</value>
    </property>

    <property>
        <name>dfs.namenode.write.stale.datanode.ratio</name>
        <value>1.0f</value>
    </property>

    <property>
        <name>dfs.nameservices</name>
        <value>nnCluster</value>
    </property>

    <property>
        <name>dfs.permissions.ContentSummary.subAccess</name>
        <value>false</value>
    </property>

    <property>
        <name>dfs.permissions.enabled</name>
        <value>true</value>
    </property>

    <property>
        <name>dfs.permissions.superusergroup</name>
        <value>hdfs</value>
    </property>

    <property>
        <name>dfs.replication</name>
        <value>3</value>
    </property>

    <property>
        <name>dfs.replication.max</name>
        <value>50</value>
    </property>

    <property>
        <name>dfs.webhdfs.enabled</name>
        <value>true</value>
        <final>true</final>
    </property>

    <property>
        <name>fs.permissions.umask-mode</name>
        <value>022</value>
    </property>

    <property>
        <name>hadoop.caller.context.enabled</name>
        <value>true</value>
    </property>

    <property>
        <name>manage.include.files</name>
        <value>false</value>
    </property>

    <property>
        <name>nfs.exports.allowed.hosts</name>
        <value>* rw</value>
    </property>

    <property>
        <name>nfs.file.dump.dir</name>
        <value>/tmp/.hdfs-nfs</value>
    </property>

</configuration>