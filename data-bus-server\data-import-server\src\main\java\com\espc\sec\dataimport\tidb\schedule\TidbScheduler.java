package com.espc.sec.dataimport.tidb.schedule;

import com.espc.sec.dataimport.common.constant.LogKeyword;
import com.espc.sec.dataimport.common.enums.ImportTypeEnum;
import com.espc.sec.dataimport.tidb.service.TidbImporter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * TiDB导入定时任务
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Slf4j
@Service
public class TidbScheduler {

    @Autowired
    private TidbImporter tidbImporter;

    /**
     * TiDB导入定时任务
     */
    @Scheduled(fixedDelayString = "#{T(com.espc.sec.dataimport.common.config.Config).importConfig.tidb.getFixedDelayMills()}")
    public void run() {
        log.info(LogKeyword.TIDB_IMPORT + "定时任务开���");
        tidbImporter.import0(ImportTypeEnum.TIDB);
        log.info(LogKeyword.TIDB_IMPORT + "定时任务结束");
    }
}