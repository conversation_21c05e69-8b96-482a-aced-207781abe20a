package com.espc.sec.flow.util;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2016/11/3
 */
@Slf4j
public final class DataTransUtil {

    /**
     * 转化成字符串形式的字节数
     *
     * @param value 字节数
     * @return 与字节数等价的字符串形式，形如 7.2G
     */
    public static String toBytesStrFormat(long value) {
        if (value <= 0) {
            return value + "B";
        }
        float k = 1024.0f;
        float result = value / k;
        //此时result的单位是K
        if (result < 1) {
            return value + "B";
        } else {
            //此时result的单位是K
            if (result < k) {
                //四舍五入到小数点后两位
                return (float) (Math.round(result * 100)) / 100 + "K";
            } else {
                result = result / k;
                //此时result的单位是M
                if (result < k) {
                    return (float) (Math.round(result * 100)) / 100 + "M";
                } else {
                    result = result / k;
                    //此时result的单位是G
                    if (result < k) {
                        return (float) (Math.round(result * 100)) / 100 + "G";
                    } else {
                        result = result / k;
                        //此时result的单位是T
                        if (result < k) {
                            return (float) (Math.round(result * 100)) / 100 + "T";
                        } else {
                            result = result / k;
                            //此时result的单位是P
                            return (float) (Math.round(result * 100)) / 100 + "P";
                        }
                    }
                }
            }
        }
    }


    /**
     * 转化成字符串列表
     *
     * @param set 字符串集
     * @return 字符串列表
     */
    public static List<String> toStrList(Set<String> set) {
        List<String> list = new ArrayList<>(set.size());
        for (String value : set) {
            list.add(value);
        }
        return list;
    }


    /**
     * 转化为十进制字符串格式
     *
     * @param value 数值
     * @return 十进制字符串
     */
    public static String toDecStrFormat(long value) {
        if (value > 100000000) {
            return (float) (Math.round(value / 100000000.00f * 100)) / 100 + "亿";
        } else if (value > 10000) {
            return (float) (Math.round(value / 10000.00f * 100)) / 100 + "万";
        } else {
            return "" + value;
        }
    }

}
