package com.espc.sec.flow.conf;

import lombok.Data;
import lombok.Delegate;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019-04-18
 */
@Data
public class Config {
    /**
     * 源目录
     */
    private Set<String> sourceDir;
    /**
     * 存放6005类型文件的目录-弃用
     */
    @Delegate
    private String sourceHJDir;
    /**
     * 临时目录
     */
    private String tempDir;
    /**
     * 源目录根路径下的文件是否要移动
     */
    private boolean clientBaseDirSwitch;
    /**
     * 任务队列最大长度
     */
    private int fileQueueSize;
    /**
     * 允许分流的最大文件大小(M)
     */
    private int maxFileSize;
    /**
     * 线程池的总大小
     */
    private int threadPoolSize;
    /**
     * 需要优先入库的源目录
     */
    private Set<String> highPriorityDirs;
    /**
     * 需要优先入库的节点
     */
    private Set<String> highPriorityNodes;
    /**
     * 需要优先入库的文件类型
     */
    private Set<String> highPriorityTypes;
    /**
     * 向本机移动的服务端配置
     */
    private List<ThisMachineServersBean> thisMachineServers;
    /**
     * 向分流服务端移动的配置
     */
    private List<FlowServersBean> flowServers;

    @Data
    public static class ThisMachineServersBean {
        /**
         * 文件移动类型（move、copy）
         */
        private String moveType;
        /**
         * 本机移动的目标目录
         */
        private String target;
        /**
         * 分流的源目录
         */
        private Set<String> dirs;
        /**
         * 分流的节点
         */
        private Set<String> nodes;
        /**
         * 分流的文件类型
         */
        private Set<String> types;
        /**
         * 非标准格式的文件名是否需要分流
         */
        private boolean needSpecialFile;
    }

    @Data
    public static class FlowServersBean {
        /**
         * 服务端的ip
         */
        private String ip;
        /**
         * 服务端程序的端口
         */
        private int port;
        /**
         * 是否需要压缩
         */
        private boolean needZip;
        /**
         * 压缩阈值(M)(目录文件累计达到该值后开始压缩)
         */
        private int zipFileMaxSize;
        /**
         * 分流的源目录
         */
        private Set<String> dirs;
        /**
         * 分流的节点
         */
        private Set<String> nodes;
        /**
         * 分流的文件类型
         */
        private Set<String> types;
        /**
         * 非标准格式的文件名是否需要分流
         */
        private boolean needSpecialFile;
    }

}
