<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="data-import-server" />
        <module name="file-flow-server" />
        <module name="data-bus-start" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="data-bus-start" options="-parameters -extdirs D:\code\v1.1.4\data-bus\data-bus-server\data-bus-start/target/java-libs" />
      <module name="data-import-server" options="-parameters" />
      <module name="file-flow-server" options="-parameters" />
    </option>
  </component>
</project>