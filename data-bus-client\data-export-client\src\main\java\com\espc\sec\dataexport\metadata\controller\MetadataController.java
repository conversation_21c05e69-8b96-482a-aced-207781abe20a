package com.espc.sec.dataexport.metadata.controller;

import com.espc.sec.dataexport.common.vo.PageVo;
import com.espc.sec.dataexport.common.vo.ResultVo;
import com.espc.sec.dataexport.metadata.dto.MetadataPageReq;
import com.espc.sec.dataexport.metadata.enums.DataSourceType;
import com.espc.sec.dataexport.metadata.service.MetadataService;
import com.espc.sec.dataexport.metadata.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 元数据查询控制器
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@RestController
@RequestMapping("/api/metadata")
@Api(tags = "元数据查询管理")
public class MetadataController {

    @Autowired
    private MetadataService metadataService;

    @GetMapping("/datasource-types")
    @ApiOperation("获取数据源类型列表")
    public ResultVo<List<String>> getDataSourceTypes() {
        List<String> types = Arrays.stream(DataSourceType.values())
                .map(DataSourceType::getCode)
                .collect(Collectors.toList());
        return ResultVo.success(types);
    }

    // ==================== Kafka相关接口 ====================

    @PostMapping("/kafka/topics")
    @ApiOperation("获取Kafka主题列表")
    public ResultVo<PageVo<TopicInfoVo>> getKafkaTopics(@RequestBody MetadataPageReq req) {
        try {
            PageVo<TopicInfoVo> result = metadataService.getKafkaTopics(req);
            return ResultVo.success(result);
        } catch (Exception e) {
            log.error("获取Kafka主题列表失败", e);
            return ResultVo.fail(500, "获取Kafka主题列表失败: " + e.getMessage());
        }
    }

    // ==================== Elasticsearch相关接口 ====================

    @PostMapping("/elasticsearch/indices")
    @ApiOperation("获取Elasticsearch索引列表")
    public ResultVo<PageVo<IndexInfoVo>> getElasticsearchIndices(@RequestBody MetadataPageReq req) {
        try {
            PageVo<IndexInfoVo> result = metadataService.getElasticsearchIndices(req);
            return ResultVo.success(result);
        } catch (Exception e) {
            log.error("获取Elasticsearch索引列表失败", e);
            return ResultVo.fail(500, "获取Elasticsearch索引列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/elasticsearch/fields")
    @ApiOperation("获取Elasticsearch索引字段列表")
    public ResultVo<PageVo<IndexFieldInfoVo>> getElasticsearchFields(@RequestBody MetadataPageReq req) {
        try {
            if (req.getIndexName() == null || req.getIndexName().trim().isEmpty()) {
                return ResultVo.fail(400, "索引名称不能为空");
            }
            PageVo<IndexFieldInfoVo> result = metadataService.getElasticsearchFields(req);
            return ResultVo.success(result);
        } catch (Exception e) {
            log.error("获取Elasticsearch字段列表失败", e);
            return ResultVo.fail(500, "获取Elasticsearch字段列表失败: " + e.getMessage());
        }
    }

    // ==================== StarRocks相��接口 ====================

    @PostMapping("/starrocks/databases")
    @ApiOperation("获取StarRocks数据库列表")
    public ResultVo<PageVo<DatabaseInfoVo>> getStarRocksDatabases(@RequestBody MetadataPageReq req) {
        try {
            PageVo<DatabaseInfoVo> result = metadataService.getStarRocksDatabases(req);
            return ResultVo.success(result);
        } catch (Exception e) {
            log.error("获取StarRocks数据库列表失败", e);
            return ResultVo.fail(500, "获取StarRocks数据库列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/starrocks/tables")
    @ApiOperation("获取StarRocks表列表")
    public ResultVo<PageVo<TableInfoVo>> getStarRocksTables(@RequestBody MetadataPageReq req) {
        try {
            if (req.getDatabaseName() == null || req.getDatabaseName().trim().isEmpty()) {
                return ResultVo.fail(400, "数据库名称不能为空");
            }
            PageVo<TableInfoVo> result = metadataService.getStarRocksTables(req);
            return ResultVo.success(result);
        } catch (Exception e) {
            log.error("获取StarRocks表列表失败", e);
            return ResultVo.fail(500, "获取StarRocks表列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/starrocks/fields")
    @ApiOperation("获取StarRocks字段列表")
    public ResultVo<PageVo<FieldInfoVo>> getStarRocksFields(@RequestBody MetadataPageReq req) {
        try {
            if (req.getDatabaseName() == null || req.getDatabaseName().trim().isEmpty()) {
                return ResultVo.fail(400, "数据库名称不能为空");
            }
            if (req.getTableName() == null || req.getTableName().trim().isEmpty()) {
                return ResultVo.fail(400, "表名称不能为空");
            }
            PageVo<FieldInfoVo> result = metadataService.getStarRocksFields(req);
            return ResultVo.success(result);
        } catch (Exception e) {
            log.error("获取StarRocks字段列表失败", e);
            return ResultVo.fail(500, "获取StarRocks字段列表失败: " + e.getMessage());
        }
    }

    // ==================== TiDB相关接口 ====================

    @PostMapping("/tidb/databases")
    @ApiOperation("获取TiDB数据库列表")
    public ResultVo<PageVo<DatabaseInfoVo>> getTidbDatabases(@RequestBody MetadataPageReq req) {
        try {
            PageVo<DatabaseInfoVo> result = metadataService.getTidbDatabases(req);
            return ResultVo.success(result);
        } catch (Exception e) {
            log.error("获取TiDB数据库列表失败", e);
            return ResultVo.fail(500, "获取TiDB数据库列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/tidb/tables")
    @ApiOperation("获取TiDB表列表")
    public ResultVo<PageVo<TableInfoVo>> getTidbTables(@RequestBody MetadataPageReq req) {
        try {
            if (req.getDatabaseName() == null || req.getDatabaseName().trim().isEmpty()) {
                return ResultVo.fail(400, "数据库名称不能为空");
            }
            PageVo<TableInfoVo> result = metadataService.getTidbTables(req);
            return ResultVo.success(result);
        } catch (Exception e) {
            log.error("获取TiDB表列表失败", e);
            return ResultVo.fail(500, "获取TiDB表列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/tidb/fields")
    @ApiOperation("获取TiDB字段列表")
    public ResultVo<PageVo<FieldInfoVo>> getTidbFields(@RequestBody MetadataPageReq req) {
        try {
            if (req.getDatabaseName() == null || req.getDatabaseName().trim().isEmpty()) {
                return ResultVo.fail(400, "数��库名称不能为空");
            }
            if (req.getTableName() == null || req.getTableName().trim().isEmpty()) {
                return ResultVo.fail(400, "表名称不能为空");
            }
            PageVo<FieldInfoVo> result = metadataService.getTidbFields(req);
            return ResultVo.success(result);
        } catch (Exception e) {
            log.error("获取TiDB字段列表失败", e);
            return ResultVo.fail(500, "获取TiDB字段列表失败: " + e.getMessage());
        }
    }
}