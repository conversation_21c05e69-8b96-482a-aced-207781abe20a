package com.espc.sec.flow.util;

import com.espc.sec.flow.exception.UnzipException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.ArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR>
 * @date 2018-06-04
 */
@Slf4j
public class ZipUtil {

    /**
     * 解压文件到制定目录（性能优化的方法）
     *
     * @param file      待解压的文件
     * @param targetDir 目标目录
     * @throws IOException
     */
    public static void unzip3(File file, String targetDir) throws Exception {
        if (null == file) {
            throw new UnzipException("zipFile is null.");
        }
        ZipInputStream zIn = new ZipInputStream(new FileInputStream(file));
        BufferedInputStream bin = new BufferedInputStream(zIn);
        File fout = null;
        ZipEntry entry;
        FileOutputStream out = null;
        try {
            while ((entry = zIn.getNextEntry()) != null && !entry.isDirectory()) {
                fout = new File(targetDir, entry.getName());
                if (!fout.exists()) {
                    new File(fout.getParent()).mkdirs();
                }

                byte[] b = new byte[(int) entry.getSize()];
                out = new FileOutputStream(fout);
                bin.read(b);
                out.write(b);
                out.close();
            }
            bin.close();
            zIn.close();
        } catch (IOException e) {
            throw e;
        }

    }

    /**
     * 解压文件到制定目录
     *
     * @param file      待解压的文件
     * @param targetDir 目标目录
     * @throws IOException
     */
    public static void unzip4(File file, String targetDir) throws Exception {
        if (null == file) {
            throw new UnzipException("zipFile is null.");
        }
        ZipInputStream zIn = new ZipInputStream(new FileInputStream(file));
        BufferedInputStream bin = new BufferedInputStream(zIn);
        File fout = null;
        ZipEntry entry = null;
        try {
            while (true) {
                try {
                    entry = zIn.getNextEntry();
                } catch (java.io.EOFException e) {
                    break;
                }
                if (entry == null) {
                    break;
                }

                if (entry.isDirectory()) {
                    continue;
                }
                fout = new File(targetDir, entry.getName());
                if (!fout.exists()) {
                    new File(fout.getParent()).mkdirs();
                }

                int size = (int) entry.getSize();
                byte[] b = new byte[(int) entry.getSize()];
                FileOutputStream out = new FileOutputStream(fout);
                bin.read(b);
                out.write(b);
                out.close();
            }
            bin.close();
            zIn.close();
        } catch (IOException e) {
            throw e;
        }
    }


    /**
     * 将压缩文件解压到指定目录
     *
     * @param file      待解压文件
     * @param targetDir 目标目录
     * @throws UnzipException
     */
    public static void unzip(File file, String targetDir) throws UnzipException {
        if (null == file) {
            throw new UnzipException("zipFile is null.");
        }

        if (StringUtils.isBlank(targetDir)) {
            throw new UnzipException("targetDirPath is blank.");
        }

        InputStream inputStream = null;
        OutputStream outputStream = null;
        ZipArchiveInputStream zipArchiveInputStream = null;
        ArchiveEntry archiveEntry;
        try {
            inputStream = new FileInputStream(file);
            zipArchiveInputStream = new ZipArchiveInputStream(inputStream, "UTF-8");
            while (null != (archiveEntry = zipArchiveInputStream.getNextEntry())) {
                String archiveEntryFileName = archiveEntry.getName();
                File entryFile = new File(targetDir, archiveEntryFileName);
                if (!entryFile.exists()) {
                    entryFile.getParentFile().mkdirs();
                }
                byte[] buffer = new byte[1024 * 5];
                outputStream = new FileOutputStream(entryFile);
                int len = -1;
                while ((len = zipArchiveInputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, len);
                }
                outputStream.flush();
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.warn(null, e);
                }
            }
        } catch (Exception e) {
            throw new UnzipException(e);
        } finally {
            if (null != zipArchiveInputStream) {
                try {
                    zipArchiveInputStream.close();
                } catch (IOException e) {
                    log.warn(null, e);
                }
            }
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.warn(null, e);
                }
            }
            if (null != inputStream) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.warn(null, e);
                }
            }
        }
    }
}