package com.espc.sec.dataimport.minio.service;


import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.constant.Constants;
import com.espc.sec.dataimport.common.enums.ImportModeEnum;
import com.espc.sec.dataimport.common.enums.ImportTypeEnum;
import com.espc.sec.dataimport.common.service.AbstractImporter;
import com.espc.sec.dataimport.common.util.DateUtil;
import com.espc.sec.dataimport.common.util.FileUtils;
import com.espc.sec.dataimport.minio.helper.MinIOServiceHelper;
import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataimport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataimport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataimport.task.service.DataTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class MinioImporter extends AbstractImporter {

    @Autowired
    private MonitorIncrementDataService monitorIncrementDataService;

    @Autowired
    private MonitorTaskDataService monitorTaskDataService;

    @Autowired
    private DataTaskService dataTaskService;

    /**
     * /data/import/minio/1_1_20220920164916873_shanghai_1000001216_1906011219.cert
     * /data/import/minio/1_2_20220920164916873_shanghai_1000001216_1906011219.file
     * /data/import/minio/2_3_20220920164916873_shanghai_1000001216_1906011219.eml
     *
     * @param file
     * @throws Exception
     */
    @Override
    protected void doImport(File file) throws Exception {

        String bucketName = getNameByNodeConfig(file);
        String fileName = file.getName();
        Map<String, String> metadata = addNodeMessage(file);

        MinIOServiceHelper.uploadFile(bucketName, fileName, file, metadata);

        saveImportLogToMysql(bucketName, fileName, file.length()
                //其他参数
        );
    }

    @Override
    protected boolean validateFileName(String fileName) {
        return fileName.endsWith(".file")
                || fileName.endsWith(".cert")
                || fileName.endsWith(".eml");
    }

    /**
     * 保存导入日志
     *
     * @param bucketName bucketName
     * @param objectName bucket下的objectName
     */
    private void saveImportLogToMysql(String bucketName, String objectName, Long fileSize){
        //[1_2_20220920164916873_shanghai_1000001216_1906011219.eml]
        String[] objectNameSplit = objectName.split("_");
        String importMode = null;
        if (objectNameSplit.length > 1) {
            importMode = objectNameSplit[0];
        }
        if (importMode != null && importMode.equals(ImportModeEnum.increment.getCode().toString())) {
            MonitorIncrementDataDto dataDto = new MonitorIncrementDataDto();
            dataDto.setNode(objectNameSplit[3]);
            dataDto.setDatabaseType(ImportTypeEnum.MINIO.getCode());
            dataDto.setTableName(bucketName);
            dataDto.setTime(new Date());
            dataDto.setSize(fileSize);
            dataDto.setFileName(objectName);

            monitorIncrementDataService.create(dataDto);
        } else {
            String taskName = dataTaskService.getTaskNameByIdFromMemoryCache(Integer.parseInt(objectNameSplit[1]));

            MonitorTaskDataAddReq monitorTaskDataAddReq = new MonitorTaskDataAddReq();
            monitorTaskDataAddReq.setTaskName(taskName);
            monitorTaskDataAddReq.setDatabaseType(ImportTypeEnum.MINIO.getCode());
            monitorTaskDataAddReq.setNode(objectNameSplit[3]);
            monitorTaskDataAddReq.setTableName(bucketName);
            monitorTaskDataAddReq.setTime(DateUtil.format(new Date(), Constants.YYYY_MM_DD_HH_MM_SS));
            monitorTaskDataAddReq.setSize(fileSize);
            monitorTaskDataAddReq.setFileName(objectName);
            monitorTaskDataService.add(monitorTaskDataAddReq);
        }
    }

    /**
     * 根据配置加载节点信息
     * @param file file
     * @return Map
     */
    public Map<String, String> addNodeMessage(File file) {
        String[] fileNameSplit = file.getName().split("_");
        Map<String, String> metadata = new HashMap<>();
        if (Config.importConfig.minio.getDataNodeEnable()) {
            metadata.put(Constants.DATA_NODE, fileNameSplit[3]);
        }
        return metadata;
    }

    /**
     * 根据配置加载节点信息
     * @param file file
     * @return String
     */
    private String getNameByNodeConfig(File file) {
        String fileName = file.getName();

        String[] fileNameSplit = fileName.split("_");
        String node = fileNameSplit[3];
        String dateStr = fileNameSplit[2];
        String year = dateStr.substring(0, 4);
        String month = dateStr.substring(4, 6);
        String day = dateStr.substring(6, 8);

        String fileExtension = FileUtils.getFileExtension(fileName);

        if (Config.importConfig.minio.getDataNodeTableEnable()) {
            return node + "-" + Config.importConfig.minio.getUploadBucketPrefix() + fileExtension + "-" + year + month + day;
        }
        return Config.importConfig.minio.getUploadBucketPrefix() + fileExtension + "-" + year + month + day;
    }
}
