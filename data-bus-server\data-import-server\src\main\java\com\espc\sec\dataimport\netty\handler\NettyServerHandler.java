package com.espc.sec.dataimport.netty.handler;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.espc.sec.dataimport.common.enums.TaskStatusEnum;
import com.espc.sec.dataimport.common.util.SpringBeanUtil;
import com.espc.sec.dataimport.netty.entity.MessageWrapper;
import com.espc.sec.dataimport.netty.utils.ChannelUtil;
import com.espc.sec.dataimport.task.service.impl.DataTaskServiceImpl;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/17
 **/
@Slf4j
@ChannelHandler.Sharable
public class NettyServerHandler extends SimpleChannelInboundHandler<MessageWrapper> {

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, MessageWrapper msg) {
        try {
            switch (msg.getType()) {
                case TASK:
                    this.handleTask(ctx, msg);
                    break;
                default:
                    ctx.fireChannelRead(msg);
                    break;
            }
        } catch (Exception e) {
            log.error("服务端处理消息异常", e);
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        // 断开连接
        ChannelUtil.removeChannel(ctx.channel());
        log.info("客户端断开连接: {}", ctx.channel().id().toString());
    }

    /**
     * 处理字符串消息
     *
     * @param ctx
     * @param msg
     */
    private void handleTask(ChannelHandlerContext ctx, MessageWrapper msg) {
        String body = StrUtil.str(msg.getBody(), StandardCharsets.UTF_8);
        HashMap<String, Object> map = (HashMap<String, Object>) JSONUtil.toBean(body, Map.class);

        Integer taskId = MapUtil.getInt(map, "taskId", 0);
        Integer taskStatus = MapUtil.getInt(map, "taskStatus", 0);

        // 客户端上报的任务数据,调用总中心任务状态修改接口
        DataTaskServiceImpl dataTaskService = SpringBeanUtil.getBean(DataTaskServiceImpl.class);
        dataTaskService.updateTaskStatus(taskId, TaskStatusEnum.getTaskStatusEnum(taskStatus));
    }
}
