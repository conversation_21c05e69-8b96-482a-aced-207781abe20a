package com.espc.sec.dataimport.common.util;


import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.StringWriter;

import static com.fasterxml.jackson.databind.DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT;

/**
 * <AUTHOR>
 * @date 2017-07-26
 */
@Slf4j
public class JsonUtil {
    /**
     * json格式转换工具
     */
    public static ObjectMapper objectMapper;

    static {
        JsonFactory jsonFactory = new JsonFactory();
        jsonFactory.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        objectMapper = new ObjectMapper(jsonFactory);
        // 忽略未知属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        objectMapper.configure(ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
    }

    /**
     * 把对象转换成json格式
     *
     * @param object 对象
     * @return 返回json
     */
    public static String objectToJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("json转换失败", e);
        }
        return "";
    }

    /**
     * 读取json内容
     *
     * @param json  json字符串
     * @param clazz 转换成的类型
     * @param <T>   对应的类型
     * @return 返回反序列化后的类型实例
     * @throws IOException 抛出异常
     */
    public static <T> T jsonToObject(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("json转换失败", e);
        }
        return null;
    }

    /**
     * 读取json内容
     *
     * @param jsonFile 保存json的文件
     * @param clazz    转换成的类型
     * @param <T>      对应的类型
     * @return 返回反序列化后的类型实例
     * @throws IOException 抛出异常
     */
    public static <T> T jsonToObject(File jsonFile, Class<T> clazz) throws IOException {
        return objectMapper.readValue(jsonFile, clazz);
    }

    /**
     * 对象转成有格式的json字符串
     */
    public static String objectToJsonWithPrettyFormat(Object o) throws IOException {
        StringWriter stringWriter = new StringWriter();
        objectMapper.writerWithDefaultPrettyPrinter().writeValue(stringWriter, o);
        String s = stringWriter.toString();
        stringWriter.close();
        return s;
    }

    public static <T> T jsonToObject(File jsonFile, TypeReference<T> typeReference) throws IOException {
        return objectMapper.readValue(jsonFile, typeReference);
    }

    public static <T> T jsonToObject(String jsonStr, TypeReference<T> typeReference) throws IOException {
        return objectMapper.readValue(jsonStr, typeReference);
    }
}
