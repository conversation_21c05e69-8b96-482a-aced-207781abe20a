package com.espc.sec.dataexport.job.task;

import com.espc.sec.dataexport.common.dto.task.MinioTaskProperties;
import com.espc.sec.dataexport.common.util.SpringBeanUtil;
import com.espc.sec.dataexport.minio.service.MinioExportServiceImpl;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MinioTask extends AbstractTask<MinioTaskProperties> {

    @Override
    protected void doExecute(MinioTaskProperties minioTaskProperties) throws Exception {
        MinioExportServiceImpl exportService = SpringBeanUtil.getBean(MinioExportServiceImpl.class);
        exportService.taskExport(minioTaskProperties);
    }
}
