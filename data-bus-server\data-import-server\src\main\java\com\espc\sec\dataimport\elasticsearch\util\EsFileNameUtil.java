package com.espc.sec.dataimport.elasticsearch.util;


import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataimport.common.config.Config;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文件名验证器
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Slf4j
public class EsFileNameUtil {

    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");

    /**
     * 验证文件名格式
     *
     * @param fileName 文件名
     * @return 是否符合格式
     */
    public static boolean validate(String fileName) {
        if (!Config.commonConfig.getFileValidateSwitch()) {
            return true;
        }

        try {
            return validateFileNameFormat(fileName);
        } catch (Exception e) {
            log.error("验证文件名失败: {}", fileName, e);
            return false;
        }
    }

    /**
     * 验证文件名格式
     * 新格式：{mode}_{taskId}_{time}_{node}_{type}_{index}_{shard}.log
     */
    private static boolean validateFileNameFormat(String fileName) {
        if (fileName == null || fileName.isEmpty() || !fileName.endsWith(".log")) {
            return false;
        }

        String nameWithoutExt = fileName.replace(".log", "");
        String[] parts = nameWithoutExt.split("_");

        if (parts.length != 7) {
            return false;
        }

        // 验证时间戳
        if (!validateTimestamp(parts[2])) {
            return false;
        }

        // 验证节点名
        if (!Config.commonConfig.getSupportedNodeNames().contains(parts[3])) {
            return false;
        }

        // 验证数据库类型
        if (!"elasticsearch".equals(parts[4])) {
            return false;
        }

        return true;
    }


    /**
     * 验证时间戳格式
     */
    private static boolean validateTimestamp(String timestamp) {
        if (timestamp == null || timestamp.length() != 17) {
            return false;
        }

        try {
            // 验证是否为数字
            Long.parseLong(timestamp);

            // 验证时间戳格式
//            LocalDateTime.parse(timestamp, TIMESTAMP_FORMATTER);
            DateUtil.parse(timestamp, "yyyyMMddHHmmssSSS");
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 从文件名解析信息
     * 新格式：{mode}_{taskId}_{time}_{node}_{type}_{index}_{shard}.log
     */
    public static FileNameInfo parseFileName(String fileName) {
        if (!validate(fileName)) {
            throw new IllegalArgumentException("文件名格式不正确: " + fileName);
        }

        String nameWithoutExt = fileName.replace(".log", "");

        String[] parts = nameWithoutExt.split("_");

        FileNameInfo info = new FileNameInfo();
        info.setExportMode(Integer.valueOf(parts[0]));
        info.setTaskId(Integer.valueOf(parts[1]));
        info.setTimestamp(Long.parseLong(parts[2]));
        info.setNodeName(parts[3]);
        info.setDatabaseType(parts[4]);
        info.setIndexName(parts[5]);
        return info;
    }

    /**
     * 文件名信息类
     */
    public static class FileNameInfo {
        private Integer exportMode;
        private Integer taskId;
        private boolean incremental;
        private long timestamp;
        private long startTime;
        private long endTime;
        private String nodeName;
        private String databaseType;
        private String indexName;
        private String typeName;

        public Integer getExportMode() { return exportMode; }
        public void setExportMode(Integer exportMode) { this.exportMode = exportMode; }

        public Integer getTaskId() { return taskId; }
        public void setTaskId(Integer taskId) { this.taskId = taskId; }

        // Getters and Setters
        public boolean isIncremental() { return incremental; }
        public void setIncremental(boolean incremental) { this.incremental = incremental; }

        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }

        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }

        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }

        public String getNodeName() { return nodeName; }
        public void setNodeName(String nodeName) { this.nodeName = nodeName; }

        public String getDatabaseType() { return databaseType; }
        public void setDatabaseType(String databaseType) { this.databaseType = databaseType; }

        public String getIndexName() { return indexName; }
        public void setIndexName(String indexName) { this.indexName = indexName; }

        public String getTypeName() { return typeName; }
        public void setTypeName(String typeName) { this.typeName = typeName; }
    }
}