package com.espc.sec.dataexport.hdfs.consumer;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import com.espc.sec.dataexport.hdfs.dto.HdfsExportDto;
import com.espc.sec.dataexport.hdfs.service.HdfsExportServiceImpl;
import com.espc.sec.dataexport.hdfs.service.HdfsExporter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class HdfsConsumer {

    @Autowired
    private HdfsExportServiceImpl hdfsExportService;

    @KafkaListener(topics = "kt-hdfs", groupId = "kt-hdfs", containerFactory = "myKafkaListenerContainerFactory")
    public void listen(String message, Acknowledgment acknowledgment) {
        if (!Config.exportConfig.hdfs.getIncrementEnable()) {
            return;
        }
        log.info(LogKeyword.HDFS_EXPORT + "收到kafka消息{}", message);
        try {
            hdfsExportService.incrementExport(message);
        } catch (Exception e) {
            log.error(LogKeyword.HDFS_EXPORT + "hdfs导出错误", e);
            return;
        }
        acknowledgment.acknowledge();
    }

}
