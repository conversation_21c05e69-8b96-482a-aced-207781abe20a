package com.espc.sec.dataimport.common.config.configuration;

import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.config.Environment;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * 自定义数据源配置
 * 替代Spring Boot的自动配置，使用environment.json中的配置
 */
@Slf4j
@Configuration
public class StarRocksDataSourceConfiguration {

    /**
     * 创建数据源Bean
     * 使用environment.json中的配置替代application.yml
     */
    @Bean
    @Primary
    public DataSource dataSource() {
        if (Config.environment == null) {
            throw new RuntimeException("Environment configuration not loaded");
        }

        Environment.StarRocksDTO dsConfig = Config.environment.starRocks;
        if (dsConfig == null) {
            throw new RuntimeException("DataSource configuration not found in environment.json");
        }

        HikariConfig hikariConfig = new HikariConfig();

        // 基本连接配置
        hikariConfig.setJdbcUrl(dsConfig.getUrl());
        hikariConfig.setUsername(dsConfig.getUsername());
        hikariConfig.setPassword(dsConfig.getPassword());
        hikariConfig.setDriverClassName("com.mysql.cj.jdbc.Driver");

        // Hikari连接池配置

        hikariConfig.setMaximumPoolSize(10);
        hikariConfig.setMinimumIdle(5);
        hikariConfig.setConnectionTimeout(30000);
        hikariConfig.setIdleTimeout(600000);
        hikariConfig.setMaxLifetime(1800000);

        // 设置连接池名称
        hikariConfig.setPoolName("BigDataHikariCP");

        // 设置连接测试查询
        hikariConfig.setConnectionTestQuery("SELECT 1");

        log.info("Creating DataSource with URL: {}", dsConfig.getUrl());
        log.info("DataSource username: {}", dsConfig.getUsername());
        log.info("DataSource driver: {}", "com.mysql.cj.jdbc.Driver");

        log.info("Hikari MaximumPoolSize: {}", 10);
        log.info("Hikari MinimumIdle: {}", 5);

        return new HikariDataSource(hikariConfig);
    }

    /**
     * 创建JdbcTemplate Bean
     */
    @Bean
    @Primary
    public JdbcTemplate jdbcTemplate(DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}