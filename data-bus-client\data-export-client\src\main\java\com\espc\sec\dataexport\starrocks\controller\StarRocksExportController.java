package com.espc.sec.dataexport.starrocks.controller;

import com.espc.sec.dataexport.common.dto.task.StarRocksTaskProperties;
import com.espc.sec.dataexport.starrocks.service.StarRocksExportServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * StarRocks导出控制器
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/export/starrocks")
public class StarRocksExportController {

    @Autowired
    private StarRocksExportServiceImpl starRocksExportServiceImpl;

    /**
     * 根据参数导出StarRocks数据
     *
     * @param request 导出请求参数
     * @return 导出结果
     */
    @PostMapping("/export")
    public void exportData(@RequestBody StarRocksTaskProperties request) {
        starRocksExportServiceImpl.taskExport(request);
    }
}