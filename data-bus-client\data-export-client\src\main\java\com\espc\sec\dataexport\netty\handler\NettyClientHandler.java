package com.espc.sec.dataexport.netty.handler;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.espc.sec.dataexport.common.enums.OperateTypeEnum;
import com.espc.sec.dataexport.common.util.SpringBeanUtil;
import com.espc.sec.dataexport.netty.entity.MessageWrapper;
import com.espc.sec.dataexport.netty.utils.ChannelUtil;
import com.espc.sec.dataexport.task.dto.DataTaskDto;
import com.espc.sec.dataexport.task.service.impl.DataTaskServiceImpl;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/17
 **/
@Slf4j
@ChannelHandler.Sharable
public class NettyClientHandler extends SimpleChannelInboundHandler<MessageWrapper> {

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, MessageWrapper msg) {
        try {
            switch (msg.getType()) {
                case TASK:
                    this.handleTask(ctx, msg);
                    break;
                default:
                    ctx.fireChannelRead(msg);
                    break;
            }
        } catch (Exception e) {
            log.error("客户端处理消息异常", e);
        }
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) {
        // 添加连接
        ChannelUtil.serverChannel = ctx.channel();
        log.info("服务端加入连接: {}", ctx.channel().id().toString());
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) {
        // 断开连接
        ChannelUtil.serverChannel = null;
        log.info("服务端断开连接: {}", ctx.channel().id().toString());
    }

    private void handleTask(ChannelHandlerContext ctx, MessageWrapper msg) {
        OperateTypeEnum operateType = msg.getOperateType();
        String body = StrUtil.str(msg.getBody(), StandardCharsets.UTF_8);
        DataTaskServiceImpl dataTaskService = SpringBeanUtil.getBean(DataTaskServiceImpl.class);
        // 服务端下发的任务数据,调用分中心任务管理接口
        switch (operateType) {
            case CREATE:
                dataTaskService.createTask(JSONUtil.toBean(body, DataTaskDto.class));
                break;
            case UPDATE:
                dataTaskService.updateTask(JSONUtil.toBean(body, DataTaskDto.class));
                break;
            case DELETE:
                HashMap<String, Integer> map = (HashMap<String, Integer>) JSONUtil.toBean(body, Map.class);
                dataTaskService.deleteTask(map.get("taskId"));
            default:
                log.error("任务操作类型错误: {}", operateType);
                break;
        }
    }
}
