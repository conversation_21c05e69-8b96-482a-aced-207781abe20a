package com.espc.sec.dataexport.tidb.service;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.config.ExportConfig;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.dto.Tricycle;
import com.espc.sec.dataexport.common.dto.task.TidbTaskProperties;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import com.espc.sec.dataexport.common.exception.BigDataException;
import com.espc.sec.dataexport.common.service.CheckPointService;
import com.espc.sec.dataexport.common.service.IncrementExportService;
import com.espc.sec.dataexport.common.service.TaskExportService;
import com.espc.sec.dataexport.common.util.DateUtil;
import com.espc.sec.dataexport.common.util.MultiThreadUtil;
import com.espc.sec.dataexport.tidb.dto.TidbExportDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * TiDB导出服务实现
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Slf4j
@Service
public class TidbExportServiceImpl implements IncrementExportService, TaskExportService<TidbTaskProperties> {
    
    @Autowired
    private CheckPointService checkPointService;

    @Autowired
    private TidbExporter tidbExporter;

    @Override
    public void taskExport(TidbTaskProperties request) {
        log.info("TiDB任务导出参数：{}", request);
        // 1.转换请求参数为TableInfo列表
        List<TidbExportDto> tidbExportDtos = new ArrayList<>();
        for (TidbTaskProperties.DatabaseConfig databaseConfig : request.getDatabase()) {
            tidbExportDtos.addAll(
                    buildRequestToExportParam(databaseConfig.getTableName(),
                            DateUtil.parse(databaseConfig.getDataStartTime(), Constants.YYYY_MM_DD_HH_MM_SS).toJdkDate(),
                            DateUtil.parse(databaseConfig.getDataEndTime(), Constants.YYYY_MM_DD_HH_MM_SS).toJdkDate(),
                            ExportModeEnum.TASK,
                            request.getTaskId()
                    )
            );
        }

        if (tidbExportDtos.isEmpty()) {
            throw new BigDataException("没有有效的表配置信息");
        }
        doExport(tidbExportDtos);
    }

    @Override
    public void incrementExport() {
        List<TidbExportDto> tidbExportDtos = new ArrayList<>();
        List<ExportConfig.Tidb.Database> databaseList = Config.exportConfig.tidb.getDatabase();
        for (ExportConfig.Tidb.Database database : databaseList) {
            tidbExportDtos.addAll(
                    buildRequestToExportParam(database.getTableName(),
                            database.getDatabaseName(),
                            database.getTimeField(),
                            null, null,
                            ExportModeEnum.INCREMENT,
                            null
                    )
            );
        }
        doExport(tidbExportDtos);
    }

    /**
     * 2.导出
     */
    private void doExport(List<TidbExportDto> exportDtos) {
        List<CompletableFuture<Tricycle<Integer, ExportModeEnum, Date>>> futures = new ArrayList<>();
        
        for (TidbExportDto tidbExportDto : exportDtos) {
            Integer incrementNumber = buildTimeForExportAndRuturnIncrId(tidbExportDto);

            if (incrementNumber == null) {
                continue;
            }

            futures.add(CompletableFuture.supplyAsync(() -> {
                try {
                    tidbExporter.export(tidbExportDto);
                    return new Tricycle<>(incrementNumber, tidbExportDto.getExportModeEnum(), tidbExportDto.getEndTime());
                } catch (Exception e) {
                    log.error("TiDB导出错误：{}", tidbExportDto, e);
                }
                return new Tricycle<>(incrementNumber, tidbExportDto.getExportModeEnum(), null);
            }, MultiThreadUtil.executorService));
        }
        
        // 3.合并为一个CompletableFuture（等待所有任务完成）
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        );
        
        // 4.如果是增量型，需要写回最新时间到记录表
        allFutures.thenApply(v -> {
            List<Tricycle<Integer, ExportModeEnum, Date>> collect = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(tricyle -> tricyle.getY().equals(ExportModeEnum.INCREMENT))
                    .filter(tricyle -> tricyle.getZ() != null)
                    .peek(tricyle -> {
                        checkPointService.update(tricyle.getX(), tricyle.getZ());
                    })
                    .collect(Collectors.toList());
            return collect;
        });
    }

    private Integer buildTimeForExportAndRuturnIncrId(TidbExportDto tidbExportDto) {
        if (!ExportModeEnum.INCREMENT.equals(tidbExportDto.getExportModeEnum())) {
            return 0;
        }

        Tricycle<Integer, Date, Date> timeRangeForIndex = checkPointService.getTimeRangeForIndex(ExportTypeEnum.TIDB, tidbExportDto.getTableKey());

        if (timeRangeForIndex == null) {
            return null;
        }
        tidbExportDto.setStartTime(timeRangeForIndex.getY());
        tidbExportDto.setEndTime(timeRangeForIndex.getZ());
        return timeRangeForIndex.getX();
    }

    private List<TidbExportDto> buildRequestToExportParam(List<String> tableNames
            , Date startTime
            , Date endTime
            , ExportModeEnum exportModeEnum
            , Integer taskId) {
        String finaldatabaseName = Config.exportConfig.tidb.getDatabase().get(0).getDatabaseName();
        String findaltimeField = Config.exportConfig.tidb.getDatabase().get(0).getTimeField();
        return buildRequestToExportParam(tableNames
                , finaldatabaseName
                , findaltimeField
                , startTime
                , endTime
                , exportModeEnum
                , taskId);
    }

    private List<TidbExportDto> buildRequestToExportParam(List<String> tableNames
            , String databaseName
            , String timeField
            , Date startTime, Date endTime
            , ExportModeEnum exportModeEnum
            , Integer taskId) {
        if (tableNames == null || tableNames.isEmpty()) {
            return new ArrayList<>();
        }
        return tableNames.stream()
                .map(tablename -> new TidbExportDto(databaseName
                        , tablename
                        , timeField
                        , startTime
                        , endTime
                        , exportModeEnum
                        , taskId))
                .collect(Collectors.toList());
    }
}