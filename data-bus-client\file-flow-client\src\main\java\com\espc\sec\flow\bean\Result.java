package com.espc.sec.flow.bean;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018-06-04
 */
@Data
public class Result implements Serializable {
    /**
     * 常量状态：失败
     */
    public static final int STATUS_ERROR = 0;
    /**
     * 常量状态：成功
     */
    public static final int STATUS_OK = 1;

    /**
     * 状态
     */
    private int status;

    /**
     * 返回的信息
     */
    private String message;
}