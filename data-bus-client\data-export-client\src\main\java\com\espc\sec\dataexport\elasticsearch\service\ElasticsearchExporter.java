package com.espc.sec.dataexport.elasticsearch.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.service.impl.AbstractExporter;
import com.espc.sec.dataexport.elasticsearch.dto.ElasticsearchExportDto;
import com.espc.sec.dataexport.elasticsearch.util.EsClientHolder;
import com.espc.sec.dataexport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataexport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataexport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataexport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataexport.task.service.DataTaskService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Elasticsearch导出器
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Slf4j
@Service
public class ElasticsearchExporter extends AbstractExporter<ElasticsearchExportDto> {

    private static final TimeValue SCROLL_TIMEOUT = TimeValue.timeValueMinutes(1L);
    private static final int SCROLL_SIZE = 10000;

    @Autowired
    private MonitorTaskDataService monitorTaskDataService;

    @Autowired
    private MonitorIncrementDataService monitorIncrementDataService;

    @Autowired
    private DataTaskService dataTaskService;

    @Override
    protected List<File> doExport(ElasticsearchExportDto dto) throws Exception {
        SearchRequest searchRequest = buildSearchRequest(dto.getStartTime(), dto.getEndTime(), dto.getIndexName());

        SearchResponse searchResponse = EsClientHolder.normalRestClient.search(searchRequest, RequestOptions.DEFAULT);
        String scrollId = searchResponse.getScrollId();
        SearchHit[] hits = searchResponse.getHits().getHits();

        List<File> resultFiles = new ArrayList<>();
        AtomicInteger fileCounter = new AtomicInteger(1);
        String fileName = buildTempFileName(dto, fileCounter);
        File tempFile = new File(dto.getExportTempPath(), fileName);
        BufferedWriter writer = new BufferedWriter(new FileWriter(tempFile));
        long currentFileSize = 0;

        try {
            ProcessResult result = processHits(hits, writer, currentFileSize, dto, fileCounter, resultFiles, tempFile);
            writer = result.writer;
            currentFileSize = result.currentFileSize;

            while (hits != null && hits.length > 0) {
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(SCROLL_TIMEOUT);
                searchResponse = EsClientHolder.normalRestClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollId = searchResponse.getScrollId();
                hits = searchResponse.getHits().getHits();

                if (hits != null && hits.length > 0) {
                    result = processHits(hits, writer, currentFileSize, dto, fileCounter, resultFiles, tempFile);
                    writer = result.writer;
                    currentFileSize = result.currentFileSize;
                }
            }
        } finally {
            writer.close();
            if (!resultFiles.contains(tempFile)) {
                resultFiles.add(tempFile);
            }
            if (scrollId != null) {
                clearScroll(scrollId);
            }
        }

        return resultFiles;
    }

    @Override
    protected void saveImportLogToMysql(ElasticsearchExportDto exporter, File tempFile) throws Exception {
        String startTimeStr = DateUtil.format(exporter.getStartTime(), Constants.MILLS_FORMAT);
        String nodeName = Config.commonConfig.getNodeName();
        String databaseType = exporter.getExportTypeEnum().getCode();
        String tableName = exporter.getIndexName();
        Long size = (long) FileUtil.readLines(tempFile, StandardCharsets.UTF_8).size();
        String fileName = tempFile.getName();
        Integer exportMode = exporter.getExportModeEnum().getCode();

        if (ExportModeEnum.INCREMENT.getCode().equals(exportMode)) {
            MonitorIncrementDataDto dto = new MonitorIncrementDataDto();
            dto.setNode(nodeName);
            dto.setDatabaseType(databaseType);
            dto.setTableName(tableName);
            dto.setFileName(fileName);
            dto.setSize(size);
            dto.setTime(exporter.getStartTime());
            monitorIncrementDataService.create(dto);
        } else {
            String taskName = "elasticsearch_task";
            if (exporter.getTaskId() != null) {
                taskName = dataTaskService.getTaskNameByIdFromMemoryCache(exporter.getTaskId());
            }

            MonitorTaskDataAddReq req = new MonitorTaskDataAddReq();
            req.setNode(nodeName);
            req.setTaskName(taskName);
            req.setDatabaseType(databaseType);
            req.setTableName(tableName);
            req.setSize(size);
            req.setTime(startTimeStr);
            req.setFileName(fileName);
            monitorTaskDataService.add(req);
        }
    }

    private String buildTempFileName(ElasticsearchExportDto dto, AtomicInteger fileShardId) {
        boolean isTaskType = dto.getExportModeEnum().equals(ExportModeEnum.TASK);
        return String.format("%s_%s_%s_%s_%s_%s_%s.log",
                dto.getExportModeEnum().getCode(),
                isTaskType ? (dto.getTaskId() != null ? dto.getTaskId() : 0) : 0,
                DateUtil.format(dto.getStartTime(), Constants.MILLS_FORMAT),
                Config.commonConfig.getNodeName(),
                "elasticsearch",
                dto.getIndexName(),
                fileShardId.get());
    }

    private SearchRequest buildSearchRequest(Date startTime, Date endTime, String indexName) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        String startTimeStr = DateUtil.format(startTime, Constants.YYYY_MM_DD_HH_MM_SS);
        String endTimeStr = DateUtil.format(endTime, Constants.YYYY_MM_DD_HH_MM_SS);

        RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery(Config.exportConfig.elasticsearch.getTimeField())
                .gte(startTimeStr)  // "2025-07-18 16:51:25" 格式
                .lt(endTimeStr);    // "2025-07-19 17:51:25" 格式

        searchSourceBuilder.query(rangeQuery);
        searchSourceBuilder.size(SCROLL_SIZE);

        SearchRequest searchRequest = new SearchRequest(indexName);
        searchRequest.source(searchSourceBuilder);
        searchRequest.scroll(TimeValue.parseTimeValue("5m", "scroll"));
        return searchRequest;
    }

    private void clearScroll(String scrollId) {
        try {
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            EsClientHolder.normalRestClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("Failed to clear es scroll", e);
        }
    }

    private ProcessResult processHits(SearchHit[] hits, BufferedWriter writer, long currentFileSize,
                                      ElasticsearchExportDto dto, AtomicInteger fileCounter, List<File> resultFiles, File currentFile) throws IOException {
        for (SearchHit hit : hits) {
            String document = hit.getSourceAsString();
            long documentSize = document.getBytes().length + 2;

            if (currentFileSize + documentSize > Config.commonConfig.getMaxTempFileSizeMb() * 1024 * 1024) {
                writer.close();
                if (!resultFiles.contains(currentFile)) {
                    resultFiles.add(currentFile);
                }

                fileCounter.incrementAndGet();
                String fileName = buildTempFileName(dto, fileCounter);
                File newFile = new File(dto.getExportTempPath(), fileName);
                writer = new BufferedWriter(new FileWriter(newFile));
                currentFile = newFile;
                currentFileSize = 0;
            }

            writer.write(document);
            writer.newLine();
            currentFileSize += documentSize;
        }
        return new ProcessResult(writer, currentFileSize);
    }

    private static class ProcessResult {
        BufferedWriter writer;
        long currentFileSize;

        ProcessResult(BufferedWriter writer, long currentFileSize) {
            this.writer = writer;
            this.currentFileSize = currentFileSize;
        }
    }
}