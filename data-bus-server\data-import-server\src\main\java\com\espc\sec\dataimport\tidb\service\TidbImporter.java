package com.espc.sec.dataimport.tidb.service;

import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.constant.Constants;
import com.espc.sec.dataimport.common.service.AbstractImporter;
import com.espc.sec.dataimport.common.util.FileNameParseUtil;
import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataimport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataimport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataimport.task.service.DataTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.annotation.Propagation;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * TiDB导入服务
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
@Slf4j
public class TidbImporter extends AbstractImporter {

    // 1.解析sql字符串
    private static Pattern CRON_EXPRESSION = Pattern.compile(
            "(?i)\\bINSERT\\s+INTO\\s+(\\w+)\\s*\\(([^)]+)\\)\\s+VALUES\\s*\\(([^)]+)\\)",
            Pattern.CASE_INSENSITIVE
    );

    @Autowired
    private JdbcTemplate tidbJdbcTemplate;

    @Autowired
    private MonitorTaskDataService monitorTaskDataService;

    @Autowired
    private MonitorIncrementDataService monitorIncrementDataService;

    @Autowired
    private DataTaskService dataTaskService;

    @Override
    @Transactional(value = "tidbTransactionManager", rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    protected void doImport(File file) throws Exception {
        String sqlContent = FileUtils.readFileToString(file, StandardCharsets.UTF_8);
        log.info("开始导入文件: {}, 文件大小: {} bytes", file.getName(), sqlContent.length());

        // 验证数据库连接和当前数据库
        try {
            String currentDb = tidbJdbcTemplate.queryForObject("SELECT DATABASE()", String.class);
            log.info("当前连接的数据库: {}", currentDb);
            
            // 确保连接到正确的数据库
            tidbJdbcTemplate.execute("USE `probe-center`");
            log.info("切换到probe-center数据库");
            
            // 注释掉自动提交设置，让Spring事务管理器处理
            // tidbJdbcTemplate.execute("SET autocommit = 1");
            // log.info("设置自动提交模式");
            
            // 验证表是否存在
            Integer tableCount = tidbJdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'probe-center' AND table_name = 'probe_status'", 
                Integer.class);
            log.info("probe_status表存在检查: {}", tableCount > 0 ? "存在" : "不存在");
            
        } catch (Exception e) {
            log.error("数据库连接验证失败: {}", e.getMessage(), e);
        }

        // 2.分割SQL语句（简单的分割，按分号分割）
        String[] sqlStatements = sqlContent.split(";");
        log.info("分割后SQL语句数量: {}", sqlStatements.length);

        int insertedCount = 0;
        int totalStatements = 0;
        for (String sql : sqlStatements) {
            sql = sql.trim();
            if (sql.isEmpty() || sql.startsWith("--") || sql.startsWith("/*")) {
                continue;
            }
            
            totalStatements++;
            if (sql.toUpperCase().startsWith("INSERT")) {
                sql = addNodeMessage(file.getName(), sql);
                // 修复时间格式问题
                sql = fixTimeFormat(sql);
                log.info("第{}条SQL准备执行: {}", totalStatements, sql.length() > 200 ? sql.substring(0, 200) + "..." : sql);
                
                try {
                    // 使用事务方式执行
                    int affected = tidbJdbcTemplate.update(sql);
                    log.info("第{}条SQL执行结果: affected={}", totalStatements, affected);
                    
                    if (affected == 0) {
                        log.warn("第{}条SQL执行成功但影响行数为0，可能存在主键冲突", totalStatements);
                        // 尝试使用REPLACE INTO
                        String replaceSql = sql.replaceFirst("INSERT INTO", "REPLACE INTO");
                        log.info("尝试使用REPLACE INTO: {}", replaceSql.length() > 200 ? replaceSql.substring(0, 200) + "..." : replaceSql);
                        int replaceAffected = tidbJdbcTemplate.update(replaceSql);
                        log.info("REPLACE INTO执行结果: affected={}", replaceAffected);
                        insertedCount += replaceAffected;
                    } else {
                        insertedCount += affected;
                    }
                } catch (Exception e) {
                    log.error("第{}条SQL执行失败: {}", totalStatements, e.getMessage(), e);
                    // 尝试使用REPLACE INTO作为备选方案
                    try {
                        String replaceSql = sql.replaceFirst("INSERT INTO", "REPLACE INTO");
                        log.info("SQL执行失败，尝试REPLACE INTO: {}", replaceSql.length() > 200 ? replaceSql.substring(0, 200) + "..." : replaceSql);
                        int replaceAffected = tidbJdbcTemplate.update(replaceSql);
                        log.info("REPLACE INTO执行结果: affected={}", replaceAffected);
                        insertedCount += replaceAffected;
                    } catch (Exception e2) {
                        log.error("REPLACE INTO也执行失败: {}", e2.getMessage());
                    }
                }
            } else {
                // 3.执行非INSERT语句（如USE, SET, BEGIN, COMMIT等）
                log.info("执行非INSERT语句: {}", sql);
                try {
                    tidbJdbcTemplate.execute(sql);
                } catch (Exception e) {
                    log.error("非INSERT语句执行失败: {}, 错误: {}", sql, e.getMessage());
                }
            }
        }

        log.info("文件导入完成: {}, 总SQL语���数: {}, 成功插入行数: {}", file.getName(), totalStatements, insertedCount);
        saveImportLogToMysql(file.getName(), insertedCount);
    }

    /**
     * 4.保存导入日志
     */
    private void saveImportLogToMysql(String fileName, int insertedCount) {
        try {
            FileNameParseUtil.FileNameInfo fileInfo = FileNameParseUtil.parseTidbFileName(fileName);

            if (fileInfo.getExportMode() == 1) {
                MonitorIncrementDataDto dto = new MonitorIncrementDataDto();
                dto.setNode(fileInfo.getNodeName());
                dto.setDatabaseType(fileInfo.getDatabaseType());
                dto.setTableName(fileInfo.getTableName());
                dto.setFileName(fileName);
                dto.setSize((long)insertedCount);
                dto.setTime(DateUtil.parse(fileInfo.getTimestamp(), "yyyyMMddHHmmssSSS"));
                monitorIncrementDataService.create(dto);
            } else {
                String taskName = "import_task";
                if (fileInfo.getTaskId() != null && fileInfo.getTaskId() != 0) {
                    taskName = dataTaskService.getTaskNameByIdFromMemoryCache(fileInfo.getTaskId());
                }

                MonitorTaskDataAddReq req = new MonitorTaskDataAddReq();
                req.setNode(fileInfo.getNodeName());
                req.setTaskName(taskName);
                req.setDatabaseType(fileInfo.getDatabaseType());
                req.setTableName(fileInfo.getTableName());
                req.setSize((long) insertedCount);
                req.setTime(fileInfo.getFormattedTime());
                req.setFileName(fileName);

                monitorTaskDataService.add(req);
            }
            log.info("TiDB导入日志记录成功: 文件={}, 导入条数={}", fileName, insertedCount);
        } catch (Exception e) {
            log.error("TiDB导入日志记录失败: 文件={}, 导入条数={}", fileName, insertedCount, e);
        }
    }

    /**
     * 5.文件名格式：20220920164916873_shanghai_tidb_probe-center_probe-info.sql
     */
    @Override
    protected boolean validateFileName(String fileName) {
        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".sql") || lowerFileName.endsWith(".log");
    }

    /**
     * 6.替换表名和增��节点字段
     */
    private String addNodeMessage(String fileName, String sql) {
        if (!Config.importConfig.tidb.getDataNodeEnable() && !Config.importConfig.tidb.getDataNodeTableEnable()) {
            return sql;
        }
        Matcher matcher = CRON_EXPRESSION.matcher(sql);
        if (!matcher.find()) {
            return sql;
        }

        try {
            String node = fileName.split("_")[3];
            String originalTable = matcher.group(1);
            String columns = matcher.group(2).trim();
            String values = matcher.group(3).trim();
            String insertPrefix = sql.substring(0, sql.indexOf(originalTable));

            String newTable = originalTable;
            if (Config.importConfig.tidb.getDataNodeTableEnable()) {
                newTable = node + "_" + originalTable;
            }
            String newColumn = Constants.DATA_NODE;
            String newValue = node;
            if (Config.importConfig.tidb.getDataNodeEnable()) {
                return insertPrefix + newTable + " (" + columns + ", " + newColumn + ") VALUES (" + values + ", '" + newValue + "')";
            }
            return insertPrefix + newTable + " (" + columns +  ") VALUES (" + values +")";
        } catch (Exception e) {
            log.info("解析TiDB失败：{}", e);
        }
        return sql;
    }

    /**
     * 修复时间格式问题 - 将ISO格式转换为MySQL标准格式
     */
    private String fixTimeFormat(String sql) {
        // 修复create_time和update_time的ISO格式
        // 将 '2025-07-11T11:23' 转换为 '2025-07-11 11:23:00'
        sql = sql.replaceAll("'(\\d{4}-\\d{2}-\\d{2})T(\\d{2}:\\d{2})'", "'$1 $2:00'");
        // 将 '2025-07-11T11:23:45' 转换为 '2025-07-11 11:23:45'
        sql = sql.replaceAll("'(\\d{4}-\\d{2}-\\d{2})T(\\d{2}:\\d{2}:\\d{2})'", "'$1 $2'");
        return sql;
    }
}