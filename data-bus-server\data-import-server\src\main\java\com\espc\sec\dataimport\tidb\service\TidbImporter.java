package com.espc.sec.dataimport.tidb.service;

import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.constant.Constants;
import com.espc.sec.dataimport.common.service.AbstractImporter;
import com.espc.sec.dataimport.common.util.FileNameParseUtil;
import com.espc.sec.dataimport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataimport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataimport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataimport.task.service.DataTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * TiDB导入服务
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
@Slf4j
public class TidbImporter extends AbstractImporter {

    // 1.解析sql字符串
    private static Pattern CRON_EXPRESSION = Pattern.compile(
            "(?i)\\bINSERT\\s+INTO\\s+(\\w+)\\s*\\(([^)]+)\\)\\s+VALUES\\s*\\(([^)]+)\\)",
            Pattern.CASE_INSENSITIVE
    );

    @Autowired
    private JdbcTemplate tidbJdbcTemplate;

    @Autowired
    private MonitorTaskDataService monitorTaskDataService;

    @Autowired
    private MonitorIncrementDataService monitorIncrementDataService;

    @Autowired
    private DataTaskService dataTaskService;

    @Override
    protected void doImport(File file) throws Exception {
        String sqlContent = FileUtils.readFileToString(file, StandardCharsets.UTF_8);

        // 2.分割SQL语句（简单的分割，按分号分割）
        String[] sqlStatements = sqlContent.split(";");

        int insertedCount = 0;
        for (String sql : sqlStatements) {
            sql = sql.trim();
            if (sql.isEmpty() || sql.startsWith("--") || sql.startsWith("/*")) {
                continue;
            }
            if (sql.toUpperCase().startsWith("INSERT")) {
                sql = addNodeMessage(file.getName(), sql);
                int affected = tidbJdbcTemplate.update(sql);
                insertedCount += affected;
            } else {
                // 3.执行非INSERT语句（如USE, SET, BEGIN, COMMIT等）
                tidbJdbcTemplate.execute(sql);
            }
        }

        saveImportLogToMysql(file.getName(), insertedCount);
    }

    /**
     * 4.保存导入日志
     */
    private void saveImportLogToMysql(String fileName, int insertedCount) {
        try {
            FileNameParseUtil.FileNameInfo fileInfo = FileNameParseUtil.parseTidbFileName(fileName);

            if (fileInfo.getExportMode() == 1) {
                MonitorIncrementDataDto dto = new MonitorIncrementDataDto();
                dto.setNode(fileInfo.getNodeName());
                dto.setDatabaseType(fileInfo.getDatabaseType());
                dto.setTableName(fileInfo.getTableName());
                dto.setFileName(fileName);
                dto.setSize((long)insertedCount);
                dto.setTime(DateUtil.parse(fileInfo.getTimestamp(), "yyyyMMddHHmmssSSS"));
                monitorIncrementDataService.create(dto);
            } else {
                String taskName = "import_task";
                if (fileInfo.getTaskId() != null && fileInfo.getTaskId() != 0) {
                    taskName = dataTaskService.getTaskNameByIdFromMemoryCache(fileInfo.getTaskId());
                }

                MonitorTaskDataAddReq req = new MonitorTaskDataAddReq();
                req.setNode(fileInfo.getNodeName());
                req.setTaskName(taskName);
                req.setDatabaseType(fileInfo.getDatabaseType());
                req.setTableName(fileInfo.getTableName());
                req.setSize((long) insertedCount);
                req.setTime(fileInfo.getFormattedTime());
                req.setFileName(fileName);

                monitorTaskDataService.add(req);
            }
            log.info("TiDB导入日志记录成功: 文件={}, 导入条数={}", fileName, insertedCount);
        } catch (Exception e) {
            log.error("TiDB导入日志记录失败: 文件={}, 导入条数={}", fileName, insertedCount, e);
        }
    }

    /**
     * 5.文件名格式：20220920164916873_shanghai_tidb_probe-center_probe-info.sql
     */
    @Override
    protected boolean validateFileName(String fileName) {
        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".sql") || lowerFileName.endsWith(".log");
    }

    /**
     * 6.替换表名和增��节点字段
     */
    private String addNodeMessage(String fileName, String sql) {
        if (!Config.importConfig.tidb.getDataNodeEnable() && !Config.importConfig.tidb.getDataNodeTableEnable()) {
            return sql;
        }
        Matcher matcher = CRON_EXPRESSION.matcher(sql);
        if (!matcher.find()) {
            return sql;
        }

        try {
            String node = fileName.split("_")[3];
            String originalTable = matcher.group(1);
            String columns = matcher.group(2).trim();
            String values = matcher.group(3).trim();
            String insertPrefix = sql.substring(0, sql.indexOf(originalTable));

            String newTable = originalTable;
            if (Config.importConfig.tidb.getDataNodeTableEnable()) {
                newTable = node + "_" + originalTable;
            }
            String newColumn = Constants.DATA_NODE;
            String newValue = node;
            if (Config.importConfig.tidb.getDataNodeEnable()) {
                return insertPrefix + newTable + " (" + columns + ", " + newColumn + ") VALUES (" + values + ", '" + newValue + "')";
            }
            return insertPrefix + newTable + " (" + columns +  ") VALUES (" + values +")";
        } catch (Exception e) {
            log.info("解析TiDB失败：{}", e);
        }
        return sql;
    }
}