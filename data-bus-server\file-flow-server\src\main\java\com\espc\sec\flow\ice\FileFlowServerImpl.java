package com.espc.sec.flow.ice;

import Ice.Current;
import com.espc.sec.flow.bean.Result;
import com.espc.sec.flow.bean.RevFile;
import com.espc.sec.flow.bean.TempFile;
import com.espc.sec.flow.util.ConfigUtil;
import com.espc.sec.flow.util.Constant;
import com.espc.sec.flow.util.JsonUtil;
import com.ice.common._CommonServiceDisp;
import com.ice.common.byteArrayHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-06-04
 */
@Slf4j
public class FileFlowServerImpl extends _CommonServiceDisp {

    public static boolean isWindows = false;

    static {
        String osName = System.getProperty("os.name").toLowerCase();
        log.info("os name:" + osName);
        isWindows = osName.contains("windows");
    }

    @Override
    public String request(int i, String s, Current current) {
        return null;
    }

    @Override
    public String requestWithByteIn(int flag, String fileInfo, byte[] bytes, Current current) {
        try {
            if (isNeedSleep(ConfigUtil.targetFileSystem)) {
                log.info("disk use percent is over the threadSold. Thread will sleep 10 minutes.");
                Thread.sleep(ConfigUtil.config.getSleepTime() * 1000);
                log.info("thread weak up.");
            }
        } catch (Exception e) {
            log.error("sleep exception.", e);
        }
        long id = Thread.currentThread().getId();
        Thread.currentThread().setName("Thread-Receive-" + id);
        switch (flag) {
            case Constant.FLAG_SEND_NORMAL_FILE:
                return receiveNormalFile(fileInfo, bytes);
            case Constant.FLAG_SEND_ZIP_FILE:
                return receiveZipFile(fileInfo, bytes);
            default:
                log.error("invalid flag:" + flag);
                return JsonUtil.objectToJson(new Result(Result.STATUS_ERROR, "invalid flag:" + flag));
        }
    }

    @Override
    public String requestWithByteOut(int i, String s, byteArrayHolder byteArrayHolder, Current current) {
        return null;
    }

    @Override
    public String requestWithByteInOut(int i, String s, byte[] bytes, byteArrayHolder byteArrayHolder, Current current) {
        return null;
    }

    /**
     * 判断是否需要休眠
     *
     * @param fileSystem 文件系统
     * @return 如果使用率超过阈值就返回true，否则返回false
     * @throws Exception 抛出异常
     */
    private boolean isNeedSleep(String fileSystem) throws Exception {
        if (isWindows) {
            File file = new File(fileSystem);
            if (file.isFile()) {
                return false;
            }
            long total = file.getTotalSpace();
            long free = file.getFreeSpace();
            long used = total - free;
            double percent = used * 100 / total;
            if (percent >= ConfigUtil.threadSoldPercent) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * 接收标准文件
     *
     * @param fileInfo 文件信息
     * @param bytes    文件内容
     * @return 返回接收结果
     */
    public String receiveNormalFile(String fileInfo, byte[] bytes) {
        try {
            fileInfo = fileInfo.replace("\\", "\\\\");
            Map req = (Map) JsonUtil.jsonToObject(fileInfo, Map.class);
            File targetFile = new File(ConfigUtil.config.getTargetDir(), (String) req.get("filePath"));
            if (targetFile.exists()) {
                FileUtils.deleteQuietly(targetFile);
            }
            FileUtils.writeByteArrayToFile(targetFile, bytes);
            log.debug("received a normal file:" + targetFile);
            return JsonUtil.objectToJson(new Result(Result.STATUS_OK));
        } catch (Exception e) {
            log.error("error when receive normal file.", e);
            Result result = new Result(Result.STATUS_ERROR, e.getMessage());
            return JsonUtil.objectToJson(result);
        }
    }

    /**
     * 接收压缩文件
     *
     * @param fileInfo 文件信息
     * @param bytes    文件内容
     * @return 返回接收结果
     */
    public String receiveZipFile(String fileInfo, byte[] bytes) {
        try {
            RevFile revFile = (RevFile) JsonUtil.jsonToObject(fileInfo, RevFile.class);
            if (RevFile.TYPE_ZIP.equals(revFile.getType())) {
                File file = new File(ConfigUtil.config.getTempDir(), revFile.getFileName());
                // 3、写入到临时目录
                FileUtils.writeByteArrayToFile(file, bytes);
                log.debug("received a xzip file:" + file);

                // 4、加入队列
                TempFile tempFile = new TempFile();
                tempFile.setFile(file);
                Constant.FILE_QUEUE.put(tempFile);
                log.debug("put zip file into queue success, file:" + file + ", queue size:" + Constant.FILE_QUEUE.size());
                return JsonUtil.objectToJson(new Result(Result.STATUS_OK));
            } else {
                String msg = "file-flow-server do not receive type:" + revFile.getType() + " file name:" + revFile.getFileName();
                log.debug(msg);
                Result result = new Result(Result.STATUS_ERROR, msg);
                return JsonUtil.objectToJson(result);
            }
        } catch (Exception e) {
            log.error("error when receive zip file.", e);
            Result result = new Result(Result.STATUS_ERROR, e.getMessage());
            return JsonUtil.objectToJson(result);
        }
    }
}