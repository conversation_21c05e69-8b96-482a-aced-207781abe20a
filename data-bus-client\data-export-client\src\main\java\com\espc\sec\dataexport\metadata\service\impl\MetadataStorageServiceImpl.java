package com.espc.sec.dataexport.metadata.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.espc.sec.dataexport.metadata.entity.DataTableConfigPo;
import com.espc.sec.dataexport.metadata.entity.DataTableFieldConfigPo;
import com.espc.sec.dataexport.metadata.mapper.DataTableConfigMapper;
import com.espc.sec.dataexport.metadata.mapper.DataTableFieldConfigMapper;
import com.espc.sec.dataexport.metadata.service.MetadataStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.IntStream;

/**
 * 元数据存储服务实现类
 *
 * <AUTHOR>
 * @date 2025-08-29
 */
@Slf4j
@Service
public class MetadataStorageServiceImpl implements MetadataStorageService {
    
    @Autowired
    private DataTableConfigMapper dataTableConfigMapper;
    
    @Autowired
    private DataTableFieldConfigMapper dataTableFieldConfigMapper;
    
    @Override
    public void saveDataTableConfig(DataTableConfigPo dataTableConfig) {
        if (dataTableConfig.getCreateTime() == null) {
            dataTableConfig.setCreateTime(new Date());
        }
        dataTableConfigMapper.insert(dataTableConfig);
    }
    
    @Override
    public void saveDataTableFieldConfigs(List<DataTableFieldConfigPo> fieldConfigs) {
        for (DataTableFieldConfigPo fieldConfig : fieldConfigs) {
            dataTableFieldConfigMapper.insert(fieldConfig);
        }
    }
    
    @Override
    public DataTableConfigPo getDataTableConfig(String dataType, String dataKey, String node) {
        QueryWrapper<DataTableConfigPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_type", dataType)
                   .eq("data_key", dataKey)
                   .eq("node", node);
        return dataTableConfigMapper.selectOne(queryWrapper);
    }
    
    @Override
    public List<DataTableFieldConfigPo> getDataTableFieldConfigs(Integer dataTableConfigId) {
        QueryWrapper<DataTableFieldConfigPo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("data_table_config_id", dataTableConfigId);
        return dataTableFieldConfigMapper.selectList(queryWrapper);
    }
    
    @Override
    @Transactional
    public void storeMetadata(String dataType, String dataKey, String node, List<String> fieldNames, List<String> fieldTypes) {
        try {
            // 检查是否已存在相同的配置
            DataTableConfigPo existingConfig = getDataTableConfig(dataType, dataKey, node);
            
            if (existingConfig != null) {
                log.info("元数据配置已存在，跳过存储: dataType={}, dataKey={}, node={}", dataType, dataKey, node);
                return;
            }
            
            // 保存数据表配置
            DataTableConfigPo dataTableConfig = new DataTableConfigPo();
            dataTableConfig.setDataType(dataType);
            dataTableConfig.setDataKey(dataKey);
            dataTableConfig.setNode(node);
            dataTableConfig.setCreateTime(new Date());
            
            saveDataTableConfig(dataTableConfig);
            
            // 保存字段配置
            if (fieldNames != null && fieldTypes != null && fieldNames.size() == fieldTypes.size()) {
                List<DataTableFieldConfigPo> fieldConfigs = IntStream.range(0, fieldNames.size())
                        .mapToObj(i -> {
                            DataTableFieldConfigPo fieldConfig = new DataTableFieldConfigPo();
                            fieldConfig.setDataTableConfigId(dataTableConfig.getId());
                            fieldConfig.setFieldName(fieldNames.get(i));
                            fieldConfig.setFieldType(fieldTypes.get(i));
                            return fieldConfig;
                        })
                        .toList();
                
                saveDataTableFieldConfigs(fieldConfigs);
            }
            
            log.info("元数据存储成功: dataType={}, dataKey={}, node={}, fieldCount={}", 
                    dataType, dataKey, node, fieldNames != null ? fieldNames.size() : 0);
                    
        } catch (Exception e) {
            log.error("元数据存储失败: dataType={}, dataKey={}, node={}", dataType, dataKey, node, e);
            throw e;
        }
    }
}