package com.espc.sec.dataimport.common.exception;

import com.espc.sec.dataimport.common.enums.ErrorCodeEnum;

public class BusinessException extends RuntimeException {
    private final Integer code;

    public BusinessException(ErrorCodeEnum errorCodeEnum, String message) {
        super(message);
        this.code = errorCodeEnum.getCode();
    }

    public BusinessException(ErrorCodeEnum errorCodeEnum) {
        super(errorCodeEnum.getName());
        this.code = errorCodeEnum.getCode();
    }

    public Integer getCode() {
        return code;
    }
}
