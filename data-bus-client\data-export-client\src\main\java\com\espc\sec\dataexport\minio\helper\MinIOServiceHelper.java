package com.espc.sec.dataexport.minio.helper;


import cn.hutool.core.util.StrUtil;
import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.config.Environment;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.common.exception.MinioDownLoadException;
import com.espc.sec.dataexport.minio.entity.MinioMetaData;
import com.google.common.collect.Lists;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import io.minio.messages.*;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
public class MinIOServiceHelper {

    public static MinioClient minioClient = null;

    // 缓存桶是否存在
    private static ConcurrentHashMap<String, Boolean> existsBucket = new ConcurrentHashMap<>();

    public static boolean loadMinIOConnect() {
        try {
            Environment.MinioDTO minio = Config.environment.minio;
            minioClient = MinioClient.builder()
                    .endpoint(minio.getEndPoint())
                    .credentials(minio.getUsername(), minio.getPassword())
                    .build();
            return true;
        } catch (Exception e) {
            log.error("获取minio连接异常", e);
            return false;
        }
    }

    /**
     * 从给定输入流中传输对象并放入bucket
     */
    public static ObjectWriteResponse putObject(String bucketName, String objectName, InputStream stream, long objectSize, String contentType) throws Exception {
        if (minioClient == null) {
            loadMinIOConnect();
        }

        if (minioClient == null) {
            log.error("获取minio连接失败");
            return null;
        }

        if (!existsBucket.getOrDefault(bucketName, false)) {
            synchronized (MinIOServiceHelper.class) {
                if (!existsBucket.getOrDefault(bucketName, false)) {
                    BucketExistsArgs existsArgs = BucketExistsArgs.builder().bucket(bucketName).build();
                    if (!minioClient.bucketExists(existsArgs)) {
                        MakeBucketArgs makeArgs = MakeBucketArgs.builder().bucket(bucketName).build();
                        minioClient.makeBucket(makeArgs);
                        log.info("bucket {} 不存在， 自动创建该bucket", bucketName);
                    }
                    existsBucket.put(bucketName, true);
                }
            }
        }

        if (objectName.endsWith(".txt")) {
            contentType = "text/plain";
        } else if (objectName.endsWith(".cert")) {
            contentType = "application/x-x509-ca-cert"; // 证书文件
        } else if (objectName.endsWith(".eml")) {
            contentType = "message/rfc822"; // 电子邮件
        } else {
            contentType = "application/octet-stream"; // 默认二进制流
        }

        //long objSize = -1;
        // objectSize已知，partSize设为-1意为自动设置
        long partSize = -1;
        PutObjectArgs putArgs = PutObjectArgs.builder()
                .bucket(bucketName)
                .object(objectName)
                .stream(stream, objectSize, partSize)
                .contentType(contentType).build();
        ObjectWriteResponse response = minioClient.putObject(putArgs);

        return response;
    }


    /**
     * 从bucket获取指定对象的输入流，后续可使用输入流读取对象
     * getObject与minio server连接默认保持5分钟，
     * 每隔15s由minio server向客户端发送keep-alive check，5分钟后由客户端主动发起关闭连接
     */
    public static InputStream getObject(String bucketName, String objectName) throws Exception {
        if (minioClient == null) {
            loadMinIOConnect();
        }

        if (minioClient == null) {
            log.error("获取minio连接失败");
            return null;
        }
        GetObjectArgs args = GetObjectArgs.builder().bucket(bucketName).object(objectName).build();
        return minioClient.getObject(args);
    }

    /**
     * 获取对象的临时访问url，有效期5分钟
     */
    public static String getObjectURL(String bucketName, String objectName) throws Exception {
        if (minioClient == null) {
            loadMinIOConnect();
        }

        if (minioClient == null) {
            log.error("获取minio连接失败");
            return null;
        }
        GetPresignedObjectUrlArgs args = GetPresignedObjectUrlArgs.builder().bucket(bucketName).object(objectName).expiry(5, TimeUnit.MINUTES).method(Method.GET).build();
        return minioClient.getPresignedObjectUrl(args);
    }

    public static List<Bucket> listBuckets() {
        if (minioClient == null) {
            loadMinIOConnect();
        }

        if (minioClient == null) {
            log.error("获取minio连接失败");
            return null;
        }

        try {

            return minioClient.listBuckets();
        } catch (Exception e) {
            log.error("获取Bucket异常");
            return null;
        }
    }

    public static void deleteBuckets(String bucketName) {
        if (minioClient == null) {
            loadMinIOConnect();
        }

        if (minioClient == null) {
            log.error("获取minio连接失败");
            return;
        }

        try {
            // 删除Bucket中的所有对象
            Iterable<Result<Item>> results = minioClient.listObjects(ListObjectsArgs.builder().bucket(bucketName).build());
            for (Result<Item> result : results) {
                Item item = result.get();
                removeObject(bucketName, item.objectName());
            }

            // 删除Bucket
            minioClient.removeBucket(RemoveBucketArgs.builder().bucket(bucketName).build());
        } catch (Exception e) {
            log.error("删除Bucket异常");
        }
    }

    /**
     * 删除对象
     */
    public static void removeObject(String bucketName, String objectName) throws Exception {
        if (minioClient == null) {
            loadMinIOConnect();
        }

        if (minioClient == null) {
            log.error("获取minio连接失败");
            return;
        }
        RemoveObjectArgs args = RemoveObjectArgs.builder().bucket(bucketName).object(objectName).build();
        minioClient.removeObject(args);
        log.info("bucket:{}文件{}已删除", bucketName, objectName);
    }

    /**
     * 上传MultipartFile
     *
     * @param bucketName 文件存放的bucket
     * @param objectName
     */
    public static ObjectWriteResponse uploadFile(String bucketName, String objectName, File file) throws Exception {
        InputStream inputStream = Files.newInputStream(file.toPath());
        ObjectWriteResponse response = putObject(bucketName, objectName, inputStream, file.length(), Files.probeContentType(file.toPath()));
        inputStream.close();
        return response;
    }

    /**
     * 下载文件到本地
     *
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param filePath   本地文件路径
     */
    public static void downloadObject(String bucketName, String objectName, String filePath) throws MinioDownLoadException {
        try {
            minioClient.downloadObject(
                    DownloadObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .filename(filePath)
                            .build());
        } catch (Exception e) {
            log.error("minio下载文件失败", e);

            throw new MinioDownLoadException("下载文件失败");
        }
    }

    public static List<String> getRecentFilesOptimized(String bucketName, int minutes) throws Exception {
        List<String> recentFiles = new ArrayList<>();
        ZonedDateTime cutoffTime = ZonedDateTime.now().minus(minutes, ChronoUnit.MINUTES);
        String startAfter = cutoffTime.toString();

        Iterable<Result<Item>> results = minioClient.listObjects(
                ListObjectsArgs.builder()
                        .bucket(bucketName)
                        .recursive(true)
                        .startAfter(startAfter)
                        .build());

        for (Result<Item> result : results) {
            Item item = result.get();
            if (!item.objectName().endsWith("/")) {
                recentFiles.add(item.objectName());
            }
        }

        return recentFiles;
    }

    /**
     * 判断存储桶是否存在
     *
     * @param bucketName 存储桶名称
     * @return 存在返回 true，否则返回 false
     */
    public static boolean doesBucketExist(String bucketName) {
        try {
            return minioClient.bucketExists(BucketExistsArgs.builder()
                    .bucket(bucketName)
                    .build());
        } catch (Exception e) {
            log.error("查询桶是否存在失败");
        }
        return false;
    }

    /**
     * 创建存储桶
     *
     * @param bucketName 存储桶名称
     */
    public static void createBucket(String bucketName) {
        try {
            // 检查桶是否已存在
            if (!doesBucketExist(bucketName)) {
                minioClient.makeBucket(MakeBucketArgs.builder()
                        .bucket(bucketName)
                        .build());
            } else {
                System.out.println("存储桶已存在: " + bucketName);
            }
        } catch (Exception e) {
            log.error("创建存储桶失败: " + bucketName, e);
        }
    }


    /**
     * 配置监听事件
     *
     * @param bucketName 桶名
     * @param queue      arn:minio:sqs::minio-kafka:kafka
     * @throws Exception
     */
    public static void configureKafkaNotification(String bucketName, String queue) throws Exception {
        if (StrUtil.isBlank(bucketName) || StrUtil.isBlank(queue)) {
            log.warn("minio桶名或监听队列为空");
        }
        createBucket(bucketName);
        NotificationConfiguration config = new NotificationConfiguration();

        QueueConfiguration queueConfig = new QueueConfiguration();
        queueConfig.setQueue(queue);
        queueConfig.setEvents(
                Lists.newArrayList(EventType.OBJECT_CREATED_PUT
                        , EventType.OBJECT_CREATED_POST
                        , EventType.OBJECT_REMOVED_DELETE)
        );


        config.setQueueConfigurationList(Lists.newArrayList(queueConfig));

        minioClient.setBucketNotification(
                SetBucketNotificationArgs.builder()
                        .bucket(bucketName)
                        .config(config)
                        .build());

        log.info(LogKeyword.MINIO_EXPORT + "添加监听成功,bucket:{},queue{}", bucketName, queue);
    }

    /**
     * 获取指定桶下面的对象,并传入分页对象
     */
    public static Iterable<Result<Item>> listObjectsByPage(String bucketName, Integer objectCount, String marker) throws IOException, InvalidKeyException, InvalidResponseException, InsufficientDataException, NoSuchAlgorithmException, ServerException, InternalException, XmlParserException, ErrorResponseException, NoSuchFieldException, IllegalAccessException {
        List<MinioMetaData> objectFileName = new ArrayList<>();
        if (objectCount < 1 || objectCount > Constants.MINIO_PAGE_OBJECT_MAX) {
            return null;
        }
        ListObjectsArgs listObjectsArgs = ListObjectsArgs.builder()
                .bucket(bucketName)
                .maxKeys(objectCount)
                .startAfter(marker)
                .includeUserMetadata(true)
                .build();
        Iterable<Result<Item>> results = minioClient.listObjects(listObjectsArgs);

        return results == null? new ArrayList<>(): results;

    }

}