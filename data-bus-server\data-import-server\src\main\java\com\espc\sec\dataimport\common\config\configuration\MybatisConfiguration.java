//package com.espc.sec.dataimport.common.config.configuration;
//
//
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//
//import javax.sql.DataSource;
//
//@Configuration
//@MapperScan(
//        basePackages = "com.espc.sec.dataimport.*.mapper",
//        sqlSessionFactoryRef = "mysqlSqlSessionFactory"
//)
//public class MybatisConfiguration {
//    /**
//     * 配置SqlSessionFactory
//     */
//    @Bean("mysqlSqlSessionFactory")
//    public SqlSessionFactory mysqlSqlSessionFactory(
//            @Qualifier("mysqlDataSource") DataSource dataSource) throws Exception {
//        SqlSessionFactoryBean factory = new SqlSessionFactoryBean();
//        factory.setDataSource(dataSource);
//
//        // 设置MyBatis配置（可选）
//        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
//        configuration.setMapUnderscoreToCamelCase(true);
//        factory.setConfiguration(configuration);
//
//        // 设置Mapper XML位置（如果有）
//        factory.setMapperLocations(new PathMatchingResourcePatternResolver()
//                .getResources("classpath:mapper/*.xml"));
//
//        return factory.getObject();
//    }
//}
