package com.espc.sec.dataimport.task.service;

import com.espc.sec.dataimport.common.enums.TaskStatusEnum;
import com.espc.sec.dataimport.common.vo.PageVo;
import com.espc.sec.dataimport.task.dto.DataTaskDto;
import com.espc.sec.dataimport.task.dto.DataTaskReq;
import com.espc.sec.dataimport.task.vo.DataTaskVo;

/**
 * 数据任务服务接口
 */
public interface DataTaskService {
    /**
     * 创建任务
     *
     * @param DataTaskDto 任务DTO
     * @return 任务ID
     */
    Integer createTask(DataTaskDto DataTaskDto);

    /**
     * 更新任务
     *
     * @param DataTaskDto 任务DTO
     * @return 是否成功
     */
    Boolean updateTask(DataTaskDto DataTaskDto);

    /**
     * 删除任务
     *
     * @param id 任务ID
     * @return 是否成功
     */
    Boolean deleteTask(Integer id);

    /**
     * 查询所有任务
     *
     * @param dataTaskReq
     * @return 任务VO列表
     */
    PageVo<DataTaskVo> listAllTasks(DataTaskReq dataTaskReq);

    /**
     * 更新任务状态，供netty调用
     *
     * @param id
     * @param taskStatusEnum
     */
    void updateTaskStatus(Integer id, TaskStatusEnum taskStatusEnum);

    /**
     * 根据任务id查询名称
     * @param id
     * @return
     */
    String getTaskNameByIdFromMemoryCache(Integer id);
}
