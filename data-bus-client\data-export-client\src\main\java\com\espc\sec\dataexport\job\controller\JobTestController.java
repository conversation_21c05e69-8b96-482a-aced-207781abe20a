package com.espc.sec.dataexport.job.controller;


import com.espc.sec.dataexport.job.dto.JobInfo;
import com.espc.sec.dataexport.job.dto.TestTaskDto;
import com.espc.sec.dataexport.job.service.JobService;
import com.espc.sec.dataexport.job.util.JobManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/job")
public class JobTestController {
    @Autowired
    private JobManager jobManager;
    @Autowired
    private JobService jobService;

    @PostMapping
    public void createJob(@RequestBody JobInfo jobInfo) throws Exception {
        jobManager.createJob(jobInfo);
    }

    @PostMapping("create")
    public void create(@RequestBody TestTaskDto testTaskDto) throws Exception {
        jobService.addJob(testTaskDto.getJobId()
                , testTaskDto.getJobTypeEnum()
                , testTaskDto.getCron()
                , testTaskDto.getTaskProperties());
    }

    @PostMapping("update")
    public void update(@RequestBody TestTaskDto testTaskDto) throws Exception {
        jobService.updateJob(testTaskDto.getJobId()
                , testTaskDto.getJobTypeEnum()
                , testTaskDto.getCron()
                , testTaskDto.getTaskProperties());
    }

    @PostMapping("delete/{jobId}")
    public void delete(Integer id) throws Exception {
        jobService.deleteJob(id);
    }

    @PutMapping
    public void updateJob(@RequestBody JobInfo jobInfo) throws Exception {
        jobManager.updateJob(jobInfo);
    }

    @DeleteMapping("/{jobGroup}/{jobName}")
    public void deleteJob(@PathVariable String jobGroup, @PathVariable String jobName) throws Exception {
        jobManager.deleteJob(jobName, jobGroup);
    }

    @GetMapping("/{jobGroup}/{jobName}/pause")
    public void pauseJob(@PathVariable String jobGroup, @PathVariable String jobName) throws Exception {
        jobManager.pauseJob(jobName, jobGroup);
    }

    @GetMapping("/{jobGroup}/{jobName}/resume")
    public void resumeJob(@PathVariable String jobGroup, @PathVariable String jobName) throws Exception {
        jobManager.resumeJob(jobName, jobGroup);
    }

    @GetMapping("/{jobGroup}/{jobName}/state")
    public String getJobState(@PathVariable String jobGroup, @PathVariable String jobName) throws Exception {
        return jobManager.getJobState(jobName, jobGroup);
    }

    @GetMapping
    public List<JobInfo> getAllJobs() throws Exception {
        return jobManager.getAllJobs();
    }

}