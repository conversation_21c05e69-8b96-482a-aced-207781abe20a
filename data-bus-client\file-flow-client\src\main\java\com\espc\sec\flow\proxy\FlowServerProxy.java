package com.espc.sec.flow.proxy;

import com.espc.sec.flow.aspect.IStat;
import com.espc.sec.flow.aspect.impl.StatImpl;
import com.espc.sec.flow.bean.Constant;
import com.espc.sec.flow.bean.Job;
import com.espc.sec.flow.bean.Result;
import com.espc.sec.flow.conf.Config;
import com.espc.sec.flow.util.DateUtil;
import com.espc.sec.flow.util.IceUtil;
import com.espc.sec.flow.util.JsonUtil;
import com.espc.sec.flow.util.ZipUtil;
import com.ice.common.CommonServicePrx;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @date 2019-04-22
 */
@Data
@Slf4j
public class FlowServerProxy extends AbstractProxy {

    /**
     * 配置信息
     */
    private Config.FlowServersBean config;
    /**
     * 连接ice的代理
     */
    private CommonServicePrx proxy;
    /**
     * zip压缩文件的最大的大小
     */
    private long zipMaxSize;
    /**
     * 临时存放待压缩的任务
     */
    private List<Job> jobList;
    /**
     * 所有待压缩文件的总大小
     */
    private long totalSize;
    /**
     * 服务端是否连接断开
     */
    private AtomicBoolean isDisconnected;

    public FlowServerProxy(Config.FlowServersBean config, CommonServicePrx proxy) {
        super(config.getDirs(), config.getNodes(), config.getTypes());
        this.proxy = proxy;
        this.config = config;
        this.jobList = new ArrayList<>();
        this.totalSize = 0L;
        this.zipMaxSize = config.getZipFileMaxSize() * 1024 * 1024;
        this.isDisconnected = new AtomicBoolean(false);
    }


    @Override
    public boolean flowOneFile(Job job) {
        long start = System.currentTimeMillis();
        if (!isDisconnected.get()) {
            if (!needFlow(job, config.isNeedSpecialFile())) {
                log.debug(job.getAbsolutePath() + " need not flow in FlowServerProxy.");
                return true;
            }
            if (proxy != null) {
                boolean b;
                if (config.isNeedZip()) {
                    b = zipSend(job);
                } else {
                    b = normalSend(job);
                }
                if (b) {
                    String fileName = job.getFileName();
                    String[] names = fileName.split("_");
                    if (names.length == standardFileNameLen) {
                        String node = names[1];
                        String type = names[2];
                        StatImpl.statOneFile(IStat.FLOW_SERVER_STAT_MAP, node, type, job.getContent().length);
                    }
                    log.info("huiju flow one success. name:" + fileName +
                            ", cost:" + (System.currentTimeMillis() - start));
                }
                return b;
            } else {
                log.error("proxy is null.");
                return false;
            }
        } else {
            log.error("FileFlowServer proxy is disconnected.");
            return false;
        }
    }

    /**
     * 发送压缩文件
     *
     * @param job 任务
     * @return 返回是否成功
     */
    private boolean zipSend(Job job) {
        List<Job> tempJobList = null;
        synchronized (this) {
            totalSize += job.getContent().length;
            jobList.add(job);
            if (totalSize >= zipMaxSize) {
                tempJobList = jobList;
                jobList = new ArrayList<>();
                totalSize = 0L;
            }
        }
        if (tempJobList != null) {
            if (!this.isDisconnected.get()) {
                try {
                    byte[] content = ZipUtil.zipJobList(tempJobList);
                    String zipFileName = DateUtil.getNow() + "_" + UUID.randomUUID().toString() + ".xzip";
                    String req = "{\"fileName\":\"" + zipFileName + "\",\"type\":\"xzip\"}";
                    String resultStr = proxy.requestWithByteIn(Constant.FLAG_SEND_ZIP_FILE, req, content);
                    Result result = (Result) JsonUtil.jsonToObject(resultStr, Result.class);
                    if (Result.STATUS_OK != result.getStatus()) {
                        log.error("send zip file failed, msg:" + result.getMessage());
                        return false;
                    }
                    return true;
                } catch (Ice.ConnectionRefusedException e1) {
                    this.isDisconnected.set(true);
                    log.error("send zip file failed. FileFlowServer proxy is disconnected.", e1);
                    new Thread(new Reconnecter()).start();
                    return false;
                } catch (Throwable e) {
                    log.error("send zip file failed.", e);
                    return false;
                }
            } else {
                log.error("send zip file failed. FileFlowServer proxy is disconnected.");
                return false;
            }
        } else {
            return true;
        }
    }

    /**
     * 发送非压缩文件
     *
     * @param job 任务
     * @return 返回是否发送成功
     */
    private boolean normalSend(Job job) {
        if (!this.isDisconnected.get()) {
            try {
                String req = "{\"filePath\":\"" + job.getFilePath() + "\"}";
                String resultStr = proxy.requestWithByteIn(Constant.FLAG_SEND_NORMAL_FILE, req, job.getContent());
                Result result = (Result) JsonUtil.jsonToObject(resultStr, Result.class);
                if (Result.STATUS_OK != result.getStatus()) {
                    log.error("send file failed, file:" + job.getFilePath() + ", msg:" + result.getMessage());
                    return false;
                }
                return true;
            } catch (Ice.ConnectionRefusedException e1) {
                this.isDisconnected.set(true);
                log.error("send normal file failed. FileFlowServer proxy is disconnected.", e1);
                new Thread(new Reconnecter()).start();
                return false;
            } catch (Throwable e) {
                log.error("send normal file failed.", e);
                return false;
            }
        } else {
            log.error("send normal file failed. FileFlowServer proxy is disconnected.");
            return false;
        }
    }

    /**
     * 重新连接服务端的内部类
     */
    class Reconnecter implements Runnable {
        @Override
        public void run() {
            CommonServicePrx commonServicePrx = IceUtil.getFlowServerProxy(config.getIp(), config.getPort());
            while (commonServicePrx == null) {
                try {
                    Thread.sleep(10000);
                } catch (InterruptedException e) {
                }
                commonServicePrx = IceUtil.getFlowServerProxy(config.getIp(), config.getPort());
            }
            proxy = commonServicePrx;
            isDisconnected.set(false);
        }
    }
}
