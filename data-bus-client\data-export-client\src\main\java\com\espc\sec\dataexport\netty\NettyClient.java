package com.espc.sec.dataexport.netty;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.netty.handler.HeartbeatTrigger;
import com.espc.sec.dataexport.netty.handler.MessageDecoder;
import com.espc.sec.dataexport.netty.handler.MessageEncoder;
import com.espc.sec.dataexport.netty.handler.NettyClientHandler;
import com.espc.sec.dataexport.netty.utils.RetryPolicy;
import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.timeout.IdleStateHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025/7/16
 **/
@Slf4j
public class NettyClient implements Runnable {

    public static void main(String[] args) {
        new Thread(new NettyClient()).start();
    }

    @Override
    public void run() {
        String host = Config.commonConfig.getTaskServerIp();
        int port = Config.commonConfig.getTaskServerPort();
        EventLoopGroup group = new NioEventLoopGroup();
        RetryPolicy retryPolicy = new RetryPolicy(10000, 1000, 30000);
        int retryCount = 0;
        while (retryPolicy.shouldRetry(retryCount)) {
            try {
                Bootstrap b = new Bootstrap();
                b.group(group)
                        .channel(NioSocketChannel.class)
                        .option(ChannelOption.TCP_NODELAY, true)
                        .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5000)
                        .handler(new ChannelInitializer<SocketChannel>() {
                            @Override
                            protected void initChannel(SocketChannel socketChannel) {
                                ChannelPipeline pipeline = socketChannel.pipeline();
                                // 空闲检测
                                pipeline.addLast(new IdleStateHandler(0, 30, 0, TimeUnit.SECONDS));
                                // 消息编码解码
                                pipeline.addLast(new MessageDecoder());
                                pipeline.addLast(new MessageEncoder());
                                // 业务处理
                                pipeline.addLast(new HeartbeatTrigger());
                                pipeline.addLast(new NettyClientHandler());
                            }
                        });

                ChannelFuture f = b.connect(host, port).sync();
                log.info("NettyClient 启动成功, 端口号: {}", port);

                f.channel().closeFuture().sync();
            } catch (Exception e) {
                retryCount++;
                if (!retryPolicy.shouldRetry(retryCount)) {
                    log.error("连接服务器失败，已达到最大重试次数 {} 次", retryPolicy.getMaxRetries(), e);
                    break;
                }

                long delay = retryPolicy.getNextDelay(retryCount);
                log.warn("连接服务器失败，第 {} 次重试将在 {} 毫秒后执行...", retryCount, delay, e);

                try {
                    Thread.sleep(delay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.error("重试等待被中断", ie);
                    break;
                }
            } finally {
                if (!retryPolicy.shouldRetry(retryCount)) {
                    group.shutdownGracefully();
                }
            }
        }
    }
}
