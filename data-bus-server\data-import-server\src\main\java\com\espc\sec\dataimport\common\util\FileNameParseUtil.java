package com.espc.sec.dataimport.common.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文件名解析工具类
 * 
 * <AUTHOR>
 * @date 2025-07-25
 */
@Slf4j
public class FileNameParseUtil {

    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMddHHmmssSSS");

    /**
     * 解析StarRocks文件名
     * 新格式：{mode}_{taskId}_{time}_{node}_{type}_{database}_{table}_{index}.log
     * 
     * @param fileName 文件名
     * @return 解析结果
     */
    public static FileNameInfo parseStarRocksFileName(String fileName) {
        String nameWithoutExt = fileName.replace(".log", "").replace(".sql", "");
        String[] parts = nameWithoutExt.split("_");

        FileNameInfo info = new FileNameInfo();
        info.setExportMode(Integer.valueOf(parts[0]));
        info.setTaskId(Integer.valueOf(parts[1]));
        info.setTimestamp(parts[2]);
        info.setNodeName(parts[3]);
        info.setDatabaseType(parts[4]);
        info.setTableName(parts[6]+"_"+parts[7]);
        info.setFileName(fileName);

        return info;
    }

    /**
     * 解析Elasticsearch文件名
     * 新格式：{mode}_{taskId}_{time}_{node}_{type}_{index}_{shard}.log
     * 
     * @param fileName ��件名
     * @return 解析结果
     */
    public static FileNameInfo parseElasticsearchFileName(String fileName) {
        String nameWithoutExt = fileName.replace(".log", "");
        String[] parts = nameWithoutExt.split("_");

        FileNameInfo info = new FileNameInfo();
        info.setExportMode(Integer.valueOf(parts[0]));
        info.setTaskId(Integer.valueOf(parts[1]));
        info.setTimestamp(parts[2]);
        info.setNodeName(parts[3]);
        info.setDatabaseType(parts[4]);
        info.setTableName(parts[5]);
        info.setFileName(fileName);

        return info;
    }

    /**
     * 解析TiDB文件名
     * 新格式：{mode}_{taskId}_{time}_{node}_{type}_{database}_{table}_{index}.log
     *
     * @param fileName 文件名
     * @return 解析结果
     */
    public static FileNameInfo parseTidbFileName(String fileName) {
        String nameWithoutExt = fileName.replace(".log", "").replace(".sql", "");
        String[] parts = nameWithoutExt.split("_");

        FileNameInfo info = new FileNameInfo();
        info.setExportMode(Integer.valueOf(parts[0]));
        info.setTaskId(Integer.valueOf(parts[1]));
        info.setTimestamp(parts[2]);
        info.setNodeName(parts[3]);
        info.setDatabaseType(parts[4]);
        info.setTableName(parts[6]+"_"+parts[7]);
        info.setFileName(fileName);

        return info;
    }

    /**
     * 解析Kafka文件名
     * 新格式：{mode}_{taskId}_{time}_{node}_{type}_{topic}_{index}.log
     * 
     * @param fileName 文件名
     * @return 解析结果
     */
    public static FileNameInfo parseKafkaFileName(String fileName) {
        String nameWithoutExt = fileName.replace(".log", "");
        String[] parts = nameWithoutExt.split("_");

        FileNameInfo info = new FileNameInfo();
        info.setExportMode(Integer.valueOf(parts[0]));
        info.setTaskId(Integer.valueOf(parts[1]));
        info.setTimestamp(parts[2]);
        info.setNodeName(parts[3]);
        info.setDatabaseType(parts[4]);
        info.setTableName(parts[5]);
        info.setFileName(fileName);

        return info;
    }

    /**
     * 将时间戳字符串转换为标准时间格式
     * 
     * @param timestamp 时间戳字符串 (yyyyMMddHHmmssSSS)
     * @return 标准时间格式字符串 (yyyy-MM-dd HH:mm:ss)
     */
    public static String formatTimestamp(String timestamp) {
        try {
            DateTime yyyyMMddHHmmssSSS = DateUtil.parse(timestamp, "yyyyMMddHHmmssSSS");
            return String.valueOf(yyyyMMddHHmmssSSS);
        } catch (Exception e) {
            log.error("格式化时间戳失败: {}", timestamp, e);
            return timestamp;
        }
    }


    /**
     * 文件名信息类
     */
    @Data
    public static class FileNameInfo {

        private Integer exportMode;

        private Integer taskId;
        /**
         * 时间戳字符串
         */
        private String timestamp;
        
        /**
         * 节点名称
         */
        private String nodeName;
        
        /**
         * 数据库类型
         */
        private String databaseType;
        
        /**
         * 表名/索引名/Topic名
         */
        private String tableName;
        
        /**
         * 文件名
         */
        private String fileName;
        
        /**
         * 获取格式化的时间字符串
         */
        public String getFormattedTime() {
            return formatTimestamp(timestamp);
        }
    }
}