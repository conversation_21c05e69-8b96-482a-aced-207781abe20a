package com.espc.sec.flow.util;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;

import static com.fasterxml.jackson.databind.DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT;

/**
 * <AUTHOR>
 * @date 2018-06-04
 */
public class ConfigUtil {
    public static Config config;
    public static int threadSoldPercent;
    public static String targetFileSystem;

    /**
     * 加载配置文件
     *
     * @return
     * @throws Exception
     */
    public static void initConfig() throws Exception {
        ObjectMapper objectMapper = objectMapper();
        config = objectMapper.readValue(new File("conf/FileFlowServer/conf/config.json"), Config.class);

        String acceptThreadSold = config.getAcceptThreadSold();
        if (acceptThreadSold == null || acceptThreadSold.isEmpty()) {
            throw new Exception("no config acceptThreadSold");
        }
        threadSoldPercent = getThreadSoldPercent(acceptThreadSold);
        if (threadSoldPercent < 0) {
            throw new Exception("config acceptThreadSold is invalid");
        }

        config.setTargetDir(new File(config.getTargetDir()).getAbsolutePath());
        config.setTempDir(new File(config.getTempDir()).getAbsolutePath());

        targetFileSystem = getFileSystem(config.getTargetDir());
    }

    private static ObjectMapper objectMapper() {
        JsonFactory factory = new JsonFactory();
        factory.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        ObjectMapper objectMapper = new ObjectMapper(factory);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        return objectMapper;
    }

    private static int getThreadSoldPercent(String threadSold) {
        String percent = threadSold.replace("%", "");
        try {
            return Integer.parseInt(percent);
        } catch (Exception e) {
            return -1;
        }
    }

    private static String getFileSystem(String path) {
        String s = path.substring(0, 2);
        return s + "\\";
    }

}