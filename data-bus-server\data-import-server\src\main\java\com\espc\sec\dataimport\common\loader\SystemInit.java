package com.espc.sec.dataimport.common.loader;


import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.constant.LogKeyword;
import com.espc.sec.dataimport.common.enums.ImportTypeEnum;
import com.espc.sec.dataimport.common.util.FileUtils;
import com.espc.sec.dataimport.hdfs.helper.HdfsServiceHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Comparator;

@Component
@Slf4j
public class SystemInit {

    @Autowired
    @Qualifier("mysqlJdbcTemplate")
    private JdbcTemplate jdbcTemplate;
    @Value("classpath:sql/*.sql")
    private Resource[] sqlResources;

    @PostConstruct
    public void init() {
        initDirectory();
        initHdfs();
        initMysqlDb();
    }

    /**
     * 初始化导出必要目录
     */
    private void initDirectory() {
        log.info("导入目录初始化开始");
        String importPath = Config.commonConfig.getImportPath();
        String importErrorPath = Config.commonConfig.getImportErrorPath();
        for (ImportTypeEnum importTypeEnum : ImportTypeEnum.values()) {
            FileUtils.createDirIfNotExists(importPath + File.separator + importTypeEnum.getCode());
            FileUtils.createDirIfNotExists(importErrorPath + File.separator + importTypeEnum.getCode());
        }
        log.info("导入目录初始化结束");
    }

    private void initHdfs() {
        log.info(LogKeyword.HDFS_IMPORT + "hdfs初始化开始");
        try {
            HdfsServiceHelper.init("hdfs://nnCluster");
        } catch (IOException e) {
            log.error(LogKeyword.HDFS_IMPORT + "hdfs初始化失败");
        }
        log.info(LogKeyword.HDFS_IMPORT + "hdfs初始化完成");
    }

    /**
     * mysql语句初始化
     */
    private void initMysqlDb() {
        try {
            Arrays.sort(sqlResources, Comparator.comparing(Resource::getFilename));
            for (Resource resource : sqlResources) {
                String sqlContent = new String(Files.readAllBytes(Paths.get(resource.getURI())), StandardCharsets.UTF_8);

                String[] sqlStatements = sqlContent.split(";\\s*\\n");

                for (String sql : sqlStatements) {
                    if (!sql.trim().isEmpty()) {
                        try {
                            jdbcTemplate.execute(sql);
                            log.info("mysql语句初始化成功,{}", sql.trim());
                        } catch (DataAccessException e) {
                            log.error("Error executing SQL from {}: {}", resource.getFilename(), sql.trim(), e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("mysql表初始化失败", e);
        }
    }


}
