package com.espc.sec.flow.util;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;

/**
 * <AUTHOR>
 * @date 2018-11-22
 */
@Slf4j
public class IoUtil {
    /**
     * 把输入流转换成字符串
     *
     * @param input   输入流
     * @param charset 编码
     * @return 字符串
     * @throws IOException 抛出异常
     */
    public static String inputStreamToString(InputStream input, String charset) throws IOException {
        if (input == null) {
            return null;
        } else {
            InputStreamReader inputStreamReader = new InputStreamReader(input, charset);
            BufferedReader reader = new BufferedReader(inputStreamReader);
            String line = "";
            StringBuilder sb = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                sb.append(line).append("\r\n");
            }
            reader.close();
            inputStreamReader.close();
            return sb.toString();
        }
    }


    /**
     * 向文件中写内容
     *
     * @param file     文件
     * @param content  写入的内容
     * @param encode   编码
     * @param isAppend 是否追加，true追加，false不追加
     * @throws IOException 抛出异常
     */
    public static void writeStringToFile(File file, String content, String encode, boolean isAppend) throws IOException {
        FileOutputStream outputStream = new FileOutputStream(file, isAppend);
        OutputStreamWriter outputStreamWriter = new OutputStreamWriter(outputStream, encode);
        PrintWriter writer = new PrintWriter(outputStreamWriter);
        writer.write(content);

        writer.close();
        outputStreamWriter.close();
        outputStream.close();
    }

    /**
     * 向文件中写内容
     *
     * @param filePath 文件路径
     * @param content  文件内容
     * @param encode   编码
     * @param isAppend 是否追加，true追加，false不追加
     * @throws IOException 抛出异常
     */
    public static void writeStringToFile(String filePath, String content, String encode, boolean isAppend) throws IOException {
        File file = new File(filePath);
        writeStringToFile(file, content, encode, isAppend);
    }

    /**
     * 把文件读取成字节流
     *
     * @param filePath 文件路径
     * @return 返回字节流
     * @throws IOException 抛出异常
     */
    public static byte[] readFileToBinary(String filePath) throws IOException {
        File file = new File(filePath);
        return readFileToBinary(file);
    }

    /**
     * 把文件读取成字节流
     *
     * @param file 文件
     * @return 返回字节流
     * @throws IOException 抛出异常
     */
    public static byte[] readFileToBinary(File file) throws IOException {
        FileInputStream in = null;
        try {
            byte[] content = new byte[(int) file.length()];
            in = new FileInputStream(file);
            in.read(content);
            in.close();
            return content;
        } finally {
            if (in != null) {
                in.close();
            }
        }
    }

    /**
     * 把二进制流写入到文件
     *
     * @param b    二进制流
     * @param file 文件
     * @throws IOException 抛出异常
     */
    public static void writeBinaryToFile(byte[] b, File file) throws IOException {
        FileOutputStream outputStream = null;
        try {
            File parent = file.getParentFile();
            if (!parent.exists()) {
                parent.mkdirs();
            }
            outputStream = new FileOutputStream(file);
            outputStream.write(b);
            outputStream.close();
        } finally {
            if (outputStream != null) {
                outputStream.close();
            }
        }
    }

    /**
     * 得到文件内容，转换成规定编码的字符串
     *
     * @param filePath 文件路径
     * @param encoding 文件的编码
     * @return 返回文件内容
     * @throws IOException 抛出异常
     */
    public static String readFileToString(String filePath, String encoding) throws IOException {
        byte[] bytes = readFileToBinary(filePath);
        return new String(bytes, encoding);
    }

    /**
     * 得到文件内容，转换成规定编码的字符串
     *
     * @param file     文件
     * @param encoding 文件编码
     * @return 返回文件内容
     * @throws IOException 抛出异常
     */
    public static String readFileToString(File file, String encoding) throws IOException {
        byte[] bytes = readFileToBinary(file);
        return new String(bytes, encoding);
    }
}
