package com.espc.sec.dataexport.monitor.task.dto;

import lombok.Data;

/**
 * 监控任务数据基础请求对象
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
public class MonitorTaskDataBaseReq {

    /**
     * 节点
     */
    private String node;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 数据库类型
     */
    private String databaseType;

    /**
     * 表名称
     */
    private String tableName;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 文件名
     */
    private String fileName;
}