package com.espc.sec.dataexport.monitor.task.dto;

import lombok.Data;

/**
 * 监控任务数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
public class MonitorTaskDataDto {

    /**
     * 自增id
     */
    private Integer id;

    /**
     * 节点
     */
    private String node;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 数据库类型
     */
    private String databaseType;

    /**
     * 表名称
     */
    private String tableName;

    /**
     * 导出或者导入条数
     */
    private Long size;

    /**
     * 导出或者导入时间
     */
    private String time;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 文件名
     */
    private String fileName;
}