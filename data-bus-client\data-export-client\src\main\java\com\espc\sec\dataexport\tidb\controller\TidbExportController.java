package com.espc.sec.dataexport.tidb.controller;

import com.espc.sec.dataexport.common.dto.task.TidbTaskProperties;
import com.espc.sec.dataexport.tidb.service.TidbExportServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * TiDB导出控制器
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Slf4j
@RestController
@RequestMapping("/api/export/tidb")
public class TidbExportController {

    @Autowired
    private TidbExportServiceImpl tidbExportServiceImpl;

    /**
     * 根据参数导出TiDB数据
     *
     * @param request 导出请求参数
     */
    @PostMapping("/export")
    public void exportData(@RequestBody TidbTaskProperties request) {
        tidbExportServiceImpl.taskExport(request);
    }
}