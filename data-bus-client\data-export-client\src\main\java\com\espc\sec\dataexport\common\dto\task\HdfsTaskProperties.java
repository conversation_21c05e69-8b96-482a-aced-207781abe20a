package com.espc.sec.dataexport.common.dto.task;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class HdfsTaskProperties extends BaseTaskProperties {
    @NotBlank(message = "导出开始时间不能为空")
    private String dataStartTime;
    @NotBlank(message = "导出结束时间不能为空")
    private String dataEndTime;
    @NotNull(message = "导出条数不能为空")
    private Integer dataSize;
}
