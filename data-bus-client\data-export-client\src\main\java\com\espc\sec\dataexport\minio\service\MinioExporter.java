package com.espc.sec.dataexport.minio.service;

import cn.hutool.core.date.DateUtil;
import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.service.impl.AbstractExporter;
import com.espc.sec.dataexport.minio.dto.MinioExportDto;
import com.espc.sec.dataexport.minio.helper.MinIOServiceHelper;
import com.espc.sec.dataexport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataexport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataexport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataexport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataexport.task.service.DataTaskService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class MinioExporter extends AbstractExporter<MinioExportDto> {

    @Autowired
    private MonitorIncrementDataService monitorIncrementDataService;

    @Autowired
    private MonitorTaskDataService monitorTaskDataService;

    @Autowired
    private DataTaskService dataTaskService;

    @Override
    protected List<File> doExport(MinioExportDto dto) throws Exception {

        String millsDateStr = DateUtil.format(new Date(), Constants.MILLS_FORMAT);

        boolean isTaskType = dto.getExportModeEnum().equals(ExportModeEnum.TASK);

        String nodeFileName = dto.getExportModeEnum().getCode()
                + "_"
                + (isTaskType ? dto.getTaskId() : 0)
                + "_"
                + millsDateStr
                + "_"
                + Config.commonConfig.getNodeName()
                + "_"
                + dto.getObjectName();
        String tempFileAbsName = dto.getExportTempPath() + File.separator + nodeFileName;

        MinIOServiceHelper.downloadObject(dto.getBucketName(), dto.getObjectName(), tempFileAbsName);

        return Lists.newArrayList(new File(tempFileAbsName));
    }

    @Override
    protected void saveImportLogToMysql(MinioExportDto exporter, File tempFile) throws Exception {
        if (exporter.getExportModeEnum().equals(ExportModeEnum.INCREMENT)) {
            monitorIncrementDataService.create(MonitorIncrementDataDto.builder()
                    .node(Config.commonConfig.getNodeName())
                    .size(tempFile.length())
                    .databaseType(exporter.getExportTypeEnum().getCode())
                    .fileName(exporter.getObjectName())
                    .tableName(exporter.getBucketName())
                    .time(new Date())
                    .build());
        } else {
            String taskName = dataTaskService.getTaskNameByIdFromMemoryCache(exporter.getTaskId());

            MonitorTaskDataAddReq monitorTaskDataAddReq = new MonitorTaskDataAddReq();
            monitorTaskDataAddReq.setTaskName(taskName);
            monitorTaskDataAddReq.setDatabaseType(exporter.getExportTypeEnum().getCode());
            monitorTaskDataAddReq.setNode(Config.commonConfig.getNodeName());
            monitorTaskDataAddReq.setSize(tempFile.length());
            monitorTaskDataAddReq.setFileName(tempFile.getName());
            monitorTaskDataAddReq.setTableName(exporter.getBucketName());
            monitorTaskDataAddReq.setTime(DateUtil.format(new Date(), Constants.MILLS_FORMAT));
            monitorTaskDataService.add(monitorTaskDataAddReq);
        }


    }
}
