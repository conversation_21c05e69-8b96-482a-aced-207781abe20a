package com.espc.sec.start;

import cn.hutool.core.date.StopWatch;
import com.espc.sec.dataimport.DataImportApp;
import com.espc.sec.flow.FileFlowServerMain;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2025/6/28
 **/
@SpringBootApplication(scanBasePackages = {"com.espc.sec"})
@EnableScheduling
@EnableAsync
@Slf4j
public class DataBusServer {
    public static void main(String[] args) {
        try {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();

            // 启动数据导入模块
            DataImportApp.start();
            // 启动Spring Boot应用
            SpringApplication.run(DataBusServer.class, args);
            // 启动数据通道模块
            FileFlowServerMain.start();

            // 启动后进行配置诊断
            // ConfigDiagnosticUtil diagnosticUtil = context.getBean(ConfigDiagnosticUtil.class);
            // diagnosticUtil.printConfigurationSummary();
            // diagnosticUtil.diagnoseAllConfigurations();

            stopWatch.stop();
            log.info("DataBusServer has been started successfully, it takes {} milliseconds", stopWatch.getTotalTimeMillis());
        } catch (Exception e) {
            log.error("DataBusServer 启动失败", e);
            Runtime.getRuntime().halt(0);
        }
    }
}
