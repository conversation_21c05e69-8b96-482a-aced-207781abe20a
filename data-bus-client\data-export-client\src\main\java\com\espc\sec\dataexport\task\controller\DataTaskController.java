package com.espc.sec.dataexport.task.controller;


import com.espc.sec.dataexport.common.vo.PageVo;
import com.espc.sec.dataexport.common.vo.ResultVo;
import com.espc.sec.dataexport.task.dto.DataTaskDto;
import com.espc.sec.dataexport.task.dto.DataTaskReq;
import com.espc.sec.dataexport.task.service.DataTaskService;
import com.espc.sec.dataexport.task.vo.DataTaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 数据任务控制器
 */
@RestController
@RequestMapping("/task")
@Api(tags = "数据任务管理")
public class DataTaskController {

    @Resource
    private DataTaskService dataTaskService;

    @PostMapping
    @ApiOperation("创建任务")
    public ResultVo<Integer> createTask(@RequestBody @Validated DataTaskDto dataTaskDto) {
        Integer taskId = dataTaskService.createTask(dataTaskDto);
        return ResultVo.success(taskId);
    }

    @PutMapping
    @ApiOperation("更新任务")
    public ResultVo<Boolean> updateTask(@RequestBody @Validated DataTaskDto dataTaskDto) {
        Boolean resultVo = dataTaskService.updateTask(dataTaskDto);
        return ResultVo.success(resultVo);
    }

    @DeleteMapping("/{id}")
    @ApiOperation("删除任务")
    public ResultVo<Boolean> deleteTask(@PathVariable Integer id) {
        Boolean result = dataTaskService.deleteTask(id);
        return ResultVo.success(result);
    }


    @PostMapping("/list")
    @ApiOperation("查询所有任务")
    public ResultVo<PageVo<DataTaskVo>> listAllTasks(@RequestBody DataTaskReq dataTaskReq) {
        PageVo<DataTaskVo> taskList = dataTaskService.pageTasks(dataTaskReq);
        return ResultVo.success(taskList);
    }

}
