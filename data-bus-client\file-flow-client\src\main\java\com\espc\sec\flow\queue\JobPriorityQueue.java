package com.espc.sec.flow.queue;

import com.espc.sec.flow.bean.Job;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @date 2019-04-19
 */
@Data
@Slf4j
public class JobPriorityQueue extends PriorityBlockingQueue<Job> {
    /**
     * 优先级队列的最大长度
     */
    private int maxSize;
    final ReentrantLock lock;
    private final Condition notFull;


    public JobPriorityQueue(int size) {
        super(size);
        this.lock = new ReentrantLock(false);
        this.notFull = lock.newCondition();
        this.maxSize = size;
    }

    @Override
    public void put(Job job) throws NullPointerException {
        if (job == null) {
            throw new NullPointerException();
        }
        final ReentrantLock lock = this.lock;
        lock.lock();
        try {
            while (super.size() >= maxSize) {
                notFull.await();
            }
            super.put(job);
            log.info("put one job success. queue size:" + super.size());
        } catch (Exception e) {
            log.error("put job into queue failed. file path:" + job.getFilePath());
        } finally {
            lock.unlock();
        }
    }

    @Override
    public Job poll(long timeout, TimeUnit unit) throws InterruptedException {
        Job job = super.poll(timeout, unit);
        if (job != null) {
            final ReentrantLock lock = this.lock;
            lock.lock();
            try {
                notFull.signal();
                log.info("pool one job success. queue size:" + super.size());
            } finally {
                lock.unlock();
            }
        } else {
            log.info("pool one job is null. queue size=0");
        }
        return job;
    }
}
