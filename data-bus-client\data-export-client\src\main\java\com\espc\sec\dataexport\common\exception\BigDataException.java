package com.espc.sec.dataexport.common.exception;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/18
 **/
@Data
public class BigDataException extends RuntimeException {

    private String msg;
    private int code = 500;

    public BigDataException(String msg) {
        super(msg);
        this.msg = msg;
    }

    public BigDataException(int code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }
}
