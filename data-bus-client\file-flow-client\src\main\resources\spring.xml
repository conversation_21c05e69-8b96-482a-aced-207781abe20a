<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
   http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
   http://www.springframework.org/schema/context
   http://www.springframework.org/schema/context/spring-context.xsd
http://www.springframework.org/schema/aop
http://www.springframework.org/schema/aop/spring-aop-3.0.xsd ">


    <context:annotation-config/>
    <bean id="stat" class="com.espc.sec.flow.aspect.impl.StatImpl"/>
    <bean id="queuePutter" class="com.espc.sec.flow.queue.QueuePutter"/>

    <!--强制使用CGLIB来创建代理-->
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <aop:config>
        <aop:aspect id="aspectStatFlow" ref="stat">
            <aop:pointcut id="putOneFileIntoQueue" expression=
                    "execution(* com.espc.sec.flow.queue.QueuePutter.putOneFileIntoQueue(..))"/>
            <aop:after method="statFlow" pointcut-ref="putOneFileIntoQueue"/>
        </aop:aspect>
    </aop:config>

</beans>