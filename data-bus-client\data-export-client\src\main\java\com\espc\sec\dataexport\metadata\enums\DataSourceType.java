package com.espc.sec.dataexport.metadata.enums;

/**
 * 数据源类型枚举
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
public enum DataSourceType {
    KAFKA("kafka", "Kafka消息队列"),
    ELASTICSEARCH("elasticsearch", "Elasticsearch搜索引擎"),
    STARROCKS("starrocks", "StarRocks数据库"),
    TIDB("tidb", "TiDB数据库");

    private final String code;
    private final String description;

    DataSourceType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}