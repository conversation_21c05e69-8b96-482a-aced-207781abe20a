package com.espc.sec.dataexport.job.task;

import com.espc.sec.dataexport.common.dto.task.ElasticsearchTaskProperties;
import com.espc.sec.dataexport.common.util.SpringBeanUtil;
import com.espc.sec.dataexport.elasticsearch.service.ElasticsearchExportServiceImpl;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EsTask extends AbstractTask<ElasticsearchTaskProperties> {

    @Override
    protected void doExecute(ElasticsearchTaskProperties esProperties) throws Exception {
        log.info("收到es配置数据,{}", esProperties);
        ElasticsearchExportServiceImpl exportService = SpringBeanUtil.getBean(ElasticsearchExportServiceImpl.class);
        exportService.taskExport(esProperties);
    }
}
