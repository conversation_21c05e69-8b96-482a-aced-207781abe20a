package com.espc.sec.dataimport.starrocks.schedule;

import com.espc.sec.dataimport.common.constant.LogKeyword;
import com.espc.sec.dataimport.common.enums.ImportTypeEnum;
import com.espc.sec.dataimport.minio.service.MinioImporter;
import com.espc.sec.dataimport.starrocks.service.StarRocksImporter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class StarRocksScheduler {
    @Autowired
    private MinioImporter minioImporter;

    @Autowired
    private StarRocksImporter starRocksImporter;

    @Scheduled(fixedDelayString = "#{T(com.espc.sec.dataimport.common.config.Config).importConfig.starRocks.getFixedDelayMills()}")
    public void run() {
        log.info(LogKeyword.STARROCKS_IMPORT + "定时任务开始");
        starRocksImporter.import0(ImportTypeEnum.STAR_ROCKS);
    }
}
