package com.espc.sec.dataexport.elasticsearch.controller;

import com.espc.sec.dataexport.common.dto.task.ElasticsearchTaskProperties;
import com.espc.sec.dataexport.elasticsearch.service.ElasticsearchExportServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Elasticsearch导出控制器
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/export/elasticsearch")
public class ElasticsearchExportController {

    @Autowired
    private ElasticsearchExportServiceImpl elasticsearchExportServiceImpl;

    /**
     * 根据参数导出Elasticsearch数据
     *
     * @param request 导出请求参数
     * @return 导出结果
     */
    @PostMapping("/export")
    public void exportData(@RequestBody ElasticsearchTaskProperties request) {
        elasticsearchExportServiceImpl.taskExport(request);
    }


}