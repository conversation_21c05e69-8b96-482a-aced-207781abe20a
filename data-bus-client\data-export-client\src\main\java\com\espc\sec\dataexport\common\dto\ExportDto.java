package com.espc.sec.dataexport.common.dto;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.File;

/**
 * 导出参数父类
 * <AUTHOR>
 */
@Getter
@Setter
public class ExportDto {
    /**
     * 导出类型必传
     */
    private ExportTypeEnum exportTypeEnum;
    /**
     * 导出模式必传
     */
    private ExportModeEnum exportModeEnum;
    /**
     * 导出模式为任务型时必传
     */
    private Integer taskId;

    public String getExportPath() {
        return Config.commonConfig.getOutputPath() + File.separator + exportTypeEnum.getCode();
    }

    public String getExportTempPath() {
        return Config.commonConfig.getOutputTempPath() + File.separator + exportTypeEnum.getCode();
    }
}
