package com.espc.sec.dataexport.tidb.task;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.tidb.service.TidbExportServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * TiDB导出定时任务
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Slf4j
@Service
public class TidbScheduler {

    @Autowired
    private TidbExportServiceImpl tidbExportServiceImpl;

    @Scheduled(cron = "#{T(com.espc.sec.dataexport.common.config.Config).exportConfig.tidb.getCron()}")
    public void scheduledIncrementalExport() {
        if (!Config.exportConfig.tidb.getIncrementEnable()) {
            return;
        }
        log.info("{}定时任务开始", LogKeyword.TIDB_EXPORT);
        tidbExportServiceImpl.incrementExport();
        log.info("{}定时任务结束", LogKeyword.TIDB_EXPORT);
    }
}