package com.espc.sec.dataexport.common.config.configuration;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.config.Environment;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * 时间戳数据库配置
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Slf4j
@Configuration
public class MysqlConfiguration {

    @Bean("mysqlJdbcTemplate")
    public JdbcTemplate timestampJdbcTemplate() {
        return new JdbcTemplate(timestampDataSource());
    }

    /**
     * 时间戳数据库数据源
     */
    @Bean("mysqlDataSource")
    public DataSource timestampDataSource() {
        Environment.MysqlDTO config = Config.environment.mysql;
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(config.getUrl());
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        hikariConfig.setDriverClassName("com.mysql.cj.jdbc.Driver");

        hikariConfig.setMaximumPoolSize(5);
        hikariConfig.setMinimumIdle(2);
        hikariConfig.setConnectionTimeout(30000L);
        hikariConfig.setIdleTimeout(600000L);
        hikariConfig.setMaxLifetime(1800000L);

        hikariConfig.setPoolName("MysqlHikariPool");

        return new HikariDataSource(hikariConfig);
    }

    @Bean
    public PlatformTransactionManager transactionManager(@Qualifier("mysqlDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}