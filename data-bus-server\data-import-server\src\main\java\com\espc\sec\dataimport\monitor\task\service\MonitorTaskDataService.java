package com.espc.sec.dataimport.monitor.task.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.espc.sec.dataimport.common.vo.PageVo;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataBaseReq;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataReq;
import com.espc.sec.dataimport.monitor.task.vo.MonitorTaskDataAggregateVo;
import com.espc.sec.dataimport.monitor.task.vo.MonitorTaskDataVo;

import java.util.List;

/**
 * 监控任务数据服务接口
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
public interface MonitorTaskDataService {

    /**
     * 新增监控任务数据
     *
     * @param req 新增请求
     * @return 是否成功
     */
    Boolean add(MonitorTaskDataAddReq req);

    /**
     * 列表查询监控任务数据（不分页）
     *
     * @param req 查询条件
     * @return 列表结果
     */
    List<MonitorTaskDataVo> getList(MonitorTaskDataBaseReq req);

    /**
     * 聚合查询监控任务数据
     * 根据node、task_name、database_type、table_name分组汇总size
     *
     * @param req 查询条件
     * @return 聚合结果
     */
    List<MonitorTaskDataAggregateVo> getAggregateList(MonitorTaskDataBaseReq req);


    /**
     * 分页查询监控任务数据
     *
     * @param req 查询条件
     * @return 分页结果
     */
    PageVo<MonitorTaskDataVo> pageTasks(MonitorTaskDataReq req);
}
