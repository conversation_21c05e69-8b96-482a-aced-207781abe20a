package com.espc.sec.dataexport.tidb.dto;

import com.espc.sec.dataexport.common.dto.ExportDto;
import com.espc.sec.dataexport.common.enums.ExportModeEnum;
import com.espc.sec.dataexport.common.enums.ExportTypeEnum;
import lombok.Data;

import java.util.Date;

/**
 * TiDB导出参数
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Data
public class TidbExportDto extends ExportDto {
    
    private String databaseName;
    private String tableName;
    private String timeField;
    private Date startTime;
    private Date endTime;
    private ExportModeEnum exportModeEnum;
    private Integer taskId;

    public TidbExportDto() {
        // 设置导出类型为TiDB
        setExportTypeEnum(ExportTypeEnum.TIDB);
    }

    public TidbExportDto(String databaseName, String tableName, String timeField, 
                        Date startTime, Date endTime, ExportModeEnum exportModeEnum, Integer taskId) {
        // 设置导出类型为TiDB
        setExportTypeEnum(ExportTypeEnum.TIDB);
        setExportModeEnum(exportModeEnum);
        setTaskId(taskId);
        
        this.databaseName = databaseName;
        this.tableName = tableName;
        this.timeField = timeField;
        this.startTime = startTime;
        this.endTime = endTime;
        this.exportModeEnum = exportModeEnum;
        this.taskId = taskId;
    }

    /**
     * 获取表的唯一标识
     */
    public String getTableKey() {
        return databaseName + "." + tableName;
    }
}