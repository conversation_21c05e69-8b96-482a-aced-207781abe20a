package com.espc.sec.flow.bean;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018-06-04
 */
public class Result implements Serializable {
    public static final int STATUS_ERROR = 0;
    public static final int STATUS_OK = 1;
    private int status;
    private String message;

    public Result(int status, String message) {
        this.status = status;
        this.message = message;
    }

    public Result(int status) {
        this.status = status;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}