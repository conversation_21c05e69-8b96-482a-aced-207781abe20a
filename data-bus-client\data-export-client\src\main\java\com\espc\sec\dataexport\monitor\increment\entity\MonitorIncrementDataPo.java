package com.espc.sec.dataexport.monitor.increment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 定时增量任务监控
 */
@Data
@TableName("monitor_increment_data")
public class MonitorIncrementDataPo {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 节点
     */
    private String node;
    /**
     * 数据库类型 如hdfs minio es kafka starrocks
     */
    private String databaseType;
    /**
     * 表类型 对应操作的文件或者数据表名字
     */
    private String tableName;
    /**
     * 文件名字。上级结构
     */
    private String fileName;
    /**
     * 导出或者导入的条数
     */
    private Long size;
    /**
     * 导入导出的时间
     */
    private Date time;
    /**
     * 创建时间
     */
    private Date createTime;
}
