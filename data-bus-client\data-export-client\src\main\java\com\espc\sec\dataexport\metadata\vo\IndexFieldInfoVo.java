package com.espc.sec.dataexport.metadata.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Elasticsearch索引字段信息VO
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexFieldInfoVo {
    /**
     * 字段名称
     */
    private String fieldName;
    
    /**
     * 字段类型
     */
    private String fieldType;
    
    /**
     * 是否可搜索
     */
    private Boolean isSearchable;
    
    /**
     * 是否可聚合
     */
    private Boolean isAggregatable;
}