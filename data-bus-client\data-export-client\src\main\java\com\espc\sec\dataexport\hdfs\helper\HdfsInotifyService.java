package com.espc.sec.dataexport.hdfs.helper;

import com.espc.sec.dataexport.common.constant.LogKeyword;
import com.espc.sec.dataexport.common.util.SpringBeanUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.hdfs.DFSInotifyEventInputStream;
import org.apache.hadoop.hdfs.inotify.Event;
import org.apache.hadoop.hdfs.inotify.EventBatch;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class HdfsInotifyService {
    private static KafkaTemplate<String, String> kafkaTemplate;

    public static void watchLoop() {
        try {
            DFSInotifyEventInputStream eventStream = HdfsServiceHelper.hdfsAdmin.getInotifyEventStream();
            while (true) {
                try {
                    EventBatch batch = eventStream.poll(5, TimeUnit.SECONDS);
                    if (batch != null) {
                        processEventBatch(batch);
                    }
                } catch (Exception e) {
                    log.error(LogKeyword.HDFS_EXPORT + "hdfs监听目录失败", e);
                }
            }
        } catch (IOException e) {
            log.error(LogKeyword.HDFS_EXPORT + "hdfs监听目录失败", e);
        }
    }

    private static void processEventBatch(EventBatch batch) {
        for (Event event : batch.getEvents()) {
            try {
                processEvent(event);
            } catch (Exception e) {
                log.error(LogKeyword.HDFS_EXPORT + "hdfs监听目录失败,event,{}", event, e);
            }
        }
    }

    /**
     * 处理事件 - Hadoop 3.x 实现
     */
    private static void processEvent(Event event) {
        String eventPath = getEventPath(event);
        if (eventPath == null || !eventPath.startsWith("/data/pcap")) {
            return;
        }

        switch (event.getEventType()) {
            case CREATE:
                break;

            case CLOSE:
                Event.CloseEvent closeEvent = (Event.CloseEvent) event;
                kafkaTemplate.send("kt-hdfs", closeEvent.getPath());
                break;

            case APPEND:
                break;

            case RENAME:
                break;

            case METADATA:
            case UNLINK:
                // 处理其他事件类型
                break;

            default:
                log.trace("Unhandled event type: {}", event.getEventType());
        }
    }

    private static String getEventPath(Event event) {
        switch (event.getEventType()) {
            case CREATE:
                return ((Event.CreateEvent) event).getPath();
            case CLOSE:
                return ((Event.CloseEvent) event).getPath();
            case APPEND:
                return ((Event.AppendEvent) event).getPath();
            case RENAME:
                return ((Event.RenameEvent) event).getSrcPath();
            case UNLINK:
                return ((Event.UnlinkEvent) event).getPath();
            default:
                return null;
        }
    }

    @PostConstruct
    public void init() {
        kafkaTemplate = SpringBeanUtil.getBean(KafkaTemplate.class);
    }


}