<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.espc.sec.dataexport.monitor.task.mapper.MonitorTaskDataMapper">

    <!-- 分页查询监控任务数据 -->
    <select id="selectPageList" resultType="com.espc.sec.dataexport.monitor.task.vo.MonitorTaskDataVo">
        SELECT
        id,
        node,
        task_name as taskName,
        database_type as databaseType,
        table_name as tableName,
        size,
        DATE_FORMAT(time, '%Y-%m-%d %H:%i:%s') as time,
        DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as createTime,
        file_name
        FROM monitor_task_data
        <where>
            <if test="req.node != null and req.node != ''">
                AND node LIKE CONCAT('%', #{req.node}, '%')
            </if>
            <if test="req.taskName != null and req.taskName != ''">
                AND task_name LIKE CONCAT('%', #{req.taskName}, '%')
            </if>
            <if test="req.databaseType != null and req.databaseType != ''">
                AND database_type = #{req.databaseType}
            </if>
            <if test="req.tableName != null and req.tableName != ''">
                AND table_name LIKE CONCAT('%', #{req.tableName}, '%')
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND create_time &gt;= #{req.startTime}
            </if>
            <if test="req.endTime != null and req.endTime != ''">
                AND create_time &lt;= #{req.endTime}
            </if>
            <if test="req.fileName != null and req.fileName != ''">
                AND file_name LIKE CONCAT('%', #{req.fileName}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>


    <!-- 列表查询监控任务数据（不分页） -->
    <select id="selectList" resultType="com.espc.sec.dataexport.monitor.task.vo.MonitorTaskDataVo">
        SELECT
        id,
        node,
        task_name as taskName,
        database_type as databaseType,
        table_name as tableName,
        size,
        DATE_FORMAT(time, '%Y-%m-%d %H:%i:%s') as time,
        DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as createTime,
        file_name
        FROM monitor_task_data
        <where>
            <if test="req.node != null and req.node != ''">
                AND node LIKE CONCAT('%', #{req.node}, '%')
            </if>
            <if test="req.taskName != null and req.taskName != ''">
                AND task_name LIKE CONCAT('%', #{req.taskName}, '%')
            </if>
            <if test="req.databaseType != null and req.databaseType != ''">
                AND database_type = #{req.databaseType}
            </if>
            <if test="req.tableName != null and req.tableName != ''">
                AND table_name LIKE CONCAT('%', #{req.tableName}, '%')
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND create_time &gt;= #{req.startTime}
            </if>
            <if test="req.endTime != null and req.endTime != ''">
                AND create_time &lt;= #{req.endTime}
            </if>
            <if test="req.fileName != null and req.fileName != ''">
                AND file_name LIKE CONCAT('%', #{req.fileName}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 聚合查询监控任务数据 -->
    <select id="selectAggregateList" resultType="com.espc.sec.dataexport.monitor.task.vo.MonitorTaskDataAggregateVo">
        SELECT
        node,
        task_name as taskName,
        database_type as databaseType,
        table_name as tableName,
        SUM(size) as totalSize
        FROM monitor_task_data
        <where>
            <if test="req.node != null and req.node != ''">
                AND node LIKE CONCAT('%', #{req.node}, '%')
            </if>
            <if test="req.taskName != null and req.taskName != ''">
                AND task_name LIKE CONCAT('%', #{req.taskName}, '%')
            </if>
            <if test="req.databaseType != null and req.databaseType != ''">
                AND database_type = #{req.databaseType}
            </if>
            <if test="req.tableName != null and req.tableName != ''">
                AND table_name LIKE CONCAT('%', #{req.tableName}, '%')
            </if>
            <if test="req.startTime != null and req.startTime != ''">
                AND create_time &gt;= #{req.startTime}
            </if>
            <if test="req.endTime != null and req.endTime != ''">
                AND create_time &lt;= #{req.endTime}
            </if>
        </where>
        GROUP BY node, task_name, database_type, table_name
        ORDER BY totalSize DESC
    </select>

</mapper>