package com.espc.sec.dataexport.common.util;

import com.espc.sec.dataexport.common.constant.Constants;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 时间工具类
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
public class DateUtil extends cn.hutool.core.date.DateUtil {

    private static final DateTimeFormatter DISPLAY_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private static Pattern CRON_EXPRESSION = Pattern.compile("^(\\d+)\\s(\\d+)\\s(\\d+)\\s(\\d+)\\s(\\d+)\\s\\?\\s(\\d+)$");


    /**
     * 格式化时间戳为可读字符串
     *
     * @param timestamp 时间戳（毫秒）
     * @return 格式化后的时间字符串
     */
    public static String formatTimestamp(long timestamp) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
        return dateTime.format(DISPLAY_FORMATTER);
    }

    /**
     * 获取当前时间戳
     *
     * @return 当前时间戳（毫秒）
     */
    public static long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }


    /**
     * 将日期时间字符串转换为Cron表达式
     *
     * @param dateTimeStr 日期时间字符串，格式为 "yyyy-MM-dd HH:mm:ss"
     * @return Cron表达式
     * @throws ParseException 如果日期格式不正确
     */
    public static String dateTimeToCron(String dateTimeStr) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(Constants.YYYY_MM_DD_HH_MM_SS);
        Date date = sdf.parse(dateTimeStr);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int second = calendar.get(Calendar.SECOND);
        int minute = calendar.get(Calendar.MINUTE);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        int month = calendar.get(Calendar.MONTH) + 1;
        int year = calendar.get(Calendar.YEAR);

        return String.format("%d %d %d %d %d ? %d",
                second, minute, hour, day, month, year);
    }

    /**
     * 获取当前时间的指定天前前的时间
     *
     * @return
     */
    public static Date getNowStartTimeByDays(Integer day) {
        Date currentDate = new Date();
        return dateAddByDay(currentDate, day);
    }

    /**
     * 给指定时间加上天数
     *
     * @param date
     * @param day
     * @return
     */
    public static Date dateAddByDay(Date date, Integer day) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        // 获取指定天前的日期
        calendar.add(Calendar.DAY_OF_MONTH, day);
        return calendar.getTime();
    }

    /**
     * 将Cron表达式转换为日期时间字符串
     *
     * @param cronExpression Cron表达式，格式为"秒 分 时 日 月 ? 年"
     * @return 日期时间字符串，格式为"yyyy-MM-dd HH:mm:ss"
     * @throws IllegalArgumentException 如果Cron表达式格式不正确
     */
    public static String cronToDateTime(String cronExpression) throws IllegalArgumentException {
        Matcher matcher = CRON_EXPRESSION.matcher(cronExpression.trim());

        if (!matcher.matches()) {
            throw new IllegalArgumentException("Invalid cron expression format. Expected format: 'sec min hour day month ? year'");
        }

        try {
            // 解析Cron表达式各部分
            int second = Integer.parseInt(matcher.group(1));
            int minute = Integer.parseInt(matcher.group(2));
            int hour = Integer.parseInt(matcher.group(3));
            int day = Integer.parseInt(matcher.group(4));
            int month = Integer.parseInt(matcher.group(5)) - 1; // Calendar月份从0开始
            int year = Integer.parseInt(matcher.group(6));

            // 验证日期时间有效性
            Calendar calendar = Calendar.getInstance();
            calendar.setLenient(false); // 严格模式，自动验证日期有效性
            calendar.set(year, month, day, hour, minute, second);

            // 格式化日期时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(calendar.getTime());

        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Cron expression contains invalid numbers", e);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid date/time values in cron expression", e);
        }
    }

    /**
     * 时间范围��
     */
    @Data
    @AllArgsConstructor
    public static class TimeRange {
        private long startTime;
        private long endTime;

        /**
         * 检查时间范围是否有效
         */
        public boolean isValid() {
            return startTime < endTime;
        }

        /**
         * 获取时间范围的持续时间（毫秒）
         */
        public long getDuration() {
            return endTime - startTime;
        }

        /**
         * 获取时间范围的持续时间（分钟）
         */
        public long getDurationMinutes() {
            return getDuration() / (60 * 1000);
        }

        @Override
        public String toString() {
            return String.format("[%s - %s]",
                    formatTimestamp(startTime),
                    formatTimestamp(endTime));
        }
    }
}