package com.espc.sec.dataexport.minio.controller;


import com.espc.sec.dataexport.common.dto.task.MinioTaskProperties;
import com.espc.sec.dataexport.minio.helper.MinIOServiceHelper;
import com.espc.sec.dataexport.minio.service.MinioExportServiceImpl;
import io.minio.messages.Bucket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/minio")
public class MinioTestController {

    @Autowired
    private MinioExportServiceImpl minioExportServiceImpl;

    /**
     * 手动上传hdfs文件
     */
    @GetMapping("/upload")
    public void test(String bucketName, String objectName, String localFile) throws Exception {
        MinIOServiceHelper.uploadFile(bucketName, objectName, new File(localFile));
    }

    @PostMapping("/uploadTaskV2")
    public void testV2(){
        List<Bucket> buckets = MinIOServiceHelper.listBuckets();
        log.info("buckets:{}", buckets);
        for (Bucket bucket : buckets) {
            log.info("bucket:{}", bucket.name());
        }
    }


    @PostMapping("/export")
    public void export(@RequestBody @Valid MinioTaskProperties minioTaskProperties) throws Exception {
        minioExportServiceImpl.taskExport(minioTaskProperties);
    }

}