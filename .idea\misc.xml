<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectInspectionProfilesVisibleTreeState">
    <entry key="Project Default">
      <profile-state>
        <expanded-state>
          <State />
          <State>
            <id>Android</id>
          </State>
          <State>
            <id>CorrectnessLintAndroid</id>
          </State>
          <State>
            <id>Gradle</id>
          </State>
          <State>
            <id>Kotlin</id>
          </State>
          <State>
            <id>LintAndroid</id>
          </State>
          <State>
            <id>Maven</id>
          </State>
          <State>
            <id>MavenMigrationKotlin</id>
          </State>
          <State>
            <id>MigrationKotlin</id>
          </State>
          <State>
            <id>Package Search</id>
          </State>
          <State>
            <id>Probable bugsGradle</id>
          </State>
        </expanded-state>
      </profile-state>
    </entry>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8" project-jdk-type="JavaSDK">
    <output url="file://$PROJECT_DIR$/out" />
  </component>
</project>