package com.espc.sec.dataexport.job.service.impl;

import com.espc.sec.dataexport.common.dto.task.TaskProperties;
import com.espc.sec.dataexport.common.enums.JobTypeEnum;
import com.espc.sec.dataexport.common.util.JsonUtil;
import com.espc.sec.dataexport.job.dto.JobInfo;
import com.espc.sec.dataexport.job.service.JobService;
import com.espc.sec.dataexport.job.util.JobManager;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.espc.sec.dataexport.common.constant.Constants.Quartz.*;

@Component
@Slf4j
public class JobServiceImpl implements JobService {
    @Autowired
    private JobManager jobManager;

    @Override
    public void addJob(Integer jobId, JobTypeEnum jobTypeEnum, String cron, TaskProperties taskProperties) {
        addOrUpdateJob(jobId, jobTypeEnum, cron, taskProperties, false);
    }

    @Override
    public void updateJob(Integer jobId, JobTypeEnum jobTypeEnum, String cron, TaskProperties taskProperties) {
        addOrUpdateJob(jobId, jobTypeEnum, cron, taskProperties, true);
    }

    private void addOrUpdateJob(Integer jobId, JobTypeEnum jobTypeEnum, String cron, TaskProperties taskProperties, boolean isUpdate) {
        log.info("quartz定时任务{},类型:{},cron:{},配置:{}",
                isUpdate ? "更新" : "创建"
                , jobTypeEnum.name()
                , cron
                , JsonUtil.objectToJson(taskProperties));
        JobInfo jobInfo = new JobInfo();
        jobInfo.setJobName(jobId.toString());
        jobInfo.setJobGroup(JOB_GROUP);
        jobInfo.setJobClassName(jobTypeEnum.getTaskClass().getName());
        jobInfo.setCronExpression(cron);
        Map<String, Object> param = Maps.newHashMap();
        param.put(TASK_PARAMS, taskProperties);
        param.put(EXPORT_TYPE_TASK_PARAMS, jobTypeEnum);
        jobInfo.setJobData(param);
        try {
            if (isUpdate) {
                jobManager.updateJob(jobInfo);
            } else {
                jobManager.createJob(jobInfo);
            }
        } catch (Exception e) {
            log.error("quartz定时任务创建失败", e);
        }
    }

    @Override
    public void deleteJob(Integer jobId) {
        try {
            jobManager.deleteJob(jobId.toString(), JOB_GROUP);
            log.info("quartz定时任务删除成功,id{}", jobId);
        } catch (SchedulerException e) {
            log.error("任务删除失败", e);
        }
    }
}
