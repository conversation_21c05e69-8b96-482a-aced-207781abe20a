<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.espc.sec.dataexport.tidb.mapper.TidbDataMapper">

    <!-- 动态查询表数据 -->
    <select id="queryDataForTable" resultType="java.util.Map">
        SELECT * FROM ${tableName} 
        WHERE ${timeField} >= #{startTime} 
        AND ${timeField} &lt; #{endTime} 
        ORDER BY ${timeField} ASC 
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

</mapper>