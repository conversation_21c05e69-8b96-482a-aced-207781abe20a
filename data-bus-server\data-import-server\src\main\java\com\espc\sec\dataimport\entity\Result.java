package com.espc.sec.dataimport.entity;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2023/2/13
 **/
public class Result extends HashMap<String, Object> {
    public Result() {
        put("code", "200");
        put("msg", "success");
    }

    public Result(int code, String data) {
        put("code", code);
        put("data", data);
    }

    public static Result ok() {
        Result result = new Result();
        return result;
    }

    public static Result ok(Object data) {
        Result result = new Result();
        result.put("data", data);
        return result;
    }

    public static Result error(String msg) {
        Result result = new Result();
        result.put("code", "500");
        result.put("msg", msg);
        return result;
    }
}
