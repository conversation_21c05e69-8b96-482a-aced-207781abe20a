package com.espc.sec.dataexport.monitor.increment.controller;

import com.espc.sec.dataexport.common.vo.PageVo;
import com.espc.sec.dataexport.common.vo.ResultVo;
import com.espc.sec.dataexport.monitor.increment.dto.MonitorIncrementDataDto;
import com.espc.sec.dataexport.monitor.increment.dto.MonitorIncrementDataReq;
import com.espc.sec.dataexport.monitor.increment.service.MonitorIncrementDataService;
import com.espc.sec.dataexport.monitor.increment.vo.MonitorIncrementDataGroupVo;
import com.espc.sec.dataexport.monitor.increment.vo.MonitorIncrementDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
/**
 * @Author: zh
 * @date: 2025/7/25
 */
@RestController
@RequestMapping("/monitorIncrementData")
@Api(tags = "增量导入导出监控")
public class MonitorIncrementDataController {


    @Resource
    private MonitorIncrementDataService monitorIncrementDataService;

    @PostMapping("/list")
    @ApiOperation("查询所有监控信息")
    public ResultVo<PageVo<MonitorIncrementDataVo>> list(@RequestBody MonitorIncrementDataReq monitorIncrementDataReq) {
        PageVo<MonitorIncrementDataVo> taskList = monitorIncrementDataService.page(monitorIncrementDataReq);
        return ResultVo.success(taskList);
    }

    @PostMapping("/create")
    @ApiOperation("创建监控信息")
    public ResultVo<Integer> create(@RequestBody MonitorIncrementDataDto monitorIncrementDataDto) {
        Integer monitorIncrementDataId = monitorIncrementDataService.create(monitorIncrementDataDto);
        return ResultVo.success(monitorIncrementDataId);
    }

    @PostMapping("/group")
    @ApiOperation("分组查询")
    public ResultVo<PageVo<MonitorIncrementDataGroupVo>> group(@RequestBody MonitorIncrementDataReq monitorIncrementDataDto) {
        PageVo<MonitorIncrementDataGroupVo> monitorIncrementDataGroup = monitorIncrementDataService.group(monitorIncrementDataDto);
        return ResultVo.success(monitorIncrementDataGroup);
    }

}
