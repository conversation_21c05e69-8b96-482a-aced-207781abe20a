package com.espc.sec.dataexport.common.service.impl;

import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.dto.ExportDto;
import com.espc.sec.dataexport.common.service.Exporter;
import com.espc.sec.dataexport.common.util.FileUtils;
import com.espc.sec.dataexport.common.util.JsonUtil;
import com.espc.sec.dataexport.common.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/29
 */
@Slf4j
public abstract class AbstractExporter<T extends ExportDto> implements Exporter {
    @Override
    public final void export(ExportDto exportDto) throws Exception {
        if (!Config.commonConfig.getSupportedExportTypes().contains(exportDto.getExportTypeEnum().getCode())) {
            return;
        }
        checkParam(exportDto);
        long start = System.currentTimeMillis();
        log.info("{}{}导出开始,配置{}"
                , exportDto.getExportTypeEnum().getLogKeyWord()
                , exportDto.getExportModeEnum().getName()
                , JsonUtil.objectToJson(exportDto)
        );
        List<File> tempFiles;
        T dto = ObjectUtil.converObjectToT(exportDto);
        try {
            tempFiles = doExport(dto);
        } catch (Exception e) {
            log.error("{}{}导出异常", exportDto.getExportTypeEnum().getLogKeyWord(), exportDto.getExportModeEnum().getName(), e);
            throw e;
        }
        tempFiles.forEach(tempFile -> {
            if (tempFile.length() == 0) {
                if (!tempFile.delete()) {
                    log.error("空文件删除失败");
                }
            } else {
                log.info("{}{}导出,文件大小{}字节,文件名{}"
                        , exportDto.getExportTypeEnum().getLogKeyWord()
                        , exportDto.getExportModeEnum().getName()
                        , tempFile.length()
                        , tempFile.getName()
                );
                try {
                    saveImportLogToMysql(dto, tempFile);
                } catch (Exception e) {
                    log.error("{}{}日志统计异常", exportDto.getExportTypeEnum().getLogKeyWord(), exportDto.getExportModeEnum().getName(), e);
                }
                FileUtils.move(tempFile.getAbsolutePath(), exportDto.getExportPath() + File.separator + tempFile.getName());
            }
        });
        log.info("{}{}导出结束,耗时{}ms"
                , exportDto.getExportTypeEnum().getLogKeyWord()
                , exportDto.getExportModeEnum().getName()
                , System.currentTimeMillis() - start);
    }

    private void checkParam(ExportDto exportDto) {
        if (exportDto.getExportModeEnum() == null || exportDto.getExportTypeEnum() == null) {
            throw new IllegalArgumentException("参数错误,导出类型或导出模式为空");
        }
    }

    /**
     * 导出
     *
     * @param exporter
     * @return 临时目录文件
     */
    protected abstract List<File> doExport(T exporter) throws Exception;

    /**
     * 保存导出日志
     *
     * @param exporter
     * @param tempFile
     * @throws Exception
     */
    protected abstract void saveImportLogToMysql(T exporter, File tempFile) throws Exception;
}
