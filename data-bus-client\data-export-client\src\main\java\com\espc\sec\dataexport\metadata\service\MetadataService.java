package com.espc.sec.dataexport.metadata.service;

import com.espc.sec.dataexport.common.vo.PageVo;
import com.espc.sec.dataexport.metadata.dto.MetadataPageReq;
import com.espc.sec.dataexport.metadata.vo.*;

/**
 * 元数据服务接口
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
public interface MetadataService {
    
    /**
     * 获取Kafka主题列表
     */
    PageVo<TopicInfoVo> getKafkaTopics(MetadataPageReq req);
    
    /**
     * 获取Elasticsearch索引列表
     */
    PageVo<IndexInfoVo> getElasticsearchIndices(MetadataPageReq req);
    
    /**
     * 获取Elasticsearch索引字段列表
     */
    PageVo<IndexFieldInfoVo> getElasticsearchFields(MetadataPageReq req);
    
    /**
     * 获取StarRocks数据库列表
     */
    PageVo<DatabaseInfoVo> getStarRocksDatabases(MetadataPageReq req);
    
    /**
     * 获取StarRocks表列表
     */
    PageVo<TableInfoVo> getStarRocksTables(MetadataPageReq req);
    
    /**
     * 获取StarRocks字段列表
     */
    PageVo<FieldInfoVo> getStarRocksFields(MetadataPageReq req);
    
    /**
     * 获取TiDB数据库列表
     */
    PageVo<DatabaseInfoVo> getTidbDatabases(MetadataPageReq req);
    
    /**
     * 获取TiDB表列表
     */
    PageVo<TableInfoVo> getTidbTables(MetadataPageReq req);
    
    /**
     * 获取TiDB字段列表
     */
    PageVo<FieldInfoVo> getTidbFields(MetadataPageReq req);
}