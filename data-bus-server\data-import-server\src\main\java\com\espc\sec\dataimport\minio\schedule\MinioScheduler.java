package com.espc.sec.dataimport.minio.schedule;

import com.espc.sec.dataimport.common.constant.LogKeyword;
import com.espc.sec.dataimport.common.enums.ImportTypeEnum;
import com.espc.sec.dataimport.minio.service.MinioImporter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class MinioScheduler {
    @Autowired
    private MinioImporter minioImporter;

    @Scheduled(fixedDelayString = "#{T(com.espc.sec.dataimport.common.config.Config).importConfig.minio.getFixedDelayMills()}")
    public void run() {
        log.info(LogKeyword.MINIO_IMPORT + "定时任务开始");
        minioImporter.import0(ImportTypeEnum.MINIO);
        log.info(LogKeyword.MINIO_IMPORT + "定时任务结束");
    }
}
