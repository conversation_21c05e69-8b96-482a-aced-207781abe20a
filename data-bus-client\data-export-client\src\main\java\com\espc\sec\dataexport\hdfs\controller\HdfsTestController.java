package com.espc.sec.dataexport.hdfs.controller;


import com.espc.sec.dataexport.common.dto.task.HdfsTaskProperties;
import com.espc.sec.dataexport.hdfs.helper.HdfsServiceHelper;
import com.espc.sec.dataexport.hdfs.service.HdfsExportServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.simpleframework.xml.core.Validate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/hdfs")
public class HdfsTestController {

    @Autowired
    private HdfsExportServiceImpl hdfsExportServiceImpl;

    /**
     * 手动上传hdfs文件
     */
    @GetMapping("/upload")
    public void test(String localPath, String hdfsPath) {
        HdfsServiceHelper.uploadFile(localPath, hdfsPath, true);
    }

    /**
     * 根据时间导出hdfs文件
     *
     * @param hdfsExportTaskDto 时间参数
     * @throws Exception
     */
    @PostMapping("/export")
    public void export(@RequestBody @Validate HdfsTaskProperties hdfsExportTaskDto) throws Exception {
        hdfsExportServiceImpl.taskExport(hdfsExportTaskDto);
    }

}