<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.espc.sec.dataexport.task.mapper.DataTaskMapper">
    <resultMap id="BaseResultMap" type="com.espc.sec.dataexport.task.entity.DataTaskPo">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="cron" property="cron" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="VARCHAR"/>
        <result column="business_config" property="businessConfig" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="node" property="node" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="delete_time" property="deleteTime" jdbcType="TIMESTAMP"/>
        <result column="is_delete" property="isDelete" jdbcType="TINYINT"/>
        <result column="extra_filed" property="extraFiled" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , name, type, cron, business_type, business_config, description, status, node,
        create_time, update_time, delete_time, is_delete, extra_filed
    </sql>


    <select id="selectByCondition" resultMap="BaseResultMap"
            parameterType="com.espc.sec.dataexport.task.dto.DataTaskReq">
        SELECT
        <include refid="Base_Column_List"/>
        FROM data_task
        WHERE is_delete = 0
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name,jdbcType=VARCHAR}, '%')
        </if>
        <if test="type != null">
            AND type = #{type,jdbcType=INTEGER}
        </if>
        <if test="businessType != null and businessType != ''">
            AND business_type = #{businessType,jdbcType=VARCHAR}
        </if>
        <if test="status != null">
            AND status = #{status,jdbcType=TINYINT}
        </if>
        <if test="node != null and node != ''">
            AND node = #{node}
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="listTasksByStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM data_task
        WHERE is_delete = 0
        <if test="status != null">
            AND status = #{status,jdbcType=TINYINT}
        </if>
    </select>


    <select id="selectByName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM data_task
        WHERE `name`=#{name} AND is_delete = 0
    </select>

    <select id="selectByNameNotId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM data_task
        WHERE `name`=#{name} AND is_delete = 0 AND id != #{id}
    </select>

    <insert id="insert" parameterType="com.espc.sec.dataexport.task.entity.DataTaskPo">
        INSERT INTO data_task (id, name, type, cron, business_type,
                               business_config, description, status, node,
                               create_time, update_time, extra_filed)
        VALUES (#{id,jdbcType=VARCHAR},
                #{name,jdbcType=VARCHAR},
                #{type,jdbcType=INTEGER},
                #{cron,jdbcType=VARCHAR},
                #{businessType,jdbcType=VARCHAR},
                #{businessConfig,jdbcType=VARCHAR},
                #{description,jdbcType=VARCHAR},
                #{status,jdbcType=TINYINT},
                #{node,jdbcType=VARCHAR},
                NOW(),
                NOW(),
                #{extraFiled,jdbcType=VARCHAR})
    </insert>

    <update id="updateById" parameterType="com.espc.sec.dataexport.task.entity.DataTaskPo">
        UPDATE data_task
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="cron != null">
                cron = #{cron,jdbcType=VARCHAR},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=VARCHAR},
            </if>
            <if test="businessConfig != null">
                business_config = #{businessConfig,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="node != null">
                node = #{node,jdbcType=VARCHAR},
            </if>
            <if test="extraFiled != null">
                extra_filed = #{extraFiled,jdbcType=VARCHAR},
            </if>
            update_time = NOW()
        </set>
        WHERE id = #{id,jdbcType=INTEGER}
        AND is_delete = 0
    </update>

    <update id="updateStatus">
        UPDATE data_task
        SET status      = #{status,jdbcType=TINYINT},
            update_time = NOW()
        WHERE id = #{id,jdbcType=INTEGER}
          AND is_delete = 0
    </update>

    <update id="deleteById" parameterType="java.lang.Integer">
        UPDATE data_task
        SET is_delete   = 1,
            delete_time = NOW()
        WHERE id = #{id,jdbcType=INTEGER}
          AND is_delete = 0
    </update>

    <select id="getById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM data_task WHERE id = #{id}
    </select>
</mapper>