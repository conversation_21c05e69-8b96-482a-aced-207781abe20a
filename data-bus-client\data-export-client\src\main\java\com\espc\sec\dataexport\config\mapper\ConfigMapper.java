package com.espc.sec.dataexport.config.mapper;

import com.espc.sec.dataexport.config.vo.ConfigVo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2025/7/28
 */
public interface ConfigMapper {

    ConfigVo getConfig(@Param("module") String module
            , @Param("tType") String type
            , @Param("tKey") String key);

    void save(@Param("module") String module
            , @Param("tType") String type
            , @Param("tKey") String key
            , @Param("tValue") String value);

    void updateValue(@Param("id") Integer id, @Param("tValue") String value);
}
