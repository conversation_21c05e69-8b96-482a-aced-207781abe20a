package com.espc.sec.dataexport.hdfs.helper;

import com.espc.sec.dataexport.common.util.SystemUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.apache.hadoop.fs.*;
import org.apache.hadoop.fs.permission.FsPermission;
import org.apache.hadoop.hdfs.client.HdfsAdmin;
import org.apache.hadoop.io.IOUtils;

import java.io.*;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class HdfsServiceHelper {

    private static final int BUFFER_SIZE = 4096;
    private static final short REPLICATION = 3;
    private static final long BLOCK_SIZE = 128 * 1024 * 1024; // 128MB
    public static HdfsAdmin hdfsAdmin;
    private static FileSystem fs = null;

    static {
        System.setProperty("HADOOP_USER_NAME", "hdfs");
        if (SystemUtils.isWindows() && SystemUtils.getAllLocalIPs().contains("*************")) {
            System.setProperty("hadoop.home.dir", "D:\\hadoop\\hadoop");
            System.load("D:\\hadoop\\hadoop\\bin\\hadoop.dll");
        }
    }

    public static void init(String hdfsUri) throws IOException {
        try {
            Configuration hadoopConfig = createHadoopConfig();
            fs = FileSystem.get(hadoopConfig);
            hdfsAdmin = new HdfsAdmin(URI.create(hdfsUri), hadoopConfig);
        } catch (IOException e) {
            log.error("Failed to initialize HDFS client", e);
            throw e;
        }
    }

    private static Configuration createHadoopConfig() {
        Configuration conf = new Configuration();

        conf.set("fs.hdfs.impl", "org.apache.hadoop.hdfs.DistributedFileSystem");
        conf.set("fs.file.impl", "org.apache.hadoop.fs.LocalFileSystem");
        conf.setBoolean("dfs.client.use.datanode.hostname", true);
        conf.setInt("dfs.client.socket-timeout", 300000);

        String coreSitePath = "conf/DataExportClient/conf/hadoop/core-site.xml";
        String hdfsSitePath = "conf/DataExportClient/conf/hadoop/hdfs-site.xml";

        if (new File(coreSitePath).exists() && new File(hdfsSitePath).exists()) {
            conf.addResource(new Path(coreSitePath));
            conf.addResource(new Path(hdfsSitePath));
        } else {
            log.warn("Hadoop配置文件 {} 或 {} 不存在，使用默认配置", coreSitePath, hdfsSitePath);
        }

        return conf;
    }

    /**
     * 创建目录
     *
     * @param path 目录路径
     * @return 是否创建成功
     */
    public static boolean mkdir(String path) {
        try {
            Path hdfsPath = new Path(path);
            if (fs.exists(hdfsPath)) {
                log.warn("Directory already exists: {}", path);
                return false;
            }
            return fs.mkdirs(hdfsPath);
        } catch (IOException e) {
            log.error("Failed to create directory: {}", path, e);
            return false;
        }
    }

    /**
     * 创建目录并设置权限
     *
     * @param path       目录路径
     * @param permission 权限，例如 "755"
     * @return 是否创建成功
     */
    public static boolean mkdirWithPermission(String path, String permission) {
        try {
            Path hdfsPath = new Path(path);
            if (fs.exists(hdfsPath)) {
                log.warn("Directory already exists: {}", path);
                return false;
            }
            FsPermission fsPermission = new FsPermission(permission);
            return fs.mkdirs(hdfsPath, fsPermission);
        } catch (IOException e) {
            log.error("Failed to create directory with permission: {}", path, e);
            return false;
        }
    }

    /**
     * 删除文件或目录
     *
     * @param path      路径
     * @param recursive 是否递归删除
     * @return 是否删除成功
     */
    public static boolean delete(String path, boolean recursive) {
        try {
            Path hdfsPath = new Path(path);
            if (!fs.exists(hdfsPath)) {
                log.warn("Path does not exist: {}", path);
                return false;
            }
            return fs.delete(hdfsPath, recursive);
        } catch (IOException e) {
            log.error("Failed to delete path: {}", path, e);
            return false;
        }
    }

    /**
     * 上传本地文件到HDFS
     *
     * @param localPath 本地文件路径
     * @param hdfsPath  HDFS路径
     * @param overwrite 是否覆盖
     * @return 是否上传成功
     */
    public static boolean uploadFile(String localPath, String hdfsPath, boolean overwrite) {
        try (InputStream in = new BufferedInputStream(new FileInputStream(localPath))) {
            Path hdfsFilePath = new Path(hdfsPath);

            if (fs.exists(hdfsFilePath)) {
                if (overwrite) {
                    fs.delete(hdfsFilePath, false);
                } else {
                    log.warn("File already exists and overwrite is false: {}", hdfsPath);
                    return false;
                }
            }

            try (OutputStream out = fs.create(hdfsFilePath, true, BUFFER_SIZE, REPLICATION, BLOCK_SIZE)) {
                IOUtils.copyBytes(in, out, BUFFER_SIZE, false);
            }
            return true;
        } catch (IOException e) {
            log.error("Failed to upload file from {} to {}", localPath, hdfsPath, e);
            return false;
        }
    }

    /**
     * 下载HDFS文件到本地
     *
     * @param hdfsPath  HDFS文件路径
     * @param localPath 本地文件路径
     * @return 是否下载成功
     */
    public static boolean downloadFile(String hdfsPath, String localPath) {
        try (OutputStream out = new BufferedOutputStream(new FileOutputStream(localPath))) {
            Path hdfsFilePath = new Path(hdfsPath);

            if (!fs.exists(hdfsFilePath)) {
                log.error("HDFS file does not exist: {}", hdfsPath);
                return false;
            }

            try (InputStream in = fs.open(hdfsFilePath)) {
                IOUtils.copyBytes(in, out, BUFFER_SIZE, false);
            }
            return true;
        } catch (IOException e) {
            log.error("Failed to download file from {} to {}", hdfsPath, localPath, e);
            return false;
        }
    }

    /**
     * 重命名文件或目录
     *
     * @param oldPath 原路径
     * @param newPath 新路径
     * @return 是否重命名成功
     */
    public static boolean rename(String oldPath, String newPath) {
        try {
            Path oldHdfsPath = new Path(oldPath);
            Path newHdfsPath = new Path(newPath);

            if (!fs.exists(oldHdfsPath)) {
                log.error("Source path does not exist: {}", oldPath);
                return false;
            }

            if (fs.exists(newHdfsPath)) {
                log.error("Destination path already exists: {}", newPath);
                return false;
            }

            return fs.rename(oldHdfsPath, newHdfsPath);
        } catch (IOException e) {
            log.error("Failed to rename from {} to {}", oldPath, newPath, e);
            return false;
        }
    }

    /**
     * 检查文件或目录是否存在
     *
     * @param path 路径
     * @return 是否存在
     */
    public static boolean exists(String path) {
        try {
            return fs.exists(new Path(path));
        } catch (IOException e) {
            log.error("Failed to check existence of path: {}", path, e);
            return false;
        }
    }

    /**
     * 获取文件或目录状态
     *
     * @param path 路径
     * @return FileStatus对象
     */
    public static FileStatus getFileStatus(String path) {
        try {
            return fs.getFileStatus(new Path(path));
        } catch (IOException e) {
            log.error("Failed to get file status: {}", path, e);
            return null;
        }
    }

    /**
     * 列出目录内容
     *
     * @param path 目录路径
     * @return 文件状态列表
     */
    public static List<FileStatus> listFiles(String path) {
        List<FileStatus> fileStatuses = new ArrayList<>();
        try {
            Path hdfsPath = new Path(path);

            if (!fs.exists(hdfsPath)) {
                log.error("Path does not exist: {}", path);
                return fileStatuses;
            }

            RemoteIterator<LocatedFileStatus> iterator = fs.listFiles(hdfsPath, true);
            while (iterator.hasNext()) {
                fileStatuses.add(iterator.next());
            }
        } catch (IOException e) {
            log.error("Failed to list files in path: {}", path, e);
        }
        return fileStatuses;
    }

    /**
     * 读取文件内容为字符串
     *
     * @param path 文件路径
     * @return 文件内容
     */
    public static String readFileToString(String path) {
        try (ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            Path hdfsPath = new Path(path);

            if (!fs.exists(hdfsPath)) {
                log.error("File does not exist: {}", path);
                return null;
            }

            try (InputStream in = fs.open(hdfsPath)) {
                IOUtils.copyBytes(in, out, BUFFER_SIZE, false);
            }
            return out.toString("UTF-8");
        } catch (IOException e) {
            log.error("Failed to read file: {}", path, e);
            return null;
        }
    }

    /**
     * 写入字符串到文件
     *
     * @param path      文件路径
     * @param content   内容
     * @param overwrite 是否覆盖
     * @return 是否写入成功
     */
    public static boolean writeStringToFile(String path, String content, boolean overwrite) {
        try (InputStream in = new ByteArrayInputStream(content.getBytes("UTF-8"))) {
            Path hdfsPath = new Path(path);

            if (fs.exists(hdfsPath)) {
                if (overwrite) {
                    fs.delete(hdfsPath, false);
                } else {
                    log.warn("File already exists and overwrite is false: {}", path);
                    return false;
                }
            }

            try (OutputStream out = fs.create(hdfsPath, true, BUFFER_SIZE, REPLICATION, BLOCK_SIZE)) {
                IOUtils.copyBytes(in, out, BUFFER_SIZE, false);
            }
            return true;
        } catch (IOException e) {
            log.error("Failed to write string to file: {}", path, e);
            return false;
        }
    }

    /**
     * 关闭文件系统连接
     */
    public static void close() {
        try {
            if (fs != null) {
                fs.close();
            }
        } catch (IOException e) {
            log.error("Failed to close HDFS client", e);
        }
    }


}
