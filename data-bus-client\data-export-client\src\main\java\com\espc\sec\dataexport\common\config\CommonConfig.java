package com.espc.sec.dataexport.common.config;

import lombok.Data;

import java.util.List;

/**
 * 公共配置
 * <AUTHOR>
 */
@Data
public class CommonConfig {
    /**
     * netty server Ip
     */
    private String taskServerIp;
    /**
     * netty server port
     */
    private int taskServerPort;
    /**
     * 导出目录
     */
    private String outputPath;
    /**
     * 导出临时目录
     */
    private String outputTempPath;
    /**
     * 生成的最大文件大小 单位Mb
     */
    private Integer maxTempFileSizeMb;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * quartz定时任务开关 关闭则任务型任务不会执行
     */
    private Boolean quartzTaskSwitch;
    /**
     * 支持导出的类型
     */
    private List<String> supportedExportTypes;
}