@echo off
set fileName=start.bat
set fileName_sh=start.sh
set commonJarName=common-project
set schedulerName=DataBusClient
set serviceJarName=%1
set mainClass=com.espc.sec.start.DataBusClient

echo cd .. > target\deploy\%fileName%
echo title  %schedulerName%  >> target\deploy\%fileName%
set /p="java -Xms8g -Xmx8g -classpath ">>target\deploy\%fileName%<nul
set /p="cd ..; java -Xms8g -Xmx8g -classpath ">>target\deploy\%fileName_sh%<nul
set /p=".\libs\%serviceJarName%;">>target\deploy\%fileName%<nul
set /p="./libs/%serviceJarName%:">>target\deploy\%fileName_sh%<nul
cd target\java-libs
SETLOCAL ENABLEDELAYEDEXPANSION
for %%i in (*) do (
    set j=%%i
	if "!j:~0,14!"=="%commonJarName%" (
		set /p=".\java-libs\%%i;">>..\deploy\%fileName%<nul
		set /p="./java-libs/%%i:">>..\deploy\%fileName_sh%<nul
	)
    if not "!j:~0,14!"=="%commonJarName%" (
        set /p=".\java-libs\%%i;">>..\deploy\%fileName%<nul
        set /p="./java-libs/%%i:">>..\deploy\%fileName_sh%<nul
    )
)

cd ../..
set /p=" -server -Djava.ext.dirs="%%JAVA_HOME%%\jre\lib\ext" ">>target\deploy\%fileName%<nul
set /p=" -Djava.ext.dirs="$JAVA_HOME/jre/lib/ext" ">>target\deploy\%fileName_sh%<nul
set /p="-Djava.library.path=.\java-libs\native ">>target\deploy\%fileName%<nul
set /p="-Djava.library.path=./java-libs/native ">>target\deploy\%fileName_sh%<nul
set /p="%mainClass% ">>target\deploy\%fileName%<nul
set /p="%mainClass% ">>target\deploy\%fileName_sh%<nul
set /p="--spring.config.location=./conf/ ">>target\deploy\%fileName%<nul
set /p="--spring.config.location=./conf/ ">>target\deploy\%fileName_sh%<nul
echo.>> target\deploy\%fileName%
echo exit>> target\deploy\%fileName%
