package com.espc.sec.dataexport.task.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 数据任务实体类
 */
@Data
@TableName("data_task")
public class DataTaskPo {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * job名称
     */
    private String name;

    /**
     * 任务类型 1-立即执行，2-定时执行
     */
    private Integer type;

    /**
     * cron表达式
     */
    private String cron;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 任务配置信息
     */
    private String businessConfig;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 状态 1-待执行，2-执行中，3-执行成功，4-执行失败
     */
    private Integer status;

    /**
     * 节点
     */
    private String node;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    private Integer isDelete;

    /**
     * 预留字段
     */
    private String extraFiled;
}
