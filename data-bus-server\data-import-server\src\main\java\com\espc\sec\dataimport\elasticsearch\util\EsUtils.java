package com.espc.sec.dataimport.elasticsearch.util;

import com.espc.sec.dataimport.elasticsearch.config.EsClientHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.common.settings.Settings;

/**
 * <AUTHOR>
 * @date 2025/7/24
 */
@Slf4j
public class EsUtils {
    public static void createIndexIfNotExists(String indexName) {
        try {
            if (StringUtils.isBlank(indexName)) {
                log.error("elasticsearch索引为空，无法创建");
                return;
            }
            GetIndexRequest getIndexRequest = new GetIndexRequest(indexName);

            if (!EsClientHolder.normalRestClient.indices().exists(getIndexRequest, RequestOptions.DEFAULT)) {
                CreateIndexRequest createIndexRequest = new CreateIndexRequest(indexName);

                // 设置索引配置
                Settings.Builder settings = Settings.builder()
                        .put("index.number_of_shards", 1)
                        .put("index.number_of_replicas", 0)
                        .put("index.refresh_interval", "30s");

                createIndexRequest.settings(settings);

                EsClientHolder.normalRestClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
                log.info("索引创建成功: {}", indexName);
            } else {
                log.info("索引已存在: {}", indexName);
            }
        } catch (Exception e) {
            log.error("创建索引失败: {}", indexName, e);
        }
    }
}
