package com.espc.sec.dataimport.monitor.task.controller;

import com.espc.sec.dataimport.common.dto.Result;
import com.espc.sec.dataimport.common.vo.PageVo;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataBaseReq;
import com.espc.sec.dataimport.monitor.task.dto.MonitorTaskDataReq;
import com.espc.sec.dataimport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataimport.monitor.task.vo.MonitorTaskDataAggregateVo;
import com.espc.sec.dataimport.monitor.task.vo.MonitorTaskDataVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 监控任务数据控制器
 *
 * <AUTHOR>
 * @date 2025-01-26
 */
@RestController
@RequestMapping("/api/monitor/task")
@Api(tags = "监控任务数据管理")
public class MonitorTaskDataController {

    @Resource
    private MonitorTaskDataService monitorTaskDataService;

    /**
     * 新增监控任务数据
     */
    @PostMapping("/add")
    @ApiOperation("新增监控任务数据")
    public Result<Boolean> add(@RequestBody @Validated MonitorTaskDataAddReq req) {
        Boolean result = monitorTaskDataService.add(req);
        return Result.success(result);
    }

    @PostMapping("/list")
    @ApiOperation("查询所有任务")
    public Result<PageVo<MonitorTaskDataVo>> getPageList(@RequestBody MonitorTaskDataReq dataTaskReq) {
        PageVo<MonitorTaskDataVo> taskList = monitorTaskDataService.pageTasks(dataTaskReq);
        return Result.success(taskList);
    }

    /**
     * 列表查询导入任务数据（不分页）
     */
    @PostMapping("/listAll")
    @ApiOperation("列表查询导入任务数据")
    public Result<List<MonitorTaskDataVo>> getList(@RequestBody MonitorTaskDataBaseReq req) {
        List<MonitorTaskDataVo> result = monitorTaskDataService.getList(req);
        return Result.success(result);
    }

    /**
     * 聚合查询导入任务数据
     * 根据node、task_name、database_type、table_name分组汇总size
     */
    @PostMapping("/aggregate")
    @ApiOperation("聚合查询导入任务数据")
    public Result<List<MonitorTaskDataAggregateVo>> getAggregateList(@RequestBody MonitorTaskDataBaseReq req) {
        List<MonitorTaskDataAggregateVo> result = monitorTaskDataService.getAggregateList(req);
        return Result.success(result);
    }
}