package com.espc.sec.dataexport.netty.utils;

import cn.hutool.json.JSONUtil;
import com.espc.sec.dataexport.common.enums.OperateTypeEnum;
import com.espc.sec.dataexport.common.enums.TaskStatusEnum;
import com.espc.sec.dataexport.netty.entity.MessageTypeEnum;
import com.espc.sec.dataexport.netty.entity.MessageWrapper;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2025/7/19
 **/
@Slf4j
public class TaskManagerUtil {

    public static void taskStatus(Integer id, TaskStatusEnum taskStatusEnum) {
        if (id == null || taskStatusEnum == null) {
            log.error("任务状态参数异常: null");
            return;
        }

        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setType(MessageTypeEnum.TASK);
        messageWrapper.setOperateType(OperateTypeEnum.UPDATE);

        HashMap<String, Object> map = new HashMap<>(2);
        map.put("taskId", id);
        map.put("taskStatus", taskStatusEnum.getCode());
        byte[] body = JSONUtil.toJsonStr(map).getBytes(StandardCharsets.UTF_8);
        messageWrapper.setLength(body.length);
        messageWrapper.setBody(body);

        Channel channel = ChannelUtil.serverChannel;
        if (channel == null) {
            log.error("总中心连接通道异常");
            return;
        }

        channel.writeAndFlush(messageWrapper);
        log.info("分中心向总中心上报任务成功");
    }
}
