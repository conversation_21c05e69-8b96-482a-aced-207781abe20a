package com.espc.sec.dataexport.monitor.task.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.espc.sec.dataexport.common.config.Config;
import com.espc.sec.dataexport.common.enums.TaskTypeEnum;
import com.espc.sec.dataexport.common.util.JsonUtil;
import com.espc.sec.dataexport.common.util.PageHelperUtil;
import com.espc.sec.dataexport.common.vo.PageVo;
import com.espc.sec.dataexport.monitor.task.dto.MonitorTaskDataAddReq;
import com.espc.sec.dataexport.monitor.task.dto.MonitorTaskDataBaseReq;
import com.espc.sec.dataexport.monitor.task.dto.MonitorTaskDataReq;
import com.espc.sec.dataexport.monitor.task.entity.MonitorTaskDataPo;
import com.espc.sec.dataexport.monitor.task.mapper.MonitorTaskDataMapper;
import com.espc.sec.dataexport.monitor.task.service.MonitorTaskDataService;
import com.espc.sec.dataexport.monitor.task.vo.MonitorTaskDataAggregateVo;
import com.espc.sec.dataexport.monitor.task.vo.MonitorTaskDataVo;
import com.espc.sec.dataexport.task.vo.DataTaskVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 监控任务数据服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Slf4j
@Service
public class MonitorTaskDataServiceImpl implements MonitorTaskDataService {

    @Autowired
    private MonitorTaskDataMapper monitorTaskDataMapper;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public Boolean add(MonitorTaskDataAddReq req) {
        try {
            MonitorTaskDataPo po = new MonitorTaskDataPo();
            BeanUtil.copyProperties(req, po);

            // 设置节点名称，如果请求中没有则使用配置中的节点名称
            if (StrUtil.isBlank(po.getNode())) {
                po.setNode(Config.commonConfig.getNodeName());
            }

// 解析时间字符串
            if (StrUtil.isNotBlank(req.getTime())) {
                po.setTime(
                        DateUtil.parseLocalDateTime(req.getTime(), "yyyyMMddHHmmssSSS")
                );
            }


            // 设置创建时间
            po.setCreateTime(LocalDateTime.now());

            int result = monitorTaskDataMapper.insert(po);
            return result > 0;
        } catch (Exception e) {
            log.error("新增监控任务数据失败", e);
            return false;
        }
    }

    @Override
    public List<MonitorTaskDataVo> getList(MonitorTaskDataBaseReq req) {
        try {
            return monitorTaskDataMapper.selectList(req);
        } catch (Exception e) {
            log.error("列表查询监控任务数据失败", e);
            throw new RuntimeException("查询失败: " + e.getMessage());
        }
    }

    @Override
    public List<MonitorTaskDataAggregateVo> getAggregateList(MonitorTaskDataBaseReq req) {
        try {
            return monitorTaskDataMapper.selectAggregateList(req);
        } catch (Exception e) {
            log.error("聚合查询监��任务数据失败", e);
            throw new RuntimeException("聚合查询失败: " + e.getMessage());
        }
    }

    @Override
    public PageVo<MonitorTaskDataVo> pageTasks(MonitorTaskDataReq req) {
        PageVo<MonitorTaskDataVo> pageList = PageHelperUtil.startPage(req, () -> monitorTaskDataMapper.selectPageList(req));
        return new PageVo<>(req.getPageNo(), req.getPageSize(), pageList.getTotal(), pageList.getList());
    }

}
