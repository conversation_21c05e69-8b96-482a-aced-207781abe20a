package com.espc.sec.dataexport.starrocks.service;


import com.espc.sec.dataexport.common.constant.Constants;
import com.espc.sec.dataexport.common.util.DateUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 动态数据服务
 * 支持任意表的数据查询和处理，不依赖特定的实体类
 */
@Slf4j
@Service
public class StarRocksHelper {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 将ResultSet映射为Map
     */
    private Map<String, Object> mapResultSetToMap(ResultSet rs) throws SQLException {
        Map<String, Object> row = new LinkedHashMap<>();
        ResultSetMetaData metaData = rs.getMetaData();
        int columnCount = metaData.getColumnCount();

        for (int i = 1; i <= columnCount; i++) {
            String columnName = metaData.getColumnName(i);
            Object value = rs.getObject(i);

            // 处理JSON字段
            if (value instanceof String && isJsonField(columnName)) {
                try {
                    // 尝试解析JSON
                    String jsonStr = (String) value;
                    if (jsonStr.trim().startsWith("[") || jsonStr.trim().startsWith("{")) {
                        value = objectMapper.readValue(jsonStr, Object.class);
                    }
                } catch (Exception e) {
                    // 如果解析失败，保持原始字符串
                    log.debug("JSON字段解析失败，保持原始值: column={}, value={}", columnName, value);
                }
            }

            row.put(columnName, value);
        }

        return row;
    }

    /**
     * 判断是否为JSON字段
     */
    private boolean isJsonField(String columnName) {
        // 根据字段名判断是否可能是JSON字段
        return columnName.contains("_info") ||
                columnName.contains("_statistic") ||
                columnName.contains("_config") ||
                columnName.contains("_detail") ||
                columnName.equals("disk_info") ||
                columnName.equals("monitor_traffic_statistic");
    }


    public List<Map<String, Object>> queryDataForTable(String tableName, String timeField, Date startTime,
                                                       Date endTime, int offset, int pageSize) {
        String sql = String.format(
                "SELECT * FROM %s WHERE %s >= ? AND %s < ? ORDER BY %s ASC LIMIT ? OFFSET ?",
                tableName, timeField, timeField, timeField
        );

        try {
            return jdbcTemplate.query(sql
                    , (rs, rowNum) -> mapResultSetToMap(rs)
                    , DateUtil.format(startTime, Constants.YYYY_MM_DD_HH_MM_SS)
                    , DateUtil.format(endTime, Constants.YYYY_MM_DD_HH_MM_SS)
                    , pageSize
                    , offset);
        } catch (Exception e) {
            log.error("动态查询数据失败: startTime={}, endTime={}, offset={}, limit={}",
                    startTime, endTime, offset, pageSize, e);
            throw new RuntimeException("查询数据失败", e);
        }
    }

    /**
     * 生成INSERT SQL语句
     *
     * @param tableName 表名
     * @param data      数据Map
     * @return INSERT SQL语句
     */
    public String generateInsertSql(String tableName, Map<String, Object> data) {
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("数据不能为空");
        }

        StringBuilder columnsSql = new StringBuilder();
        StringBuilder valuesSql = new StringBuilder();

        columnsSql.append("INSERT INTO ").append(tableName).append(" (");

        boolean first = true;
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            String columnName = entry.getKey();
            Object value = entry.getValue();

            if (!first) {
                columnsSql.append(", ");
                valuesSql.append(", ");
            }

            columnsSql.append(columnName);
            String sqlValue = convertToSqlValue(columnName, value);
            valuesSql.append(sqlValue);

            first = false;
        }

        columnsSql.append(") VALUES (");
        columnsSql.append(valuesSql);
        columnsSql.append(")");

        return columnsSql.toString();
    }

    /**
     * 将值转换为SQL值
     */
    private String convertToSqlValue(String columnName, Object value) {
        if (value == null) {
            // 对于数值类型的字段，null值转换为0
            if (isNumericColumn(columnName)) {
                return "0";
            }
            return "NULL";
        }

        if (columnName.contains("time") && value instanceof Number) {
            return value.toString();
        }

        if (value instanceof Number) {
            return value.toString();
        }

        if (value instanceof Boolean) {
            return ((Boolean) value) ? "1" : "0";
        }

        // 处理复杂对象（JSON序列化）
        if (needJsonSerialization(columnName, value)) {
            return convertJsonField(value);
        }

        // 其他类型用sqlValue处理
        return sqlValue(value);
    }

    /**
     * 判断是否为数值类型的列
     */
    private boolean isNumericColumn(String columnName) {
        String lowerName = columnName.toLowerCase();
        return lowerName.contains("_used") ||
                lowerName.contains("_size") ||
                lowerName.contains("_speed") ||
                lowerName.contains("_count") ||
                lowerName.contains("_total") ||
                lowerName.equals("cpu_used") ||
                lowerName.equals("mem_used") ||
                lowerName.equals("trans_traffic_size") ||
                lowerName.equals("trans_traffic_speed");
    }

    /**
     * 判断是否需要JSON序列化
     */
    private boolean needJsonSerialization(String columnName, Object value) {
        if (value == null) return false;

        // 如果已经是字符串且看起来像JSON，直接���用
        if (value instanceof String) {
            String str = (String) value;
            return str.trim().startsWith("{") || str.trim().startsWith("[");
        }

        // 根据字段名判断
        return columnName.contains("_info") ||
                columnName.contains("_statistic") ||
                columnName.contains("_config") ||
                columnName.contains("_detail") ||
                columnName.equals("disk_info") ||
                columnName.equals("monitor_traffic_statistic");
    }

    /**
     * 转换JSON字段
     */
    private String convertJsonField(Object value) {
        try {
            if (value == null) {
                return "NULL";
            }

            // 如果已经是字符串，直接使用
            if (value instanceof String) {
                return sqlValue(value);
            }

            // 序列化为JSON字符串
            String json = objectMapper.writeValueAsString(value);
            return sqlValue(json);
        } catch (Exception e) {
            log.warn("序列化JSON字段失败: column={}, value={}", value, e);
            return "NULL";
        }
    }

    /**
     * SQL值处理（添加引号和转义）
     */
    private String sqlValue(Object value) {
        if (value == null) {
            return "NULL";
        }

        String str = value.toString();
        // 转义单引号和反斜杠
        str = str.replace("\\", "\\\\").replace("'", "''");
        return "'" + str + "'";
    }
}