#!/bin/bash
#set -x
# set -e
echo "使用该脚本之前需要设置以下参数：组名称，cpu占比，"
## 参数1为 cgroup 组名称，如 hbaseServiceGroup，必须设置
groupname=DataBusServer
## 参数2为cpu可以使用最大占比范围为 1-100整形,比如 5，25，可不设置
cpuPercent=20
## 参数3为可以使用最大内存，单位 MB，可不设置
maxMemory=16384
## 参数4为设备名 如：/dev/sda  /dev/sdb(注意不是分区 /dev/sdb1和磁盘挂载目录/data/dn1)，可不设置
devName=
## 参数5为设备最大写速率，单位为 MB，跟devName 一同设置
io_write_limit=
## 参数6为设备最大读速率，单位为 MB，跟devName 一同设置
io_read_limit=
## 参数7为程序启动命令,默认都是调用 nohup sh start.sh >/dev/null &
startProgram="nohup sh start.sh >/dev/null 2>&1 &"

if [ -z "$groupname" ]
then
	echo "请您输入cgrou 组名称"
	exit;
fi
##创建 程序组（/sys/fs/cgroup）
cgcreate -g cpu,memory,blkio:$groupname
## 管控 CPU
if [ -n "$cpuPercent" ]
then
	if [ $cpuPercent -lt 0 -o $cpuPercent -gt 99 ] 
	then
	    echo "参数2 cpu 占比输入范围有误，正确应该在（0-100）开区间"
	    exit;
	fi
	cpuNumer=`cat /proc/cpuinfo |grep "physical id"|wc -l`
	##设置CPU 占比
	cpuquota=$(($cpuNumer * $cpuPercent * 1000 ))

	## cfs_period_us 单个核的周期；cfs_quota_us 单个核的周期内可以使用的时间
	echo "cpu 总量为 100000*$cpuNumer， 该组最多可用占 $cpuquota "
	cgset -r cpu.cfs_period_us=100000 $groupname
	cgset -r cpu.cfs_quota_us=$cpuquota $groupname
fi
echo "CPU 管控设置成功..."
## 管控 内存
if [ -n "$maxMemory" ]
then
    if [ $maxMemory -lt 0 -o $maxMemory -gt 51200 ] 
	then
	    echo "参数3 内存输入的范围有误，正确应该在 0-51200 MB 之间"
	    exit;
	fi
	echo "开始设置memory,$groupname 组最多可用占 $maxMemory MB 内存，超过该内存会触发kill操作"
	maxMemoryByte=$(($maxMemory * 1024 * 1024))
	cgset -r memory.limit_in_bytes=$maxMemoryByte $groupname
fi
echo "内存管控设置成功..."
## 管控 磁盘IO
if [ -n "$devName" ]
then
	diskId=`ls -l $devName |awk '{print $5,$6}'|sed 's/ //g'|tr ',' ':'`
	echo "开始设置磁盘IO，$devName ($diskId)磁盘每秒写数据最大为 $io_write_limit MB and 最大读速率为 $io_read_limit MB"
	maxReadByte=$(($io_read_limit * 1024 * 1024))
	maxWriteByte=$(($io_write_limit * 1024 * 1024))
	cgset -r blkio.throttle.read_bps_device="$diskId $maxReadByte" $groupname
	cgset -r blkio.throttle.write_bps_device="$diskId $maxWriteByte" $groupname	    
fi
echo "磁盘IO管控设置成功，开始设置启动程序..."
cgexec -g cpu:$groupname -g blkio:$groupname -g memory:$groupname $startProgram
