package com.espc.sec.dataexport.common.config;

import lombok.Data;

public class Environment {
    public ElasticsearchDTO elasticsearch;

    public StarRocksDTO starRocks;

    public TidbDTO tidb;

    public MysqlDTO mysql;

    public KafkaDTO kafka;

    public MinioDTO minio;

    public HdfsDTO hdfs;

    @Data
    public static class ElasticsearchDTO {

        private String indexClusterName;

        private String indexClusterServer;

        private String indexClusterServerHttp;

        private String indexUserName;

        private String indexPassword;
    }

    @Data
    public static class StarRocksDTO {

        private String url;

        private String username;

        private String password;
    }

    @Data
    public static class TidbDTO {

        private String url;

        private String username;

        private String password;
    }

    @Data
    public static class MysqlDTO {

        private String url;

        private String username;

        private String password;
    }

    @Data
    public static class KafkaDTO {

        private String bootstrapServers;

        private String username;

        private String password;
    }

    @Data
    public static class MinioDTO {

        private String endPoint;

        private String username;

        private String password;
    }

    @Data
    public static class HdfsDTO {
        private String hdfsUri;
    }
}
