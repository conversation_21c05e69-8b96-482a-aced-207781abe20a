package com.espc.sec.dataimport.common.loader;


import com.espc.sec.dataimport.common.config.Config;
import com.espc.sec.dataimport.common.config.Environment;
import com.espc.sec.dataimport.common.config.ImportConfig;
import com.espc.sec.dataimport.common.util.JsonUtil;
import com.espc.sec.dataimport.elasticsearch.config.EsClientHolder;
import lombok.extern.slf4j.Slf4j;
import lombok.var;

import java.io.File;
import java.io.IOException;

/**
 * 本地配置加载器
 * 负责从environment.json文件加载配置信息
 * 替代Spring Boot的配置加载机制
 */
@Slf4j
public class LocalConfigLoader {

    public static void loadConfig() {
        try {
            parseConfigNew();
            validateConfiguration();
            //初始化 es集群连接信息
            EsClientHolder.init();

            log.info("Configuration loaded successfully from environment.json");
        } catch (Exception e) {
            log.error("loadConfig failed.", e);
            System.exit(-1);
        }
    }

    public static void parseConfigNew() {
        File env = new File("conf/DataImportServer/conf/environment.json");
        File common = new File("conf/DataImportServer/conf/config-common.json");
        File export = new File("conf/DataImportServer/conf/config-import.json");
        try {
            Config.environment = JsonUtil.jsonToObject(env, Environment.class);
            Config.commonConfig = JsonUtil.jsonToObject(common, com.espc.sec.dataimport.common.config.CommonConfig.class);
            Config.importConfig = JsonUtil.jsonToObject(export, ImportConfig.class);
        } catch (IOException e) {
            log.error("解析配置失败", e);
        }
    }

    /**
     * 验证配置完整性
     */
    private static void validateConfiguration() {
        if (Config.environment == null) {
            throw new RuntimeException("Environment configuration is null");
        }

        // 验证StarRocks配置
        if (Config.importConfig.starRocks != null) {
            var srExportConfig = Config.commonConfig.getImportPath();
            if (srExportConfig != null) {
                if (Config.commonConfig.getImportPath() == null || Config.commonConfig.getImportPath().isEmpty()) {
                    log.warn("StarRocks output path configuration is missing");
                }
            }
        }

        // 验证数据源配置
        if (Config.environment.mysql != null) {
            var dsConfig = Config.environment.mysql;
            if (dsConfig.getUrl() == null || dsConfig.getUrl().isEmpty()) {
                log.warn("Database URL configuration is missing");
            }
        }

        log.info("Configuration validation completed");
    }
}
