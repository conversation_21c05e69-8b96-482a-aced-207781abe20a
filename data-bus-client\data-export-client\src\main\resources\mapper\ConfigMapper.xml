<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.espc.sec.dataexport.config.mapper.ConfigMapper">
    <resultMap id="BaseResultMap" type="com.espc.sec.dataexport.config.vo.ConfigVo">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="module" property="module" jdbcType="VARCHAR"/>
        <result column="t_type" property="tType" jdbcType="VARCHAR"/>
        <result column="t_key" property="tKey" jdbcType="VARCHAR"/>
        <result column="t_value" property="tValue" jdbcType="VARCHAR"/>
        <result column="t_config" property="tConfig" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , module, t_type, t_key, t_value, t_config
    </sql>


    <select id="getConfig" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM `config`
        WHERE `module`=#{module}
        AND t_type = #{tType}
        AND t_key = #{tKey}
    </select>

    <insert id="save">
        INSERT INTO `config` (module, t_type, t_key, t_value)
        VALUES (#{module,jdbcType=VARCHAR},
                #{tType,jdbcType=INTEGER},
                #{tKey,jdbcType=VARCHAR},
                #{tValue,jdbcType=VARCHAR})
    </insert>

    <update id="updateValue">
        UPDATE `config`
        SET t_value = #{tValue}
        WHERE id = #{id}
    </update>
</mapper>