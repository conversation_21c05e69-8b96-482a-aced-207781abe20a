package com.espc.sec.flow.proxy;

import com.espc.sec.flow.aspect.IStat;
import com.espc.sec.flow.aspect.impl.StatImpl;
import com.espc.sec.flow.bean.Job;
import com.espc.sec.flow.util.FileUtil;
import com.espc.sec.flow.util.IoUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019-04-19
 */
@Data
@Slf4j
public class ThisMachineProxy extends AbstractProxy {

    private boolean isMove;
    private String target;
    private boolean needSpecialFile;

    public ThisMachineProxy(String target, Set<String> dirs, Set<String> nodes, Set<String> types,
                            String moveType, boolean needSpecialFile) {
        super(dirs, nodes, types);
        this.target = target;
        this.needSpecialFile = needSpecialFile;
        if ("move".equalsIgnoreCase(moveType)) {
            this.isMove = true;
        } else {
            this.isMove = false;
        }
    }

    @Override
    public boolean flowOneFile(Job job) {
        long start = System.currentTimeMillis();
        if (!needFlow(job, needSpecialFile)) {
            log.debug(job.getAbsolutePath() + " need not flow in ThisMachineProxy.");
            return true;
        }
        File file = job.getFile();
        File target = new File(getTarget(), job.getFilePath());
        if (target.exists()) {
            FileUtils.deleteQuietly(target);
        }
        if (isMove) {
            try {
                FileUtil.moveFile(file, target);
            } catch (Exception e) {
                log.error("move to dest dir failed. file:" + file.getAbsolutePath(), e);
                FileUtils.deleteQuietly(file);
                return false;
            }
        } else {
            try {
                IoUtil.writeBinaryToFile(job.getContent(), target);
            } catch (IOException e) {
                log.error("copy to dest dir failed. file:" + file.getAbsolutePath(), e);
                return false;
            }
        }

        String fileName = job.getFileName();
        String[] names = fileName.split("_");
        if (names.length == standardFileNameLen) {
            String node = names[1];
            String type = names[2];
            StatImpl.statOneFile(IStat.THIS_MACHINE_STAT_MAP, node, type, job.getContent().length);
        }
        log.info("this machine flow one success. name:" + fileName +
                ", cost:" + (System.currentTimeMillis() - start));
        return true;
    }
}
