package com.espc.sec.dataexport.netty.handler;

import com.espc.sec.dataexport.common.enums.OperateTypeEnum;
import com.espc.sec.dataexport.netty.entity.MessageTypeEnum;
import com.espc.sec.dataexport.netty.entity.MessageWrapper;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/16
 **/
public class MessageDecoder extends ByteToMessageDecoder {

    // 协议头固定长度
    private static final int BASE_LENGTH = 8;

    @Override
    protected void decode(ChannelHandlerContext ctx, ByteBuf in, List<Object> out) {
        // 可读数据小于协议头长度
        if (in.readableBytes() < BASE_LENGTH) {
            return;
        }
        // 标记当前读取位置
        in.markReaderIndex();

        // 消息类型
        int messageTypeCode = in.readInt();
        int operateTypeCode = in.readInt();
        // 消息长度
        int length = in.readInt();
        // 数据长度非法处理
        if (length < 0) {
            ctx.close();
            return;
        }
        // 等待完整数据包到达
        if (in.readableBytes() < length) {
            in.resetReaderIndex();
            return;
        }
        // 消息体
        byte[] body = new byte[length];
        in.readBytes(body);

        // 构建消息对象
        MessageWrapper messageWrapper = new MessageWrapper();
        messageWrapper.setType(MessageTypeEnum.getMessageTypeEnum(messageTypeCode));
        messageWrapper.setOperateType(OperateTypeEnum.values()[operateTypeCode]);
        messageWrapper.setBody(body);

        out.add(messageWrapper);
    }
}
