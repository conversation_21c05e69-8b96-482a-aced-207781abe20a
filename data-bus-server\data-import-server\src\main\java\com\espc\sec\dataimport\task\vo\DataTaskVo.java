package com.espc.sec.dataimport.task.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 数据任务VO
 */
@Data
public class DataTaskVo {
    /**
     * 任务ID
     */
    private Integer id;

    /**
     * job名称
     */
    private String name;

    /**
     * 任务类型 1-立即执行，2-定时执行
     */
    private Integer type;

    /**
     * cron表达式
     */
    private String cron;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 任务配置信息
     */
    private Object businessConfig;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 状态 1-待执行，2-执行中，3-执行成功，4-执行失败
     */
    private Integer status;

    /**
     * 节点
     */
    private String node;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date updateTime;

    /**
     * 预留字段
     */
    private String extraFiled;
}
